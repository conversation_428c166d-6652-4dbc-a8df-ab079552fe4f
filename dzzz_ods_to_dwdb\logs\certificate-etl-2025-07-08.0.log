2025-07-08 09:35:37.752 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 11876 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-07-08 09:35:37.754 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-07-08 09:35:39.685 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-08 09:35:40.365 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-08 09:35:40.365 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 09:35:40.395 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-08 09:35:40.396 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 09:35:40.655 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 09:35:40.658 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 09:35:40.678 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 09:35:41.162 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-08 09:35:41.371 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-08 09:35:41.429 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 09:35:41.617 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-08 09:35:45.881 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-07-08 09:35:46.253 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-07-08 09:35:46.253 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-07-08 09:35:57.103 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-07-08 09:35:57.400 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-07-08 09:35:57.401 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-07-08 09:35:58.008 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-07-08 09:35:58.009 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-07-08 09:35:58.009 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-07-08 09:35:58.009 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 09:35:58.009 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 09:35:58.427 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 09:35:58.428 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-07-08 09:35:58.429 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-08 09:36:01.593 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 3584毫秒
2025-07-08 09:36:01.594 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-07-08 09:36:02.820 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 25.802 seconds (JVM running for 28.07)
2025-07-08 09:36:03.854 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 09:36:03.854 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 09:36:04.312 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 458毫秒
2025-07-08 09:36:04.617 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-08 09:36:05.549 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-07-08 09:36:05.550 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-08 09:36:06.574 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-07-08 15:07:22.427 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 23128 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-07-08 15:07:22.430 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-07-08 15:07:24.388 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-08 15:07:25.056 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-08 15:07:25.057 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 15:07:25.085 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-08 15:07:25.085 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-08 15:07:25.271 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 15:07:25.274 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 15:07:25.293 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-08 15:07:25.776 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-08 15:07:25.920 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-08 15:07:25.974 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 15:07:26.064 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-08 15:07:29.417 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-07-08 15:07:29.876 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-07-08 15:07:29.877 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-07-08 15:07:41.140 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-07-08 15:07:41.500 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-07-08 15:07:41.501 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-07-08 15:07:41.922 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-07-08 15:07:41.922 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-07-08 15:07:41.923 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-07-08 15:07:41.923 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:07:41.923 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:07:42.470 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-08 15:07:42.471 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-07-08 15:07:42.472 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-08 15:07:45.181 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 3258毫秒
2025-07-08 15:07:45.181 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-07-08 15:07:46.311 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 24.57 seconds (JVM running for 26.8)
2025-07-08 15:07:47.348 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-08 15:07:47.348 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-08 15:07:48.203 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 855毫秒
2025-07-08 15:07:48.559 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-08 15:07:48.574 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-07-08 15:07:48.575 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-08 15:07:50.266 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
