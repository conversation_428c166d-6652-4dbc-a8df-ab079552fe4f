2025-07-31 12:11:55.437 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Starting CertificateEtlServiceIntegrationTest using Java 1.8.0_431 on DESKTOP-HIR03GC with PID 13800 (started by deLl in E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb)
2025-07-31 12:11:55.444 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - The following 1 profile is active: "test"
2025-07-31 12:11:58.907 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.aggregate.OdsCertificateDataMapper.getCurrentTime] is ignored, because it exists, maybe from xml file
2025-07-31 12:11:59.757 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.CertQueryOrg".
2025-07-31 12:11:59.759 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.CertQueryOrg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-31 12:11:59.835 [main] WARN  c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.example.certificate.entity.standard.DataReceptionTask".
2025-07-31 12:11:59.835 [main] WARN  c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.example.certificate.entity.standard.DataReceptionTask ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-31 12:12:00.222 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzRangeByDataId] is ignored, because it exists, maybe from xml file
2025-07-31 12:12:00.223 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteYhysrzByDataId] is ignored, because it exists, maybe from xml file
2025-07-31 12:12:00.247 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.example.certificate.mapper.standard.DwdbCertificateDataMapper.deleteGwccyByDataId] is ignored, because it exists, maybe from xml file
2025-07-31 12:12:01.066 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter初始化完成，缓存对象注入成功
2025-07-31 12:12:01.320 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始初始化基础参数表缓存...
2025-07-31 12:12:01.392 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-31 12:12:01.626 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-31 12:12:05.982 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 证书类型目录缓存加载完成，共 35 条记录
2025-07-31 12:12:06.816 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 从数据库加载机构映射数据，共 605 条记录
2025-07-31 12:12:06.816 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构映射缓存加载完成，共 604 条记录
2025-07-31 12:12:23.443 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 部门信息缓存加载完成，共 8275 条记录
2025-07-31 12:12:24.037 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 机构查询辅助表缓存加载完成，共 490 条记录
2025-07-31 12:12:24.037 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始加载职务映射表...
2025-07-31 12:12:24.857 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 职务映射表加载完成，共加载 527 条记录
2025-07-31 12:12:24.857 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 基础参数表缓存初始化完成
2025-07-31 12:12:24.857 [main] INFO  com.example.certificate.util.CertificateConverter - CertificateConverter手动设置缓存完成，缓存大小: certType=35, orgMapping=604, deptInfo=8275
2025-07-31 12:12:24.857 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-31 12:12:24.858 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-31 12:12:25.451 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Starting...
2025-07-31 12:12:25.452 [main] ERROR com.zaxxer.hikari.pool.PoolBase - StandardHikariCP - JMX name (StandardHikariCP) is already registered.
2025-07-31 12:12:25.452 [main] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Start completed.
2025-07-31 12:12:28.451 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 3593毫秒
2025-07-31 12:12:28.452 [main] INFO  com.example.certificate.service.CertificateService - 证照数据ETL测试结果: 
2025-07-31 12:12:30.079 [main] INFO  c.e.c.service.CertificateEtlServiceIntegrationTest - Started CertificateEtlServiceIntegrationTest in 35.605 seconds (JVM running for 38.403)
2025-07-31 12:12:31.660 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始执行证照数据ETL任务, taskName: ods_certificate_data
2025-07-31 12:12:31.661 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - 开始处理重处理任务
2025-07-31 12:12:32.243 [main] INFO  c.e.c.service.impl.CertificateEtlServiceImpl - , 耗时: 582毫秒
2025-07-31 12:12:32.572 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-31 12:12:33.868 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
2025-07-31 12:12:33.869 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown initiated...
2025-07-31 12:12:34.511 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - StandardHikariCP - Shutdown completed.
