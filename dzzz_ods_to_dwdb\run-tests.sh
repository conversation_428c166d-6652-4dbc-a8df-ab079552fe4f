#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}开始执行证书ETL测试用例...${NC}"

# 切换到项目根目录
cd "$(dirname "$0")"

# 清理并编译项目
echo -e "${GREEN}清理并编译项目...${NC}"
mvn clean compile

# 运行测试
echo -e "${GREEN}运行单元测试...${NC}"
mvn test

# 检查测试结果
if [ $? -eq 0 ]; then
    echo -e "${GREEN}所有测试通过!${NC}"
else
    echo -e "${RED}测试失败，请检查日志!${NC}"
    exit 1
fi

# 显示测试覆盖率报告
echo -e "${GREEN}生成测试覆盖率报告...${NC}"
mvn jacoco:report

echo -e "${GREEN}测试完成，可以在 target/site/jacoco/index.html 查看覆盖率报告${NC}" 