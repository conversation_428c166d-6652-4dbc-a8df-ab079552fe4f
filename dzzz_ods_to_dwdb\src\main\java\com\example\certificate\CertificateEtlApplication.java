package com.example.certificate;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import java.io.File;

@SpringBootApplication
@EnableScheduling
public class CertificateEtlApplication {

    public static void main(String[] args) {
        // 确保日志目录存在
        File logDir = new File("logs");
        if (!logDir.exists()) {
            logDir.mkdirs();
        }
        
        SpringApplication.run(CertificateEtlApplication.class, args);
    }
} 