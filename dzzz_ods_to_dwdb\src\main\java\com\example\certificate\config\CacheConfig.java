package com.example.certificate.config;

import com.example.certificate.entity.standard.CertTypeDirectory;
import com.example.certificate.entity.standard.CtfSysDept;
import com.example.certificate.entity.standard.DictYthOrgMapping;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class CacheConfig {

    @Bean("certTypeCache")
    public Map<String, CertTypeDirectory> certTypeCache() {
        return new HashMap<>();
    }
    
    @Bean("orgMappingCache")
    public Map<String, DictYthOrgMapping> orgMappingCache() {
        return new HashMap<>();
    }
    
    @Bean("deptInfoCache")
    public Map<String, CtfSysDept> deptInfoCache() {
        return new HashMap<>();
    }

    @Bean("capMappingCache")
    public Map<String, String> capMappingCache() {
        return new HashMap<>();
    }
} 