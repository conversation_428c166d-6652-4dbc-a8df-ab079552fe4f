package com.example.certificate.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.example.certificate.mapper.aggregate", sqlSessionFactoryRef = "aggregateSqlSessionFactory")
@MapperScan(basePackages = "com.example.certificate.mapper.standard", sqlSessionFactoryRef = "standardSqlSessionFactory")
public class DataSourceConfig {

    @Primary
    @Bean(name = "aggregateDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.aggregate")
    public DataSource aggregateDataSource() {
        return new HikariDataSource();
    }

    @Bean(name = "standardDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.standard")
    public DataSource standardDataSource() {
        return new HikariDataSource();
    }

    @Bean(name = "aggregateSqlSessionFactory")
    @Primary
    public SqlSessionFactory aggregateSqlSessionFactory(@Qualifier("aggregateDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath:mapper/aggregate/*.xml"));
        return sqlSessionFactory.getObject();
    }

    @Bean(name = "standardSqlSessionFactory")
    public SqlSessionFactory standardSqlSessionFactory(@Qualifier("standardDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath:mapper/standard/*.xml"));
        return sqlSessionFactory.getObject();
    }
} 