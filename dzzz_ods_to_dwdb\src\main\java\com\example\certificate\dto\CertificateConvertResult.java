package com.example.certificate.dto;

import lombok.Data;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.example.certificate.entity.standard.DwdbCertificateDataAttribute;

/**
 * 证书转换结果类
 * 用于存储证书转换的结果，包括主表数据和子表数据
 */
@Data
public class CertificateConvertResult {
    
    /**
     * 数据ID
     */
    private String dataId;
    
    /**
     * 证书类型
     */
    private String certificateType;
    
    /**
     * 主表数据
     */
    private Object mainTableData;
    
    /**
     * 子表数据列表，key为子表名称，value为子表数据列表
     */
    private Map<String, List<?>> subTableDataMap = new HashMap<>();
    
    /**
     * 是否有错误
     */
    private boolean hasError;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 未被使用的属性列表
     */
    private List<DwdbCertificateDataAttribute> unusedAttributes = new ArrayList<>();
    
    /**
     * 设置子表数据列表
     * @param key 子表名称
     * @param dataList 子表数据列表
     */
    public void setSubTableDataList(String key, List<?> dataList) {
        this.subTableDataMap.put(key, dataList);
    }
    
    /**
     * 设置子表数据列表
     * @param key 子表名称
     * @param data 子表数据对象
     */
    public void setSubTableDataList(String key, Object data) {
        if (data instanceof List) {
            this.subTableDataMap.put(key, (List<?>) data);
        }
    }
    
    /**
     * 获取指定类型的子表数据列表
     * @param key 子表类型标识
     * @return 子表数据列表
     */
    public List<?> getSubTableDataList(String key) {
        return subTableDataMap.getOrDefault(key, new ArrayList<>());
    }
    
    /**
     * 判断是否包含指定类型的子表数据
     * @param key 子表类型标识
     * @return 是否包含
     */
    public boolean hasSubTableData(String key) {
        return subTableDataMap.containsKey(key) && 
               subTableDataMap.get(key) != null && 
               !subTableDataMap.get(key).isEmpty();
    }
    
    /**
     * 设置未使用的属性列表
     * @param attributes 未使用的属性列表
     */
    public void setUnusedAttributes(List<DwdbCertificateDataAttribute> attributes) {
        this.unusedAttributes = attributes;
    }
    
    /**
     * 获取未使用的属性列表
     * @return 未使用的属性列表
     */
    public List<DwdbCertificateDataAttribute> getUnusedAttributes() {
        return this.unusedAttributes;
    }
    
    /**
     * 判断是否有未使用的属性
     * @return 是否有未使用的属性
     */
    public boolean hasUnusedAttributes() {
        return unusedAttributes != null && !unusedAttributes.isEmpty();
    }
} 