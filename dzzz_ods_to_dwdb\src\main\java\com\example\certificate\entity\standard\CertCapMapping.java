package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.util.Date;

/**
 * 职务映射表实体类
 */
@Data
@TableName("dwdb_ctf_cert_cap_mapping")
public class CertCapMapping {
    
    @TableId("CAP_MAPPING_ID")
    private String capMappingId;      // 主键
    
    @TableField("SOURCE_CAP_NAME")
    private String sourceCapName;     // 源职务名称
    
    @TableField("DEST_CAP_NAME")
    private String destCapName;       // 目标职务名称
    
    @TableField("CREATE_OPER_ID")
    private String createOperId;      // 创建人
    
    @TableField("CREATE_DATE")
    private Date createDate;          // 创建时间
    
    @TableField("MODIFY_OPER_ID")
    private String modifyOperId;      // 修改人
    
    @TableField("MODIFY_DATE")
    private Date modifyDate;          // 修改时间
    
    @TableField("DEL_FLAG")
    private String delFlag;           // 逻辑删除标记(0--正常 1--删除)
    
    @TableField("rec_create_date")
    private Date recCreateDate;       // 记录创建日期
    
    @TableField("rec_modify_date")
    private Date recModifyDate;       // 记录修改日期
} 