package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("cert_type_directory")
public class CertTypeDirectory {
    
    @TableId
    private String certTypeDirId;
    
    private String certificateTypeCode;
    
    private String certificateTypeName;
    
    private String parentId;
    
    private Integer certLevel;
    
    private Integer sort;
    
    private String certificateDefineAuthorityName;
    
    private String certificateDefineAuthorityCode;
    
    private String certificateDefineAuthorityLevel;
    
    private String relatedItemName;
    
    private String relatedItemId;
    
    private String relatedItemCode;
    
    private String certificateHolderCategory;
    
    private String validityRange;
    
    private String certificateIssuingAuthorityLevel;
    
    private String createOrgCode;
    
    private String createOrgName;
    
    private String approvalStatus;
    
    private String issueStatus;
    
    private Date issueDate;

    private String catalogId;
    
    private Date createTime;
    
    private String createBy;
    
    private Date updateTime;
    
    private String updateBy;
    
    private String delFlag;
} 