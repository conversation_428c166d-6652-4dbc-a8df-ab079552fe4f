package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;

@Data
@TableName("ctf_sys_dept")
public class CtfSysDept {
    
    @TableId
    private String deptId;
    
    private String name;
    
    private BigDecimal sort;
    
    private Date createTime;
    
    private Date updateTime;
    
    private String delFlag;
    
    private String parentId;
    
    private String govLevel;
    
    private String code;
    
    private String isShow;
    
    private String hallShow;
    
    private String creditCode;
    
    private String handleDate;
    
    private String approveAddress;
    
    private String consultType;
    
    private String superviseType;
    
    private String isCenter;
    
    private String deptPhone;
    
    private String taskCode;
    
    private String parentCode;
    
    private String deptType;
    
    private String nameAbbr;
    
    private String codesetId;
    
    private String orgTag;
    
    private String orgCcname;
    
    private String orgDesc;
    
    private String orgCertifit;
    
    private String zsOrgType;
    
    private String oldDeptId;
    
    private String areaId;
    
    private String grade;
    
    private String uscc;
} 