package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

@Data
@TableName("dict_yth_org_mapping")
public class DictYthOrgMapping {
    
    @TableId
    private String id;
    
    private String srcOrgCode;
    
    private String srcOrgName;
    
    private String orgCode;
    
    private String orgName;
    
    private String remark;
    
    private String sourceTypeCode;
    
    private String sourceCode;
    
    private Date recCreateDate;
    
    private Date recModifyDate;
} 