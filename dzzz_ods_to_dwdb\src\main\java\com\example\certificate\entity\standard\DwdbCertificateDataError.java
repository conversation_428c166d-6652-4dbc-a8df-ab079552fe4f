package com.example.certificate.entity.standard;

import com.example.certificate.entity.BaseEntity;
import lombok.Data;
import java.util.Date;

/**
 * 证照数据处理错误记录
 */
@Data
public class DwdbCertificateDataError extends BaseEntity {
    
    /**
     * 错误记录ID
     */
    private String errorId;
    
    /**
     * 数据ID
     */
    private String dataId;
    
    /**
     * 表类型
     */
    private String tableType;
    
    /**
     * 错误时间
     */
    private Date errorTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 数据内容(JSON格式)
     */
    private String dataContent;
} 