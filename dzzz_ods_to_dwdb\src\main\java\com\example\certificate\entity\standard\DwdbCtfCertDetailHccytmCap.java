package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员特免证明职务等级信息表实体类
 */
@Data
public class DwdbCtfCertDetailHccytmCap {
    /**
     * 主键
     */
    private String hccytmCapacityId;

    /**
     * 证书主表ID
     */
    private String hccytmId;

    /**
     * 职务等级(中文)
     */
    private String capacity1;

    /**
     * 职务等级(英文)
     */
    private String capacity2;

    /**
     * 职务限制(中文)
     */
    private String alimitationsApplying1;

    /**
     * 职务限制(英文)
     */
    private String alimitationsApplying2;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}