package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员特免证明职能信息表实体类
 */
@Data
public class DwdbCtfCertDetailHccytmFunc {
    /**
     * 主键
     */
    private String hccytmFunctionId;

    /**
     * 证书主表ID
     */
    private String hccytmId;

    /**
     * 职能(中文)
     */
    private String function1;

    /**
     * 职能(英文)
     */
    private String function2;

    /**
     * 等级(中文)
     */
    private String level1;

    /**
     * 等级(英文)
     */
    private String level2;

    /**
     * 限制(中文)
     */
    private String limitationsApplying1;

    /**
     * 限制(英文)
     */
    private String limitationsApplying2;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}