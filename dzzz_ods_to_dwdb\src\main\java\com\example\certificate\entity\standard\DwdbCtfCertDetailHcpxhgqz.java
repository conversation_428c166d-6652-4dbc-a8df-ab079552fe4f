package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证承认签证照面信息表实体类
 */
@Data
public class DwdbCtfCertDetailHcpxhgqz {
    /**
     * 主键ID
     */
    private String hcpxhgqzId;

    /**
     * 证照数据主键
     */
    private String dataId;

    /**
     * 持证人姓名(中文)
     */
    private String fullNameOfTheHolder1;

    /**
     * 持证人姓名(英文)
     */
    private String fullNameOfTheHolder2;

    /**
     * 国籍(中文)
     */
    private String nationality1;

    /**
     * 国籍(英文)
     */
    private String nationality2;

    /**
     * 出生日期(中文格式)
     */
    private String dateOfBirth1;

    /**
     * 出生日期(英文格式)
     */
    private String dateOfBirth2;

    /**
     * 性别(中文)
     */
    private String gender1;

    /**
     * 性别(英文)
     */
    private String gender2;

    /**
     * 证书编号
     */
    private String certificateNo;

    /**
     * 证书到期日期(中文格式)
     */
    private String dateOfExpiry1;

    /**
     * 证书到期日期(英文格式)
     */
    private String dateOfExpiry2;

    /**
     * 签发时间(中文格式)
     */
    private String issuedOn1;

    /**
     * 签发时间(英文格式)
     */
    private String issuedOn2;

    /**
     * 条款编号1
     */
    private String articleNumber1;

    /**
     * 条款编号2
     */
    private String articleNumber2;

    /**
     * 条款编号3
     */
    private String articleNumber3;

    /**
     * 条款编号4
     */
    private String articleNumber4;

    /**
     * 照片信息
     */
    private String photo;

    /**
     * 授权官员姓名(中文)
     */
    private String nameOfDulyAuthorizedOfficial1;

    /**
     * 授权官员姓名(英文)
     */
    private String nameOfDulyAuthorizedOfficial2;

    /**
     * 授权机关(中文)
     */
    private String issuingAminstration1;

    /**
     * 授权机关(英文)
     */
    private String issuingAminstration2;

    /**
     * 官方使用(中文)
     */
    private String officialUseOnly1;

    /**
     * 官方使用(英文)
     */
    private String officialUseOnly2;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}