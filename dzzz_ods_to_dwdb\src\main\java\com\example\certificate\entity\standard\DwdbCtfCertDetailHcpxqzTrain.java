package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证承认签证培训项目信息表实体类
 */
@Data
public class DwdbCtfCertDetailHcpxqzTrain {
    /**
     * 主键
     */
    private String hcpxhgqzTrainingId;

    /**
     * 证书主表ID
     */
    private String hcpxhgqzId;

    /**
     * 培训项目名称(中文)
     */
    private String titleOfTheCertificate1;

    /**
     * 培训项目名称(英文)
     */
    private String titleOfTheCertificate2;

    /**
     * 适用公约条款
     */
    private String clause;

    /**
     * 缔约方合格证编号(中文格式)
     */
    private String certificateNo1;

    /**
     * 缔约方合格证编号(英文格式)
     */
    private String certificateNo2;

    /**
     * 培训到期日期(中文格式)
     */
    private String dateOfExpiry3;

    /**
     * 培训到期日期(英文格式)
     */
    private String dateOfExpiry4;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}