package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 不参加航行和轮机值班海船船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailBcjhljzb {
    private String bcjhljzbId;        // 主键ID
    private String dataId;            // 数据ID
    private String fullNameoftheHolder1;  // 持证人姓名(中文)
    private String fullNameoftheHolder2;  // 持证人姓名(英文)
    private String nationality1;      // 国籍(中文)
    private String nationality2;      // 国籍(英文)
    private String dateOfBirth1;      // 出生日期(中文)
    private String dateOfBirth2;      // 出生日期(英文)
    private String gender1;           // 性别(中文)
    private String gender2;           // 性别(英文)
    private String certificateNo;     // 证书编号
    private String certificateExpiringDate1;  // 证书有效期至(中文)
    private String certificateExpiringDate2;  // 证书有效期至(英文)
    private String certificateIssuedDate1;    // 发证日期(中文)
    private String certificateIssuedDate2;    // 发证日期(英文)
    private String certificateHolderName;     // 持证人姓名
    private String informationOfPhoto;        // 照片信息
    private String capacity1;         // 职务1(中文)
    private String capacity2;         // 职务1(英文)
    private String capacity3;         // 职务2(中文)
    private String capacity4;         // 职务2(英文)
    private String applivations1;     // 适用范围1(中文)
    private String applivations2;     // 适用范围1(英文)
    private String applivations3;     // 适用范围2(中文)
    private String applivations4;     // 适用范围2(英文)
    private String nameOfDulyAuthorizedOfficial1;  // 签发官员姓名(中文)
    private String nameOfDulyAuthorizedOfficial2;  // 签发官员姓名(英文)
    private String issuingAuthority1;     // 发证机关(中文)
    private String issuingAuthority2;     // 发证机关(英文)
    private String officialUseOnly1;      // 备注(中文)
    private String officialUseOnly2;      // 备注(英文)
    private Date createTime;          // 创建时间
    private Date updateTime;          // 更新时间
} 