package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailBcjhljzbCapacity {
    private String bcjhljzbCapacityId;  // 主键
    private String dataId;
    private String bcjhljzbId;          // 证书主表ID
    private String capacity1;            // 职务等级(中文)
    private String capacity2;            // 职务等级(英文)
    private String applivations1;        // 职务限制(中文)
    private String applivations2;        // 职务限制(英文)
    private Date createTime;             // 创建时间
    private Date updateTime;             // 更新时间
} 