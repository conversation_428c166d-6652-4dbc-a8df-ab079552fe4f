package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailCsssfzpx {
    private String csssfzpxId;               // 主键ID
    private String dataId;                   // 证照数据主键
    private String fullNameOfTheHolder1;     // 持证人姓名(中文)
    private String fullNameOfTheHolder2;     // 持证人姓名(英文)
    private String nationality1;             // 国籍(中文)
    private String nationality2;             // 国籍(英文)
    private String dateOfBirth1;             // 出生日期(中文格式)
    private String dateOfBirth2;             // 出生日期(英文格式)
    private String gender1;                  // 性别(中文)
    private String gender2;                  // 性别(英文)
    private String certificateNo;            // 证书编号
    private String dateOfIssue1;             // 签发日期(中文格式)
    private String dateOfIssue2;             // 签发日期(英文格式)
    private String nameOfTheTraingManager1;  // 培训主管姓名(中文)
    private String nameOfTheTraingManager2;  // 培训主管姓名(英文)
    private String issuingBody1;             // 培训机构(中文)
    private String issuingBody2;             // 培训机构(英文)
    private String informationOfPhoto;       // 照片信息
    private Date createTime;                 // 创建时间
    private Date updateTime;                 // 更新时间
    private String issuingBody;             // 培训机构
    private String certificateHolderName;   // 持证人姓名
} 