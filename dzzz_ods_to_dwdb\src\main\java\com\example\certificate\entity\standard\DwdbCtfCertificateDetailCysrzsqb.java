package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 船员适任证书申请表实体类
 */
@Data
public class DwdbCtfCertificateDetailCysrzsqb {
    private String cysrzsqbId;    // 主键ID
    private String dataId;        // 数据ID
    private String sqYear;        // 申请年
    private String sqMonth;       // 申请月
    private String sqDay;         // 申请日
    private String number;        // 编号
    private String name;          // 姓名
    private String py;            // 拼音
    private String sex;           // 性别
    private String idCode;        // 身份证号
    private String birthYear;     // 出生年
    private String birthMonth;    // 出生月
    private String birthDay;      // 出生日
    private String company;       // 公司
    private String education;     // 学历
    private String school;        // 学校
    private String special;       // 专业
    private String certNumber;    // 证书编号
    private String certDate;      // 证书日期
    private String photo;         // 照片
    private String gmdssJob;      // GMDSS职务
    private String gmdssNo;       // GMDSS编号
    private String gYear;         // GMDSS年
    private String gMonth;        // GMDSS月
    private String gDay;          // GMDSS日
    private String area2;         // 航区
    private String level2;        // 等级
    private String job2;          // 职务
    private String limitInfo;     // 限制信息
    private String hgzNo;         // 合格证编号
    private String year1;         // 年份
    private String month1;        // 月份
    private String day1;          // 日期
    private String signature;     // 签名
    private String seal;          // 印章
    private String link;          // 联系人
    private String tel;           // 电话
    
    private String certNo1;           // 
    private String sDay;           // 
    private String job1;           // 
    private String sYear;           // 
    private String level1;           // 
    private String sMonth;           // 
    private String area1;           //  
    
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间

    @TableField(exist = false)
    private List<DwdbCtfCertificateDetailCysrzsqbExperience> experienceList;

    @TableField(exist = false)
    private List<DwdbCtfCertificateDetailCysrzsqbOptions> optionsList;

    public List<DwdbCtfCertificateDetailCysrzsqbExperience> getExperienceList() {
        return experienceList;
    }

    public void setExperienceList(List<DwdbCtfCertificateDetailCysrzsqbExperience> experienceList) {
        this.experienceList = experienceList;
    }

    public List<DwdbCtfCertificateDetailCysrzsqbOptions> getOptionsList() {
        return optionsList;
    }

    public void setOptionsList(List<DwdbCtfCertificateDetailCysrzsqbOptions> optionsList) {
        this.optionsList = optionsList;
    }
} 