package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 船员适任证书申请表服务资历信息实体类
 */
@Data
public class DwdbCtfCertificateDetailCysrzsqbExperience {
    private String cysrzsqbExperienceId;  // 主键ID
    private String dataId;                // 关联的源数据ID
    private String cysrzsqbId;            // 关联的申请表ID
    private String jobs;                   // 职务
    private String shipName;               // 船名
    private String type;                   // 船舶类型
    private String class_;                 // 航区
    private String weight;                 // 吨位
    private String rzDate;                 // 任职日期
    private String lzDate;                 // 离职日期
    private Date createTime;               // 创建时间
    private Date updateTime;               // 更新时间
} 