package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 船员适任证书申请表选项信息实体类
 */
@Data
public class DwdbCtfCertificateDetailCysrzsqbOptions {
    private String cysrzsqbOptionsId;  // 主键ID
    private String dataId;             // 关联的源数据ID
    private String cysrzsqbId;         // 关联的申请表ID
    private String optionKey;          // 选项键
    private String optionValue;        // 选项值
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 