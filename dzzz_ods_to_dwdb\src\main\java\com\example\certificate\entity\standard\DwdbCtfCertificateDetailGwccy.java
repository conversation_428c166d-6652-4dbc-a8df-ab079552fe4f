package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 公务船船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailGwccy {
    private String gwccyId;           // 主键ID
    private String dataId;            // 数据ID
    private String fullNameoftheHolder1;  // 持证人姓名(中文)
    private String fullNameoftheHolder2;  // 持证人姓名(英文)
    private String nationality1;      // 国籍(中文)
    private String nationality2;      // 国籍(英文)
    private String dateOfBirth1;      // 出生日期(中文)
    private String dateOfBirth2;      // 出生日期(英文)
    private String gender1;           // 性别(中文)
    private String gender2;           // 性别(英文)
    private String certificateNo;     // 证书编号
    private String certificateExpiringDate1;  // 证书有效期至(中文)
    private String certificateExpiringDate2;  // 证书有效期至(英文)
    private String dateOfIssue1;      // 发证日期(中文)
    private String dateOfIssue2;      // 发证日期(英文)
    private String certificateHolderName;  // 持证人姓名
    private String informationOfPhoto;     // 照片信息    
    private String issuingAuthority1;      // 发证机关(中文)
    private String issuingAuthority2;      // 发证机关(英文)
    private String officialUseOnly1;       // 备注
    private String officialUseOnly2;       // 备注
    private Date createTime;          // 创建时间
    private Date updateTime;          // 更新时间
} 