package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailGwccyCapacity {
    private String gwccyCapacityId;  // 主键
    private String gwccyId;          // 证书主表ID
    private String dataId;                // 关联的源数据ID    
    private String gradwAndCapacity1; // 职务等级(中文)
    private String gradwAndCapacity2; // 职务等级(英文)
    private String alimitationsApplying1; // 职务限制(中文)
    private String alimitationsApplying2; // 职务限制(英文)
    private Date createTime;         // 创建时间
    private Date updateTime;         // 更新时间
} 