package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 海船高级船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailHcgjcy {
    private String hcgjcyId;          // 主键ID
    private String dataId;            // 数据ID
    private String fullNameOfTheHolder1;  // 持证人姓名(中文)
    private String fullNameOfTheHolder2;  // 持证人姓名(英文)
    private String nationality1;      // 国籍(中文)
    private String nationality2;      // 国籍(英文)
    private String dateOfBirth1;      // 出生日期(中文格式)
    private String dateOfBirth2;      // 出生日期(英文格式)
    private String gender1;           // 性别(中文)
    private String gender2;           // 性别(英文)
    private String certificateNo;     // 证书编号
    private String certificateExpiringDate1;  // 证书到期日期(中文格式)
    private String certificateExpiringDate2;  // 证书到期日期(英文格式)
    private String dateOfIssue1;      // 签发日期(中文格式)
    private String dateOfIssue2;      // 签发日期(英文格式)
    private String certificateHolderName;  // 持证人姓名
    private String articleNumber1;    // 条款编号1
    private String articleNumber2;    // 条款编号2
    private String articleNumber3;    // 条款编号3
    private String articleNumber4;    // 条款编号4
    private String informationOfPhoto;  // 照片信息
    private String nameOfDulyAuthorizedOfficial1;  // 授权官员姓名(中文)
    private String nameOfDulyAuthorizedOfficial2;  // 授权官员姓名(英文)
    private String issuingAuthority1;  // 发证机关(中文)
    private String issuingAuthority2;  // 发证机关(英文)
    private String officialUseOnly1;   // 官方使用(中文)
    private String officialUseOnly2;   // 官方使用(英文)
    
    // 新增字段
    private List<DwdbCtfCertificateDetailHcgjcyCapacity> capacities;  // 职务等级信息列表
    private List<DwdbCtfCertificateDetailHcgjcyFunction> functions;   // 职能信息列表
    
    private Date createTime;          // 创建时间
    private Date updateTime;          // 更新时间
} 