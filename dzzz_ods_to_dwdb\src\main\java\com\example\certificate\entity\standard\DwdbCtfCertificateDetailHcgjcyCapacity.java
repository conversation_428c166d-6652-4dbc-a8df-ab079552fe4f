package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船高级船员适任证书职务等级信息实体类
 */
@Data
public class DwdbCtfCertificateDetailHcgjcyCapacity {
    private String hcgjcyCapacityId;  // 主键ID
    private String dataId;            // 关联的源数据ID
    private String hcgjcyId;          // 关联的证书ID
    private String gradwAndCapacity1;  // 职务等级(中文)
    private String gradwAndCapacity2;  // 职务等级(英文)
    private String alimitationsApplying1;  // 职务限制(中文)
    private String alimitationsApplying2;  // 职务限制(英文)
    private Date createTime;          // 创建时间
    private Date updateTime;          // 更新时间
} 