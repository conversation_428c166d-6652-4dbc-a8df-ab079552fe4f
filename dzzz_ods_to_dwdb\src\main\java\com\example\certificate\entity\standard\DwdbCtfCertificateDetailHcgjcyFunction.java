package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船高级船员适任证书职能信息实体类
 */
@Data
public class DwdbCtfCertificateDetailHcgjcyFunction {
    private String hcgjcyFunctionId;  // 主键ID
    private String dataId;            // 关联的源数据ID
    private String hcgjcyId;          // 关联的证书ID
    private String function1;         // 职能(中文)
    private String function2;         // 职能(英文)
    private String level1;            // 等级(中文)
    private String level2;            // 等级(英文)
    private String limitationsApplying1;  // 适用限制(中文)
    private String limitationsApplying2;  // 适用限制(英文)
    private Date createTime;          // 创建时间
    private Date updateTime;          // 更新时间
} 