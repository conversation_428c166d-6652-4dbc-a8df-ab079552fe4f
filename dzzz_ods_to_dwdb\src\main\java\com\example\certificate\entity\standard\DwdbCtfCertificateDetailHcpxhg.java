package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证书主表实体类
 */
@Data
public class DwdbCtfCertificateDetailHcpxhg {
    /**
     * 主键ID
     */
    private String hcpxhgId;
    
    /**
     * 关联的证书数据ID
     */
    private String dataId;
    
    /**
     * 持证人姓名(中文)
     */
    private String fullNameOfTheHolder1;
    
    /**
     * 持证人姓名(英文)
     */
    private String fullNameOfTheHolder2;
    
    /**
     * 国籍(中文)
     */
    private String nationality1;
    
    /**
     * 国籍(英文)
     */
    private String nationality2;
    
    /**
     * 出生日期(中文格式)
     */
    private String dateOfBirth1;
    
    /**
     * 出生日期(英文格式)
     */
    private String dateOfBirth2;
    
    /**
     * 性别(中文)
     */
    private String gender1;
    
    /**
     * 性别(英文)
     */
    private String gender2;
    
    /**
     * 证书编号
     */
    private String certificateNo;
    
    /**
     * 签发时间(中文格式)
     */
    private String issuedOn1;
    
    /**
     * 签发时间(英文格式)
     */
    private String issuedOn2;
    
    /**
     * 持证人姓名
     */
    private String certificateHolderName;
    
    /**
     * 照片信息
     */
    private String informationOfPhoto;
    
    /**
     * 授权官员签名(中文)
     */
    private String signatureOfDulyAutOffi1;
    
    /**
     * 授权官员签名(英文)
     */
    private String signatureOfDulyAutOffi2;
    
    /**
     * 授权官员姓名(中文)
     */
    private String nameOfDulyAutOffi1;
    
    /**
     * 授权官员姓名(英文)
     */
    private String nameOfDulyAutOffi2;
    
    /**
     * 发证机关(中文)
     */
    private String officalSeal1;
    
    /**
     * 发证机关(英文)
     */
    private String officalSeal2;
    
    /**
     * 官方使用(中文)
     */
    private String officialUseOnly1;
    
    /**
     * 官方使用(英文)
     */
    private String officialUseOnly2;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 