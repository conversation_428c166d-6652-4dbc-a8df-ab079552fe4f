package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证书培训项目信息表实体类
 */
@Data
public class DwdbCtfCertificateDetailHcpxhgTraining {
    /**
     * 主键ID
     */
    private String hcpxhgTrainingId;
    
    /**
     * 关联的源数据ID
     */
    private String dataId;
    
    /**
     * 关联的证书主表ID
     */
    private String hcpxhgId;
    
    /**
     * 培训项目代码
     */
    private String prefix;
    
    /**
     * 培训项目名称(中文)
     */
    private String titleOfTheCertificate1;
    
    /**
     * 培训项目名称(英文)
     */
    private String titleOfTheCertificate2;
    
    /**
     * 培训等级代码
     */
    private String level;
    
    /**
     * 培训发证日期(中文格式)
     */
    private String dateOfIssue1;
    
    /**
     * 培训发证日期(英文格式)
     */
    private String dateOfIssue2;
    
    /**
     * 培训到期日期(中文格式)
     */
    private String dateOfExpiry1;
    
    /**
     * 培训到期日期(英文格式)
     */
    private String dateOfExpiry2;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 