package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海上非自航船舶船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailHsfhcysrz {
    private String hsfhcysrzId;        // 主键ID
    private String dataId;             // 数据ID
    private String certificateNo;      // 证书编号
    private String fullNameOfTheHolder;  // 持证人姓名
    private String dateOfBirth;        // 出生日期
    private String placeOfBirth;       // 出生地
    private String dateOfExpirty;      // 有效期至
    private String dateOfIssue;        // 签发日期
    private String certificateHolderName;  // 持证人姓名
    private String informationOfPhoto;  // 照片信息
    private String nameOfDulyAuthorizedOfficial;  // 授权官员姓名
    private String remark;             // 备注
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 