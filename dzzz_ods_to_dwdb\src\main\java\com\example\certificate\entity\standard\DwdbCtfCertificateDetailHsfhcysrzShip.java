package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海上非自航船舶船员适任证书船舶信息实体类
 */
@Data
public class DwdbCtfCertificateDetailHsfhcysrzShip {
    private String hsfhcysrzShipId;    // 主键ID
    private String dataId;             // 关联的源数据ID
    private String hsfhcysrzId;        // 关联的证书ID
    private String shipType;           // 船舶类型
    private String level;              // 等级
    private String capacity;           // 职务
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 