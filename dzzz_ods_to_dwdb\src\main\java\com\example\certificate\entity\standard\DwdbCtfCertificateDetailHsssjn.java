package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailHsssjn {
    private String hsssjnId;                 // 主键ID
    private String dataId;                   // 证照数据主键
    private String fullNameOfTheHolder1;     // 持证人姓名(中文)
    private String fullNameOfTheHolder2;     // 持证人姓名(英文)
    private String editBox1;                 // 身份证号码/护照号码
    private String nationality1;             // 国籍(中文)
    private String nationality2;             // 国籍(英文)
    private String dateOfBirth1;             // 出生日期(中文格式)
    private String dateOfBirth2;             // 出生日期(英文格式)
    private String gender1;                  // 性别(中文)
    private String gender2;                  // 性别(英文)
    private String certificateNo;            // 证书编号
    private String anThorityName1;           // 基本培训
    private String anThorityName2;           // 专业培训
    private String evaluationOrganization1;  // 单位名称
    private String informationOfPhoto;       // 照片信息
    private String year1;                    // 年
    private String month1;                   // 月
    private String day1;                     // 日
    private String year2;                    // 年
    private String month2;                   // 月
    private String day2;                     // 日
    private String IDNumber;                     // 身份证号码
    private String passportNumber;                     // 护照号码
    private Date createTime;                 // 创建时间
    private Date updateTime;                 // 更新时间
} 