package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海员外派机构资质证书实体类
 */
@Data
public class DwdbCtfCertificateDetailHywpjg {
    private String hywpjgId;           // 主键ID
    private String dataId;             // 数据ID
    private String permitNumber1;       // 许可证号
    private String permitNumber2;       // 许可证号(重复)
    private String anThorityName1;     // 机构名称(中文)
    private String anThorityName2;     // 机构名称(英文)
    private String anThorityName3;     // 机构名称(中文重复)
    private String anThorityName4;     // 机构名称(英文重复)
    private String address1;           // 地址(中文)
    private String address2;           // 地址(英文)
    private String address3;           // 地址(中文重复)
    private String address4;           // 地址(英文重复)
    private String representative1;    // 法定代表人(中文)
    private String representative2;    // 法定代表人(英文)
    private String representative3;    // 法定代表人(中文重复)
    private String representative4;    // 法定代表人(英文重复)
    private String expiryDate1;        // 到期日期(中文格式)
    private String expiryDate2;        // 到期日期(英文格式)
    private String dateOfIssue1;       // 签发日期(中文格式)
    private String dateOfIssue3;       // 签发日期(重复)
    private String issuingAuthority1;  // 发证机关(中文)
    private String issuingAuthority2;  // 发证机关(英文)
    private String issuingAuthority3;  // 发证机关(中文重复)
    private String issuingAuthority4;  // 发证机关(英文重复)
    private String remark1;            // 备注(中文)
    private String remark2;            // 备注(英文)
    private String annualExamination;  // 年审信息
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 