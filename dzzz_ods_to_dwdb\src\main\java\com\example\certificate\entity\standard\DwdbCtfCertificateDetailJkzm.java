package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员健康证明实体类
 */
@Data
public class DwdbCtfCertificateDetailJkzm {
    private String jkzmId;              // 主键ID
    private String dataId;              // 数据ID
    private String fullNameOfTheHolder1;  // 持证人姓名(中文)
    private String fullNameOfTheHolder2;  // 持证人姓名(英文)
    private String nationality1;        // 国籍(中文)
    private String nationality2;        // 国籍(英文)
    private String dateOfBirth1;        // 出生日期(中文格式)
    private String dateOfBirth2;        // 出生日期(英文格式)
    private String gender1;             // 性别(中文)
    private String gender2;             // 性别(英文)
    private String department1;         // 部门(中文)
    private String department2;         // 部门(英文)
    private String certificateNo;       // 证书编号
    private String certificateExpiringDate1;  // 证书到期日期(中文格式)
    private String certificateExpiringDate2;  // 证书到期日期(英文格式)
    private String dateOfIssue1;        // 签发日期(中文格式)
    private String dateOfIssue2;        // 签发日期(英文格式)
    private String certificateHolderName;  // 持证人姓名
    private String informationOfPhoto;  // 照片信息
    private String yesOrNo1;           // 体检项目1结果
    private String yesOrNo2;           // 体检项目2结果
    private String yesOrNo3;           // 体检项目3结果
    private String yesOrNo4;           // 体检项目4结果
    private String yesOrNo5;           // 体检项目5结果
    private String yesOrNo6;           // 体检项目6结果
    private String yesOrNo7;           // 体检项目7结果
    private String yesOrNo8;           // 体检项目8结果
    private String yesOrNo9;           // 体检项目9结果
    private String authorizingAuthority1;  // 授权机构(中文)
    private String authorizingAuthority2;  // 授权机构(英文)
    private String doctorName1;        // 医生姓名(中文)
    private String doctorName2;        // 医生姓名(英文)
    private String issuingAuthority1;  // 发证机关(中文)
    private String issuingAuthority2;  // 发证机关(英文)
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 