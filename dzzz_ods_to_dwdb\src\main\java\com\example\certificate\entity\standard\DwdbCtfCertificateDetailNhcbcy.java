package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 内河船舶船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailNhcbcy {
    private String nhcbcyId;           // 主键ID
    private String dataId;             // 数据ID
    private String name;               // 姓名
    private String sex;                // 性别
    private String number;             // 证件号码
    private String type;               // 船员类型
    private String endDate;            // 有效期限
    private String signDept;           // 签发部门
    private String printNo;            // 印刷号码
    private String scope;              // 适用范围
    private String photo;              // 照片信息
    private String year;               // 签发年份
    private String month;              // 签发月份
    private String day;                // 签发日期
    private String issueDept;          // 发证机关
    private String signDate;           // 签发日期
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 