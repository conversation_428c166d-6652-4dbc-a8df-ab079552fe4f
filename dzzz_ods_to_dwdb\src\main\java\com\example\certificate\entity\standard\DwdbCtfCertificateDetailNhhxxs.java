package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员内河航线行驶资格证明实体类
 */
@Data
public class DwdbCtfCertificateDetailNhhxxs {
    private String nhhxxsId;           // 主键ID
    private String dataId;             // 数据ID
    private String numberOfCertificate;  // 证书编号
    private String name;               // 姓名
    private String gender;             // 性别
    private String creditCode;         // 身份证号
    private String dateOfIssue;        // 签发日期
    private String expiryDate;         // 到期日期
    private String issuingDate;        // 发证日期
    private String applivations;       // 适用范围说明
    private String photo;              // 照片信息
    private String issuingAuthority;   // 发证机关
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 