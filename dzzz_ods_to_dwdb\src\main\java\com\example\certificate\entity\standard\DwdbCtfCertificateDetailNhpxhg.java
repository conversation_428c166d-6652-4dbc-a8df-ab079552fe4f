package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 内河船舶船员培训合格证实体类
 */
@Data
public class DwdbCtfCertificateDetailNhpxhg {
    private String nhpxhgId;           // 主键ID
    private String dataId;             // 数据ID
    private String name;               // 姓名
    private String sex;                // 性别
    private String number;             // 证件号码
    private String printNo;            // 印刷号码
    private String photo;              // 照片信息
    private String year;               // 签发年份
    private String month;              // 签发月份
    private String day;                // 签发日期    
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
    private List<DwdbCtfCertificateDetailNhpxhgItem> items; // 培训项目子表数据
} 