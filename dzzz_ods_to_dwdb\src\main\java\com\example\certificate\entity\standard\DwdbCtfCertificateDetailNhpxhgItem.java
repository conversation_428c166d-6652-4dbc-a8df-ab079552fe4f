package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 内河船舶船员培训合格证书培训项目信息实体类
 */
@Data
public class DwdbCtfCertificateDetailNhpxhgItem {
    private String nhpxhgItemId;  // 主键ID
    private String dataId;        // 关联的源数据ID
    private String nhpxhgId;      // 关联的证书ID
    private String project;       // 培训项目名称
    private String signDept;      // 签发部门
    private String signDate;      // 签发日期
    private String endDate;       // 有效期限
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间
} 