package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 船员培训质量管理体系证书实体类
 */
@Data
public class DwdbCtfCertificateDetailQms {
    private String qmsId;              // 主键ID
    private String dataId;             // 数据ID
    private String number1;            // 证书编号1
    private String fullNameOfTheHolder1;  // 持证人中文名称
    private String fullNameOfTheHolder2;  // 持证人英文名称
    private String year1;              // 有效期年份
    private String month1;             // 有效期月份
    private String day1;               // 有效期日期
    private String certificateExpiringDate;  // 证书到期日期(英文格式)
    private String nameOfDulyAuthorizedOfficial1;  // 授权官员姓名
    private String evaluationOrganization1;  // 评估机构中文名称
    private String evaluationOrganization2;  // 评估机构英文名称
    private String dateOfIssue1;       // 签发日期(中文格式)
    private String dateOfIssue2;       // 签发日期(英文格式)
    private String number2;            // 证书编号2
    private String remarks;            // 备注
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 