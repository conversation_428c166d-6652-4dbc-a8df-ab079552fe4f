package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证个人信息实体类
 */
@Data
public class DwdbCtfCertificateDetailSeamanInfo {
    private String seamanInfoId;      // 主键ID
    private String dataId;            // 数据ID
    private String number;            // 证书编号
    private String nameCn;            // 姓名（中文）
    private String nameEn;            // 姓名（英文）
    private String sexCn;             // 性别（中文）
    private String sexEn;             // 性别（英文）
    private String countryCn;         // 国籍（中文）
    private String countryEn;         // 国籍（英文）
    private String birthCn;           // 出生日期（中文）
    private String birthEn;           // 出生日期（英文）
    private String fileNoCn;          // 档案编号（中文）
    private String fileNoEn;          // 档案编号（英文）
    private String qualificationCn;    // 资格等级（中文）
    private String qualificationEn;    // 资格等级（英文）
    private String initialDateCn;      // 初次发证日期（中文）
    private String initialDateEn;      // 初次发证日期（英文）
    private String expiryDateCn;       // 有效期至（中文）
    private String expiryDateEn;       // 有效期至（英文）
    private String signDeptCn;         // 签发机关（中文）
    private String signDeptEn;         // 签发机关（英文）
    private String officeOfIssueCn;    // 发证机关（中文）
    private String officeOfIssueEn;    // 发证机关（英文）
    private String date;               // 日期
    private String photo;              // 照片路径
    private String year;               // 发证年份
    private String month;              // 发证月份
    private String day;                // 发证日期
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 