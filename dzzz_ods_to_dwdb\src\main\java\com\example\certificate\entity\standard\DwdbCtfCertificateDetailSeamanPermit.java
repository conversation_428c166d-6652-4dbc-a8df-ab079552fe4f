package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证照面信息主表实体类
 */
@Data
public class DwdbCtfCertificateDetailSeamanPermit {
    private String seamanPermitId;          // 主键ID
    private String dataId;                // 证照数据主键
    private String permitNumber1;         // 许可证编号1
    private String anThorityName1;        // 机构名称1
    private String trainingInstitutionCode1; // 培训机构代码1
    private String representative1;       // 法定代表人1
    private String trainingProgram1;      // 培训项目1
    private String trainingProgram2;      // 培训项目2
    private String registeredAddress1;    // 注册地址1
    private String trainingLocation1;     // 培训地点1
    private String periodOfValidity1;     // 有效期起始1
    private String periodOfValidity2;     // 有效期结束1
    private String issuingAuthority1;     // 发证机关1
    private String dateofIssue1;          // 发证日期1
    private String permitNumber2;         // 许可证编号2
    private String anThorityName2;        // 机构名称2
    private String registeredAddress2;    // 注册地址2
    private String representative2;       // 法定代表人2
    private String trainingLocation2;     // 培训地点2
    private String periodOfValidity3;     // 有效期起始2
    private String periodOfValidity4;     // 有效期结束2
    private String remarks;               // 备注
    private String issuingAuthority2;     // 发证机关2
    private String dateofIssue2;          // 发证日期2
    private Date createTime;
    private Date updateTime;
    private String createBy;              // 创建人
    private String updateBy;              // 更新人
} 