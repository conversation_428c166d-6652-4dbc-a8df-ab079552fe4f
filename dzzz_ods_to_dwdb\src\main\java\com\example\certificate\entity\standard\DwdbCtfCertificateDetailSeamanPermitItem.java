package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证培训项目子表实体类
 */
@Data
public class DwdbCtfCertificateDetailSeamanPermitItem {
    private String seamanPermitItemId;    // 主键ID
    private String dataId;                // 关联的源数据ID
    private String seamanPermitId;        // 关联主表ID
    private String number;              // 培训项目序号
    private String atrainingProgram;    // 培训项目名称
    private String trainingScale;       // 培训规模
    private Date createTime;            // 创建时间
    private Date updateTime;            // 更新时间
    private String createBy;            // 创建人
    private String updateBy;            // 更新人
} 