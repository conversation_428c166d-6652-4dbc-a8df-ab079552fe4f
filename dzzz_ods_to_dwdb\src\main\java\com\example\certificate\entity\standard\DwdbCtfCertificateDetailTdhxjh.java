package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 特定航线江海直达船舶船员行驶资格证明培训合格证实体类
 */
@Data
public class DwdbCtfCertificateDetailTdhxjh {
    private String tdhxjhId;           // 主键ID
    private String dataId;             // 数据ID
    private String numberOfCertificate; // 证书编号
    private String name;               // 姓名
    private String dateOfBirth;        // 出生日期
    private String creditCode;         // 身份证号
    private String gender;             // 性别
    private String dateOfIssue;        // 签发日期
    private String expiryDate;         // 有效期限
    private String applivations;       // 特定航线范围
    private String limitationsApplying; // 限制说明
    private String photo;              // 照片信息
    private String issuingAuthority;   // 发证机关
    private String issuingDate;        // 发证日期
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 