package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailXhcsrzFunction {
    private String xhcsrzFunctionId;   // 主键
    private String xhcsrzId;           // 证书主表ID
    private String dataId;            // 关联的源数据ID
    private String function1;          // 职能(中文)
    private String function2;          // 职能(英文)
    private String level1;             // 等级(中文)
    private String level2;             // 等级(英文)
    private String limitationsApplying1; // 适用限制(中文)
    private String limitationsApplying2; // 适用限制(英文)
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 