package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 引航员船员适任证书实体类
 */
@Data
public class DwdbCtfCertificateDetailYhysrz {
    private String yhysrzId;                    // 主键ID
    private String dataId;                      // 数据ID
    private String fullNameOfTheHolder1;        // 持证人姓名(中文)
    private String fullNameOfTheHolder2;        // 持证人姓名(英文)
    private String nationality1;                // 国籍/省份(中文)
    private String nationality2;                // 国籍/省份(英文)
    private String dateOfBirth1;                // 出生日期(中文格式)
    private String dateOfBirth2;                // 出生日期(英文格式)
    private String gender1;                     // 性别(中文)
    private String gender2;                     // 性别(英文)
    private String certificateNo;               // 证书编号
    private String certificateExpiringDate1;    // 证书到期日期(中文格式)
    private String certificateExpiringDate2;    // 证书到期日期(英文格式)
    private String dateOfIssue1;                // 签发日期(中文格式)
    private String dateOfIssue2;                // 签发日期(英文格式)
    private String certificateHolderName;       // 持证人姓名
    private String informationOfPhoto;          // 照片信息    
    private String remarks;                     // 备注
    private String nameOfDulyAuthorizedOfficial1; // 授权官员姓名(中文)
    private String nameOfDulyAuthorizedOfficial2; // 授权官员姓名(英文)
    private String issuingAuthority1;           // 发证机关(中文)
    private String issuingAuthority2;           // 发证机关(英文)
    private Date createTime;                    // 创建时间
    private Date updateTime;                    // 更新时间
} 