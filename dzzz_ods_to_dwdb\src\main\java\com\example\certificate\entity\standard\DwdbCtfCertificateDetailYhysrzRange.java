package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailYhysrzRange {
    private String yhysrzRangeId;      // 主键
    private String yhysrzId;           // 证书主表ID
    private String dataId;            // 关联的源数据ID
    private String type1;              // 引航员类型(中文)
    private String type2;              // 引航员类型(英文)
    private String level1;             // 等级(中文)
    private String level2;             // 等级(英文)
    private String pilotageArea1;      // 引航区域(中文)
    private String pilotageArea2;      // 引航区域(英文)
    private String limitationOfPolotage1; // 引航限制(中文)
    private String limitationOfPolotage2; // 引航限制(英文)
    private Date createTime;           // 创建时间
    private Date updateTime;           // 更新时间
} 