package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

@Data
public class DwdbCtfCertificateDetailYtjsz {
    private String ytjszId;                  // 主键ID
    private String dataId;                   // 证照数据主键
    private String fullNameOfTheHolder1;     // 持证人姓名(中文)
    private String fullNameOfTheHolder2;     // 持证人姓名(英文)
    private String nationality1;             // 国籍(中文)
    private String nationality2;             // 国籍(英文)
    private String dateOfBirth1;             // 出生日期(中文格式)
    private String dateOfBirth2;             // 出生日期(英文格式)
    private String gender1;                  // 性别(中文)
    private String gender2;                  // 性别(英文)
    private String certificateNo;            // 证书编号
    private String dateOfExpiry1;            // 证书到期日期(中文格式)
    private String dateOfExpiry2;            // 证书到期日期(英文格式)
    private String issuedOn1;                // 签发日期(中文格式)
    private String issuedOn2;                // 签发日期(英文格式)
    private String informationOfPhoto;       // 照片信息
    private String fileNoCn;                 // 文件编号(中文)
    private String fileNoEn;                 // 文件编号(英文)
    private String qualificationCn;          // 资格(中文)
    private String qualificationEn;          // 资格(英文)
    private String initialDateCn;            // 初次发证日期(中文)
    private String initialDateEn;            // 初次发证日期(英文)
    private String signDeptCn;               // 签发机构(中文)
    private String signDeptEn;               // 签发机构(英文)
    private String officeOfIssueCn;          // 签发机构(中文)
    private String officeOfIssueEn;          // 签发机构(英文)
    private String date;                     // 日期
    private String year;                     // 年
    private String month;                    // 月
    private String day;                      // 日
    private Date createTime;                 // 创建时间
    private Date updateTime;                 // 更新时间
} 