package com.example.certificate.mapper.aggregate;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.aggregate.OdsCertificateData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.time.LocalDateTime;

@Mapper
public interface OdsCertificateDataMapper extends BaseMapper<OdsCertificateData> {
    
    /**
     * 获取最新的数据同步时间
     */
    @Select("SELECT MAX(fcdc_date) FROM ods_certificate_data")
    Date getCurrentTime();

    /**
     * 查询增量数据
     * @param startTime 开始时间
     * @param lastDataId 上次处理的最后数据ID
     * @param endTime 结束时间上限
     * @return 增量数据列表
     */
    List<OdsCertificateData> selectIncrementalData(
            @Param("startTime") LocalDateTime startTime, 
            @Param("lastDataId") String lastDataId);
} 