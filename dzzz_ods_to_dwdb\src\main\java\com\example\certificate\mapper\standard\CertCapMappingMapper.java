package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.CertCapMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CertCapMappingMapper extends BaseMapper<CertCapMapping> {
    
    @Select("SELECT * FROM dwdb_ctf_cert_cap_mapping WHERE del_flag = '0'")
    List<CertCapMapping> getAllCapMappings();
    
    /**
     * 根据源职务名称查询映射关系
     */
    @Select("SELECT * FROM dwdb_ctf_cert_cap_mapping WHERE SOURCE_CAP_NAME = #{sourceCapName} AND DEL_FLAG = '0' LIMIT 1")
    CertCapMapping selectBySourceCapName(@Param("sourceCapName") String sourceCapName);
    
    @Insert("INSERT INTO dwdb_ctf_cert_cap_mapping (" +
            "CAP_MAPPING_ID, SOURCE_CAP_NAME, DEST_CAP_NAME, DEL_FLAG, " +
            "rec_create_date, rec_modify_date) " +
            "VALUES (" +
            "#{capMappingId}, #{sourceCapName}, #{destCapName}, #{delFlag}, " +
            "#{recCreateDate}, #{recModifyDate})")
    int insertMapping(CertCapMapping mapping);
} 