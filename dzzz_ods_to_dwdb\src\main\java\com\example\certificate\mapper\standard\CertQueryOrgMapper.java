package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.CertQueryOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CertQueryOrgMapper extends BaseMapper<CertQueryOrg> {
    
    @Select("SELECT * FROM dwdb_ctf_cert_query_org")
    List<CertQueryOrg> getAllQueryOrgs();
    
    void batchInsert(@Param("list") List<CertQueryOrg> list);
} 