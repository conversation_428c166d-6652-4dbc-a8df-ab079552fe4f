package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.CertTypeDirectory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CertTypeDirectoryMapper extends BaseMapper<CertTypeDirectory> {
    
    /**
     * 根据目录ID获取证书类型信息
     * 1. 先根据catalogId查询dwdb_ctf_certificate_config表获取CERT_TYPE_DIR_CODE
     * 2. 再根据CERT_TYPE_DIR_CODE查询dwdb_ctf_cert_type_directory表获取证书类型信息
     * 
     * @param catalogId 目录ID
     * @return 证书类型信息
     */
    @Select("SELECT d.* FROM dwdb_ctf_cert_type_directory d " +
           "JOIN dwdb_ctf_certificate_config c ON d.certificate_type_code = c.CERT_TYPE_DIR_CODE " +
           "WHERE c.catalog_id = #{catalogId} " +
           "AND c.del_flag = '0' " +
           "AND d.del_flag = '0'")
    CertTypeDirectory getCertTypeInfo(@Param("catalogId") String catalogId);
    
    @Select("SELECT c.catalog_id, d.* FROM dwdb_ctf_cert_type_directory d " +
           "JOIN dwdb_ctf_certificate_config c ON d.certificate_type_code = c.CERT_TYPE_DIR_CODE " +
           "WHERE c.del_flag = '0' AND d.del_flag = '0'")
    List<CertTypeDirectory> getAllCertTypeInfo();
} 