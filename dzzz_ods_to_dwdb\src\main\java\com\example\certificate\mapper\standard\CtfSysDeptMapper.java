package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.CtfSysDept;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CtfSysDeptMapper extends BaseMapper<CtfSysDept> {
    
    @Select("SELECT * FROM dwdb_ctf_sys_dept WHERE code = #{code} AND del_flag = '0'")
    CtfSysDept getDeptInfo(@Param("code") String code);
    
    @Select("SELECT * FROM dwdb_ctf_sys_dept WHERE del_flag = '0'")
    List<CtfSysDept> getAllDeptInfo();
} 