package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.DataReceptionTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.Date;
import java.time.LocalDateTime;

@Mapper
public interface DataReceptionTaskMapper extends BaseMapper<DataReceptionTask> {
    DataReceptionTask getTaskByName(@Param("taskName") String taskName);
    void updateLastCompletedTime(@Param("taskName") String taskName, @Param("lastCompletedTime") Date lastCompletedTime);
    /**
     * 更新任务的最后完成时间和最后数据ID
     * @param taskName 任务名称
     * @param lastCompletedTime 最后完成时间
     * @param lastDataId 最后数据ID
     */
    void updateLastCompletedTimeAndDataId(@Param("taskName") String taskName, 
                                         @Param("lastCompletedTime") LocalDateTime lastCompletedTime,
                                         @Param("lastDataId") String lastDataId);
} 