package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.DictYthOrgMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface DictYthOrgMappingMapper extends BaseMapper<DictYthOrgMapping> {
    
    @Select("SELECT * FROM dwdb_ctf_dict_yth_org_mapping WHERE src_org_code = #{srcOrgCode}")
    DictYthOrgMapping getOrgMapping(@Param("srcOrgCode") String srcOrgCode);
    
    @Select("SELECT * FROM dwdb_ctf_dict_yth_org_mapping")
    List<DictYthOrgMapping> getAllOrgMapping();
} 