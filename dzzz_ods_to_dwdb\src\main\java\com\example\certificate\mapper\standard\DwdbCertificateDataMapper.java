package com.example.certificate.mapper.standard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.certificate.entity.standard.DwdbCertificateData;
import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzbCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbExperience;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbOptions;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhgTraining;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrzShip;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHywpjg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailJkzm;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcbcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhhxxs;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailQms;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanInfo;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermit;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermitItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailTdhxjh;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhgItem;
import com.example.certificate.entity.standard.DwdbCertificateDataError;
import com.example.certificate.entity.standard.DwdbCertificateDataAttribute;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrzRange;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYtjsz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCscspx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCsssfzpx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycr;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsssjn;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkzItem;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytm;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmCap;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmFunc;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxhgqz;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxqzTrain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.time.LocalDateTime;

@Mapper
public interface DwdbCertificateDataMapper extends BaseMapper<DwdbCertificateData> {
    
    /**
     * 批量插入数据
     */
    int batchInsert(List<DwdbCertificateData> dataList);

    void insertBcjhljzb(DwdbCtfCertificateDetailBcjhljzb data);
    void insertCysrzsqb(DwdbCtfCertificateDetailCysrzsqb data);
    void insertCysrzsqbExperience(DwdbCtfCertificateDetailCysrzsqbExperience data);
    void insertCysrzsqbOptions(DwdbCtfCertificateDetailCysrzsqbOptions data);
    void insertYhysrz(DwdbCtfCertificateDetailYhysrz data);
    void insertGwccy(DwdbCtfCertificateDetailGwccy data);
    void insertHcgjcy(DwdbCtfCertificateDetailHcgjcy data);
    void insertHcgjcyCapacity(DwdbCtfCertificateDetailHcgjcyCapacity data);
    void insertHcgjcyFunction(DwdbCtfCertificateDetailHcgjcyFunction data);
    void insertHcptcysrz(DwdbCtfCertificateDetailHcptcysrz data);
    void insertHcptcysrzCapacity(DwdbCtfCertificateDetailHcptcysrzCapacity data);
    void insertHcpxhg(DwdbCtfCertificateDetailHcpxhg data);
    void insertHcpxhgTraining(DwdbCtfCertificateDetailHcpxhgTraining data);
    void insertHsfhcysrz(DwdbCtfCertificateDetailHsfhcysrz data);
    void insertHsfhcysrzShip(DwdbCtfCertificateDetailHsfhcysrzShip data);
    void insertHywpjg(DwdbCtfCertificateDetailHywpjg data);
    void insertJkzm(DwdbCtfCertificateDetailJkzm data);
    void insertNhcbcy(DwdbCtfCertificateDetailNhcbcy data);
    void insertNhhxxs(DwdbCtfCertificateDetailNhhxxs data);
    void insertNhpxhg(DwdbCtfCertificateDetailNhpxhg data);
    void insertQms(DwdbCtfCertificateDetailQms data);
    void insertSeamanInfo(DwdbCtfCertificateDetailSeamanInfo data);
    void insertSeamanPermit(DwdbCtfCertificateDetailSeamanPermit data);
    void insertSeamanPermitItem(DwdbCtfCertificateDetailSeamanPermitItem data);
    void insertTdhxjh(DwdbCtfCertificateDetailTdhxjh data);
    void insertXhcsrz(DwdbCtfCertificateDetailXhcsrz data);

    // 各类子表批量插入方法
    void batchInsertBcjhljzb(List<DwdbCtfCertificateDetailBcjhljzb> dataList);
    void batchInsertGwccy(List<DwdbCtfCertificateDetailGwccy> dataList);
    void batchInsertCysrzsqb(List<DwdbCtfCertificateDetailCysrzsqb> dataList);
    void batchInsertHcgjcy(List<DwdbCtfCertificateDetailHcgjcy> dataList);
    void batchInsertHcptcysrz(List<DwdbCtfCertificateDetailHcptcysrz> dataList);
    void batchInsertHcpxhg(List<DwdbCtfCertificateDetailHcpxhg> dataList);
    void batchInsertHsfhcysrz(List<DwdbCtfCertificateDetailHsfhcysrz> dataList);
    void batchInsertHywpjg(List<DwdbCtfCertificateDetailHywpjg> dataList);
    void batchInsertJkzm(List<DwdbCtfCertificateDetailJkzm> dataList);
    void batchInsertNhcbcy(List<DwdbCtfCertificateDetailNhcbcy> dataList);
    void batchInsertNhhxxs(List<DwdbCtfCertificateDetailNhhxxs> dataList);
    void batchInsertNhpxhg(List<DwdbCtfCertificateDetailNhpxhg> dataList);
    void batchInsertQms(List<DwdbCtfCertificateDetailQms> dataList);
    void batchInsertSeamanInfo(List<DwdbCtfCertificateDetailSeamanInfo> dataList);
    void batchInsertSeamanPermit(List<DwdbCtfCertificateDetailSeamanPermit> dataList);
    void batchInsertSeamanPermitItem(List<DwdbCtfCertificateDetailSeamanPermitItem> dataList);
    void batchInsertTdhxjh(List<DwdbCtfCertificateDetailTdhxjh> dataList);
    void batchInsertXhcsrz(List<DwdbCtfCertificateDetailXhcsrz> dataList);
    void batchInsertYhysrz(List<DwdbCtfCertificateDetailYhysrz> dataList);
    
    // 子表关联表批量插入方法
    void batchInsertHcgjcyCapacity(List<DwdbCtfCertificateDetailHcgjcyCapacity> dataList);
    void batchInsertHcptcysrzCapacity(List<DwdbCtfCertificateDetailHcptcysrzCapacity> dataList);
    void batchInsertHcgjcyFunction(List<DwdbCtfCertificateDetailHcgjcyFunction> dataList);
    void batchInsertHcptcysrzFunction(List<DwdbCtfCertificateDetailHcptcysrzFunction> dataList);
    void batchInsertHsfhcysrzShip(List<DwdbCtfCertificateDetailHsfhcysrzShip> dataList);
    void batchInsertCysrzsqbExperience(List<DwdbCtfCertificateDetailCysrzsqbExperience> dataList);
    void batchInsertCysrzsqbOptions(List<DwdbCtfCertificateDetailCysrzsqbOptions> dataList);

    void batchInsertNhpxhgItem(List<DwdbCtfCertificateDetailNhpxhgItem> items);

    /**
     * 查询需要重处理的数据
     * @return 需要重处理的数据列表
     */
    List<OdsCertificateData> selectRedoData();

    /**
     * 更新重处理状态
     * @param dataId 数据ID
     * @param status 状态 1-处理成功 2-处理失败
     * @param failReason 失败原因
     */
    void updateRedoStatus(@Param("dataId") String dataId, @Param("status") String status, @Param("failReason") String failReason);

    /**
     * 插入错误记录
     * @param errorRecord 错误记录
     */
    void insertErrorRecord(DwdbCertificateDataError errorRecord);

    /**
     * 插入重处理数据
     */
    void insertRedoData(
        @Param("dataid") String dataid,
        @Param("certificateid") String certificateid,
        @Param("catalogid") String catalogid,
        @Param("catalogname") String catalogname,
        @Param("templateid") String templateid,
        @Param("certificatetype") String certificatetype,
        @Param("certificatetypecode") String certificatetypecode,
        @Param("issuedept") String issuedept,
        @Param("issuedeptcode") String issuedeptcode,
        @Param("certificateareacode") String certificateareacode,
        @Param("certificateholder") String certificateholder,
        @Param("certificateholdercode") String certificateholdercode,
        @Param("certificateholdertype") String certificateholdertype,
        @Param("certificatenumber") String certificatenumber,
        @Param("issuedate") String issuedate,
        @Param("validbegindate") String validbegindate,
        @Param("validenddate") String validenddate,
        @Param("surfacedata") String surfacedata,
        @Param("status") String status,
        @Param("creator") String creator,
        @Param("createtime") String createtime,
        @Param("operator") String operator,
        @Param("updatetime") String updatetime,
        @Param("filepath") String filepath,
        @Param("syncstatus") String syncstatus,
        @Param("remarks") String remarks,
        @Param("deptid") String deptid,
        @Param("applynum") String applynum,
        @Param("affairname") String affairname,
        @Param("affairtype") String affairtype,
        @Param("servebusiness") String servebusiness,
        @Param("affairid") String affairid,
        @Param("affairnum") String affairnum,
        @Param("qztype") String qztype,
        @Param("zztype") String zztype,
        @Param("drafturl") String drafturl,
        @Param("isview") String isview,
        @Param("sortname") String sortname,
        @Param("col1") String col1,
        @Param("verifydate") String verifydate,
        @Param("verification") String verification,
        @Param("creditcode") String creditcode,
        @Param("sealname") String sealname,
        @Param("fcdc_date") LocalDateTime fcdc_date,
        @Param("redo_status") String redo_status,
        @Param("fail_reason") String fail_reason
    );

    /**
     * 检查数据是否已存在于重处理表
     * @param dataId 数据ID
     * @return 是否存在
     */
    boolean existsInRedoTable(@Param("dataId") String dataId);

    // 主表删除
    void deleteByDataId(@Param("dataId") String dataId);

    // 明细表删除
    void deleteBcjhljzbByDataId(@Param("dataId") String dataId);
    void deleteGwccyByDataId(@Param("dataId") String dataId);
    void deleteCysrzsqbByDataId(@Param("dataId") String dataId);
    void deleteCysrzsqbExperienceByDataId(@Param("dataId") String dataId);
    void deleteCysrzsqbOptionsByDataId(@Param("dataId") String dataId);
    void deleteHcgjcyByDataId(@Param("dataId") String dataId);
    void deleteHcgjcyCapacityByDataId(@Param("dataId") String dataId);
    void deleteHcgjcyFunctionByDataId(@Param("dataId") String dataId);
    void deleteHcptcysrzByDataId(@Param("dataId") String dataId);
    void deleteHcptcysrzCapacityByDataId(@Param("dataId") String dataId);
    void deleteHcptcysrzFunctionByDataId(@Param("dataId") String dataId);
    void deleteHcpxhgByDataId(@Param("dataId") String dataId);
    void deleteHcpxhgTrainingByDataId(@Param("dataId") String dataId);
    void deleteHsfhcysrzByDataId(@Param("dataId") String dataId);
    void deleteHsfhcysrzShipByDataId(@Param("dataId") String dataId);
    void deleteHywpjgByDataId(@Param("dataId") String dataId);
    void deleteJkzmByDataId(@Param("dataId") String dataId);
    void deleteNhcbcyByDataId(@Param("dataId") String dataId);
    void deleteNhhxxsByDataId(@Param("dataId") String dataId);
    void deleteNhpxhgByDataId(@Param("dataId") String dataId);
    void deleteNhpxhgItemByDataId(@Param("dataId") String dataId);
    void deleteQmsByDataId(@Param("dataId") String dataId);
    void deleteSeamanInfoByDataId(@Param("dataId") String dataId);
    void deleteSeamanPermitByDataId(@Param("dataId") String dataId);
    void deleteSeamanPermitItemByDataId(@Param("dataId") String dataId);
    void deleteTdhxjhByDataId(@Param("dataId") String dataId);
    void deleteXhcsrzByDataId(@Param("dataId") String dataId);
    void deleteYhysrzByDataId(@Param("dataId") String dataId);

    void batchInsertAttributes(@Param("attributes") List<DwdbCertificateDataAttribute> attributes);

    void deleteXhcsrzCapacityByDataId(@Param("dataId") String dataId);
    void deleteXhcsrzFunctionByDataId(@Param("dataId") String dataId);
    void batchInsertXhcsrzCapacity(List<DwdbCtfCertificateDetailXhcsrzCapacity> dataList);
    void batchInsertXhcsrzFunction(List<DwdbCtfCertificateDetailXhcsrzFunction> dataList);

    void batchInsertYhysrzRange(List<DwdbCtfCertificateDetailYhysrzRange> dataList);

    void deleteYtjszByDataId(@Param("dataId") String dataId);
    void batchInsertYtjsz(List<DwdbCtfCertificateDetailYtjsz> dataList);

    void deleteCscspxByDataId(@Param("dataId") String dataId);
    void batchInsertCscspx(List<DwdbCtfCertificateDetailCscspx> dataList);

    void deleteCsssfzpxByDataId(@Param("dataId") String dataId);
    void batchInsertCsssfzpx(List<DwdbCtfCertificateDetailCsssfzpx> dataList);

    void deleteHccycrByDataId(@Param("dataId") String dataId);
    void deleteHccycrCapacityByDataId(@Param("dataId") String dataId);
    void deleteHccycrFunctionByDataId(@Param("dataId") String dataId);
    void batchInsertHccycr(List<DwdbCtfCertificateDetailHccycr> dataList);
    void batchInsertHccycrCapacity(List<DwdbCtfCertificateDetailHccycrCapacity> dataList);
    void batchInsertHccycrFunction(List<DwdbCtfCertificateDetailHccycrFunction> dataList);

    void deleteHsssjnByDataId(@Param("dataId") String dataId);
    void batchInsertHsssjn(List<DwdbCtfCertificateDetailHsssjn> dataList);

    void deleteHcbcjcyByDataId(@Param("dataId") String dataId);
    void deleteHcbcjcyCapacityByDataId(@Param("dataId") String dataId);
    void batchInsertHcbcjcy(List<DwdbCtfCertificateDetailHcbcjcy> dataList);
    void batchInsertHcbcjcyCapacity(List<DwdbCtfCertificateDetailHcbcjcyCapacity> dataList);

    void batchInsertBcjhljzbCapacity(List<DwdbCtfCertificateDetailBcjhljzbCapacity> list);
    void deleteBcjhljzbCapacityByDataId(String dataId);

    void batchInsertHcpxhgTraining(List<DwdbCtfCertificateDetailHcpxhgTraining> list);

    void batchInsertGwccyCapacity(List<DwdbCtfCertificateDetailGwccyCapacity> list);
    void deleteGwccyCapacityByDataId(String dataId);

    void insertNhcyxkz(DwdbCtfCertificateDetailNhcyxkz nhcyxkz);
    void batchInsertNhcyxkz(List<DwdbCtfCertificateDetailNhcyxkz> dataList);
    void batchInsertNhcyxkzItems(List<DwdbCtfCertificateDetailNhcyxkzItem> items);
    void deleteNhcyxkzByDataId(@Param("dataId") String dataId);
    void deleteNhcyxkzItemsByDataId(@Param("dataId") String dataId);

    // 海船船员特免证明相关方法
    void batchInsertHccytm(List<DwdbCtfCertDetailHccytm> dataList);
    void batchInsertHccytmCapacity(List<DwdbCtfCertDetailHccytmCap> dataList);
    void batchInsertHccytmFunction(List<DwdbCtfCertDetailHccytmFunc> dataList);
    void deleteHccytmByDataId(@Param("dataId") String dataId);
    void deleteHccytmCapacityByDataId(@Param("dataId") String dataId);
    void deleteHccytmFunctionByDataId(@Param("dataId") String dataId);

    // 海船船员培训合格证承认签证相关方法
    void batchInsertHcpxhgqz(List<DwdbCtfCertDetailHcpxhgqz> dataList);
    void batchInsertHcpxqzTrain(List<DwdbCtfCertDetailHcpxqzTrain> dataList);
    void deleteHcpxhgqzByDataId(@Param("dataId") String dataId);
    void deleteHcpxqzTrainByDataId(@Param("dataId") String dataId);

    /**
     * 根据证照编号检查记录是否已存在
     * @param certificateNumber 证照编号
     * @return 是否存在
     */
    boolean existsByCertificateNumber(String certificateNumber);

    /**
     * 根据数据ID检查记录是否已存在
     * @param dataId 数据ID
     * @return 是否存在
     */
    boolean existsByDataId(String dataId);

    /**
     * 根据证书ID检查记录是否已存在
     * @param certificateId 证书ID
     * @return 是否存在
     */
    boolean existsByCertificateId(String certificateId);

    /**
     * 根据证书ID查找数据ID列表
     * @param certificateId 证书ID
     * @return 数据ID列表
     */
    List<String> findDataIdsByCertificateId(String certificateId);

    /**
     * 根据数据ID获取证书类型
     * @param dataId 数据ID
     * @return 证书类型
     */
    String getCertificateTypeByDataId(String dataId);
} 