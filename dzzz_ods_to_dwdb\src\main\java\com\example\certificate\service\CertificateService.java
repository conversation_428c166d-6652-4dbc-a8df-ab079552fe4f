package com.example.certificate.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Slf4j
@Service
public class CertificateService {

    @Autowired
    private CertificateEtlService certificateEtlService;

    @PostConstruct
    public void init() {
        // 测试证照数据ETL流程
        try {
            String result = certificateEtlService.executeEtlTask("ods_certificate_data");
            log.info("证照数据ETL测试结果: {}", result);
        } catch (Exception e) {
            log.error("证照数据ETL测试失败", e);
        }
    }
} 