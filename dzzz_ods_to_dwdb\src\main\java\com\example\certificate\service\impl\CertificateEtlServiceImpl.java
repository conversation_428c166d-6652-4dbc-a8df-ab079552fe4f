package com.example.certificate.service.impl;

import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.*;
import com.example.certificate.mapper.aggregate.OdsCertificateDataMapper;
import com.example.certificate.mapper.standard.DataReceptionTaskMapper;
import com.example.certificate.mapper.standard.DwdbCertificateDataMapper;
import com.example.certificate.service.CertificateEtlService;
import com.example.certificate.util.CertificateConverter;
import com.example.certificate.dto.CertificateConvertResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import com.alibaba.fastjson.JSON;
import java.util.Collections;
import java.util.Arrays;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.sql.SQLException;
import com.example.certificate.mapper.standard.CertTypeDirectoryMapper;
import com.example.certificate.mapper.standard.CtfSysDeptMapper;
import com.example.certificate.mapper.standard.DictYthOrgMappingMapper;
import com.example.certificate.mapper.standard.CertQueryOrgMapper;
import com.example.certificate.mapper.standard.CertCapMappingMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

@Slf4j
@Service
public class CertificateEtlServiceImpl implements CertificateEtlService {

    @Autowired
    private OdsCertificateDataMapper odsCertificateDataMapper;

    @Autowired
    private DwdbCertificateDataMapper dwdbCertificateDataMapper;

    @Autowired
    private DataReceptionTaskMapper dataReceptionTaskMapper;

    @Autowired
    private CertificateConverter certificateConverter;

    // 将缓存改为静态变量
    private static final Map<String, CertTypeDirectory> certTypeCache = new HashMap<>();
    private static final Map<String, DictYthOrgMapping> orgMappingCache = new HashMap<>();
    private static final Map<String, CtfSysDept> deptInfoCache = new HashMap<>();
    private static final Map<String, CertQueryOrg> queryOrgCache = new HashMap<>();
    private static final Map<String, String> capMappingCache = new HashMap<>();

    private static boolean cacheInitialized = false;
    private static final List<CertQueryOrg> newQueryOrgs = new ArrayList<>();

    @Autowired
    private CertTypeDirectoryMapper certTypeDirectoryMapper;

    @Autowired
    private DictYthOrgMappingMapper dictYthOrgMappingMapper;

    @Autowired
    private CtfSysDeptMapper ctfSysDeptMapper;

    @Autowired
    private CertQueryOrgMapper certQueryOrgMapper;

    @Autowired
    private CertCapMappingMapper certCapMappingMapper;

    /**
     * 初始化基础参数表缓存
     */
    private synchronized void initCache() {
        if (cacheInitialized) {
            return;
        }

        log.info("开始初始化基础参数表缓存...");

        // 1. 加载证书类型目录
        List<CertTypeDirectory> certTypeList = certTypeDirectoryMapper.getAllCertTypeInfo();
        for (CertTypeDirectory certType : certTypeList) {
            certTypeCache.put(certType.getCatalogId(), certType);
        }
        log.info("证书类型目录缓存加载完成，共 {} 条记录", certTypeCache.size());

        // 2. 加载机构映射
        List<DictYthOrgMapping> orgMappingList = dictYthOrgMappingMapper.getAllOrgMapping();
        log.info("从数据库加载机构映射数据，共 {} 条记录", orgMappingList.size());
        for (DictYthOrgMapping orgMapping : orgMappingList) {
            orgMappingCache.put(orgMapping.getSrcOrgCode(), orgMapping);
        }
        log.info("机构映射缓存加载完成，共 {} 条记录", orgMappingCache.size());

        // 3. 加载部门信息
        List<CtfSysDept> deptInfoList = ctfSysDeptMapper.getAllDeptInfo();
        for (CtfSysDept deptInfo : deptInfoList) {
            deptInfoCache.put(deptInfo.getCode(), deptInfo);
        }
        log.info("部门信息缓存加载完成，共 {} 条记录", deptInfoCache.size());

        // 4. 加载机构查询辅助表
        List<CertQueryOrg> queryOrgList = certQueryOrgMapper.getAllQueryOrgs();
        for (CertQueryOrg queryOrg : queryOrgList) {
            queryOrgCache.put(queryOrg.getQueryOrgName(), queryOrg);
        }
        log.info("机构查询辅助表缓存加载完成，共 {} 条记录", queryOrgCache.size());

        // 加载职务映射表
        log.info("开始加载职务映射表...");
        List<CertCapMapping> capMappings = certCapMappingMapper.getAllCapMappings();
        for (CertCapMapping mapping : capMappings) {
            capMappingCache.put(mapping.getSourceCapName(), mapping.getDestCapName());
        }
        log.info("职务映射表加载完成，共加载 {} 条记录", capMappings.size());

        cacheInitialized = true;
        log.info("基础参数表缓存初始化完成");

        // 将缓存对象设置到CertificateConverter中
        certificateConverter.setCaches(certTypeCache, orgMappingCache, deptInfoCache);
    }

    @Override
    public String executeEtlTask(String taskName) {
        // 初始化缓存
        initCache();

        log.info("开始执行证照数据ETL任务, taskName: {}", taskName);
        long startTime = System.currentTimeMillis();

        try {
            StringBuilder resultBuilder = new StringBuilder();
            long costTime;

            // 0. 查询需要重处理的数据
            log.info("开始处理重处理任务");
            List<OdsCertificateData> redoDataList = dwdbCertificateDataMapper.selectRedoData();
            if (!CollectionUtils.isEmpty(redoDataList)) {
                // 使用单独的事务处理重处理数据
                processRedoData(redoDataList, resultBuilder);
            }

            // 1、循环处理增量数据，直到没有新数据为止
            while (true) {
                // 2. 获取任务配置
                DataReceptionTask task = dataReceptionTaskMapper.getTaskByName(taskName);
                if (task == null) {
                    task = initTask(taskName);
                }

                // // 3. 计算时间范围
                // // 当前时间往前推5分钟，作为上限
                // LocalDateTime currentTimeMinus5Min = LocalDateTime.now().minusMinutes(5);
                // log.info("当前时间往前推5分钟: {}", currentTimeMinus5Min);
                //
                // // 如果当前时间往前推5分钟，小于task.getLastCompletedTime()，则退出任务
                // if (task.getLastCompletedTime() != null &&
                // currentTimeMinus5Min.isBefore(task.getLastCompletedTime())) {
                // log.warn("当前时间往前推5分钟({})小于上次任务完成时间({}), 任务退出",
                // currentTimeMinus5Min, task.getLastCompletedTime());
                // resultBuilder.append("任务提前退出: 当前时间往前推5分钟小于上次任务完成时间");
                // break;
                // }

                // // 将lastCompletedTime往前推5秒，作为下限
                // LocalDateTime adjustedLastCompletedTime = null;
                // if (task.getLastCompletedTime() != null) {
                // // 将时间往前推5秒，防止漏掉数据
                // adjustedLastCompletedTime = task.getLastCompletedTime().minusSeconds(5);
                // log.info("查询增量数据 - 原始时间: {}, 调整后时间(往前推5秒): {}",
                // task.getLastCompletedTime(), adjustedLastCompletedTime);
                // }

                // 3. 查询增量数据，增加fcdc_date上限条件
                List<OdsCertificateData> sourceDataList = odsCertificateDataMapper.selectIncrementalData(
                        task.getLastCompletedTime(), task.getLastDataId());

                // 如果没有增量数据了，退出循环
                if (CollectionUtils.isEmpty(sourceDataList)) {
                    break;
                }

                // 分批处理数据，每批200条
                int batchSize = 200;
                int totalSize = sourceDataList.size();
                int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整

                log.info("开始分批处理增量数据，总数据量: {}，批次大小: {}，总批次数: {}",
                        totalSize, batchSize, batchCount);

                for (int i = 0; i < batchCount; i++) {
                    int fromIndex = i * batchSize;
                    int toIndex = Math.min((i + 1) * batchSize, totalSize);
                    List<OdsCertificateData> batchDataList = sourceDataList.subList(fromIndex, toIndex);

                    log.info("处理第 {} 批数据，数据量: {}", i + 1, batchDataList.size());

                    // 使用单独的事务处理每批数据
                    processIncrementalData(taskName, batchDataList, resultBuilder);

                    // 每处理一批数据后，休眠1秒钟，避免数据库压力过大
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error("线程休眠被中断", e);
                    }
                }

                // 每处理完一轮增量数据后，休眠5秒钟
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    log.error("线程休眠被中断", e);
                }
            }

            costTime = System.currentTimeMillis() - startTime;
            log.info("{}, 耗时: {}毫秒", resultBuilder.toString(), costTime);
            return resultBuilder.toString();
        } catch (Exception e) {
            log.error("ETL任务执行异常", e);
            e.printStackTrace();
            throw new RuntimeException("ETL任务执行失败: " + e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private void processRedoData(List<OdsCertificateData> redoDataList, StringBuilder resultBuilder) {
        int redoSuccessCount = 0;
        int redoFailCount = 0;

        for (OdsCertificateData redoData : redoDataList) {
            try {
                cleanupTargetData(redoData);
                ProcessResult singleResult = processSourceData(Collections.singletonList(redoData));

                if (singleResult.isSuccess(redoData.getDataid())) {
                    dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), "1", null);
                    redoSuccessCount++;
                } else {
                    String failReason = singleResult.getFailReason(redoData.getDataid());
                    dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), "2", failReason);
                    redoFailCount++;
                }
            } catch (Exception e) {
                log.error("重处理数据失败, dataId: " + redoData.getDataid(), e);
                dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), "2",
                        e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()));
                redoFailCount++;
            }
        }

        resultBuilder.append(String.format("重处理数据完成 - 总数: %d, 成功: %d, 失败: %d; ",
                redoDataList.size(), redoSuccessCount, redoFailCount));
    }

    @Transactional(rollbackFor = Exception.class)
    private void processIncrementalData(String taskName, List<OdsCertificateData> sourceDataList,
            StringBuilder resultBuilder) {
        // 处理增量数据
        ProcessResult incrementalResult = processSourceData(sourceDataList);

        // 更新任务完成时间和最后数据ID
        OdsCertificateData lastRecord = sourceDataList.get(sourceDataList.size() - 1);
        dataReceptionTaskMapper.updateLastCompletedTimeAndDataId(
                taskName,
                lastRecord.getFcdcDate(),
                lastRecord.getDataid());
        log.info("更新任务完成时间为: {}, 最后数据ID: {}", lastRecord.getFcdcDate(), lastRecord.getDataid());

        resultBuilder.append(String.format("增量数据处理完成 - 总数: %d, 成功: %d, 失败: %d; ",
                sourceDataList.size(), incrementalResult.getSuccessCount(), incrementalResult.getFailCount()));
    }

    /**
     * 处理源数据
     * 
     * @param sourceDataList 源数据列表
     * @return 处理结果
     */
    private ProcessResult processSourceData(List<OdsCertificateData> sourceDataList) {
        ProcessResult result = new ProcessResult();

        if (sourceDataList == null || sourceDataList.isEmpty()) {
            return result;
        }

        List<DwdbCertificateData> targetDataList = new ArrayList<>();
        Map<String, List<Object>> subTableDataMap = new HashMap<>();
        List<DwdbCertificateDataAttribute> unusedAttributesList = new ArrayList<>();

        for (OdsCertificateData sourceData : sourceDataList) {
            try {
                // 1. 根据dataId查重，如果已存在则跳过处理
                String dataId = sourceData.getDataid();
                if (StringUtils.isNotBlank(dataId)) {
                    boolean existsById = dwdbCertificateDataMapper.existsByDataId(dataId);
                    if (existsById) {
                        log.info("跳过重复数据处理，数据ID已存在: {}", dataId);
                        result.addSuccess(dataId); // 标记为成功，因为不需要处理
                        continue; // 跳过此条数据的处理
                    }
                }

                // 2. 根据证书ID查重，如果已存在则先清理再重新处理（更新数据）
                String certificateId = sourceData.getCertificateid();
                if (StringUtils.isNotBlank(certificateId)) {
                    boolean existsByCertId = dwdbCertificateDataMapper.existsByCertificateId(certificateId);
                    if (existsByCertId) {
                        log.info("发现证书ID重复数据，执行清理后重新处理，证书ID: {}, 数据ID: {}", certificateId, dataId);
                        // 清理目标表中的数据，以便重新处理
                        cleanupTargetData(sourceData);
                    }
                }

                // 处理机构查询辅助表，传入证书类型
                processQueryOrg(sourceData.getIssuedept(), sourceData.getCatalogname());

                // 3. 转换主表数据
                DwdbCertificateData targetData = certificateConverter.convert(sourceData);
                certificateConverter.validateRequiredFields(targetData);
                if (targetData != null) {
                    targetDataList.add(targetData);
                }

                // 4. 转换表面数据
                CertificateConvertResult convertResult = certificateConverter.convertSurfaceData(sourceData);

                if (!convertResult.isHasError()) {
                    // 根据证书类型,从表面数据更新主表字段
                    if (sourceData.getCatalogname().equals("游艇驾驶证")
                            || sourceData.getCatalogname().equals("游艇驾驶证（内河）")
                            || sourceData.getCatalogname().equals("游艇驾驶证（海上）")
                            || sourceData.getCatalogname().equals("游艇驾驶证海上")
                            || sourceData.getCatalogname().equals("游艇驾驶证内河")) {
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailYtjsz) {
                            DwdbCtfCertificateDetailYtjsz ytjsz = (DwdbCtfCertificateDetailYtjsz) mainData;
                            targetData.setBirth(ytjsz.getDateOfBirth1());
                            targetData.setBirthEn(ytjsz.getDateOfBirth2());
                            targetData.setNameEn(ytjsz.getFullNameOfTheHolder2());
                            targetData.setCountryCn(ytjsz.getNationality1());
                            targetData.setCountryEn(ytjsz.getNationality2());
                            targetData.setSignDeptEn(ytjsz.getSignDeptEn());
                            targetData.setCertificateIssuedDateEn(ytjsz.getIssuedOn2());
                            targetData.setQualificationCn(ytjsz.getQualificationCn());
                            targetData.setCertificateExpiringDateEn(ytjsz.getDateOfExpiry2());

                            // 游艇驾驶证主表的证照颁发机构有些数据名称不全，需要用照面表的数据刷回来
                            // 只有当新值非空时才更新
                            if (StringUtils.isNotBlank(ytjsz.getSignDeptCn())) {
                                targetData.setCertificateIssuingAuthorityName(ytjsz.getSignDeptCn());
                            } else if (StringUtils.isNotBlank(ytjsz.getOfficeOfIssueCn())) {
                                targetData.setCertificateIssuingAuthorityName(ytjsz.getOfficeOfIssueCn());
                            }
                            // 如果都为空，则保留原值，不做任何操作

                            // 根据资质代码设置证书名称
                            String qualificationCn = ytjsz.getQualificationCn();
                            if (qualificationCn != null) {
                                if (qualificationCn.startsWith("A")) {
                                    targetData.setCertificateName("游艇驾驶证（海上）");
                                } else if (qualificationCn.startsWith("B")) {
                                    targetData.setCertificateName("游艇驾驶证（内河）");
                                }
                            } else {
                                // 根据证书类型名称设置证书名称
                                String catalogName = sourceData.getCatalogname();
                                if ("游艇驾驶证海上".equals(catalogName)) {
                                    targetData.setCertificateName("游艇驾驶证（海上）");
                                } else if ("游艇驾驶证内河".equals(catalogName)) {
                                    targetData.setCertificateName("游艇驾驶证（内河）");
                                }
                            }
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员内河航线行驶资格证明")) {
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailNhhxxs) {
                            DwdbCtfCertificateDetailNhhxxs nhhxxs = (DwdbCtfCertificateDetailNhhxxs) mainData;
                            // 从身份证号提取出生年月日
                            String idCard = nhhxxs.getCreditCode();
                            if (idCard != null && idCard.length() == 18) {
                                targetData.setBirth(idCard.substring(6, 10) + "-" + idCard.substring(10, 12) + "-"
                                        + idCard.substring(12, 14));
                            }
                            targetData.setApplivationsCn(nhhxxs.getApplivations());
                        }
                    } else if (sourceData.getCatalogname().equals("内河船舶船员适任证书")) {
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailNhcbcy) {
                            DwdbCtfCertificateDetailNhcbcy nhcbcy = (DwdbCtfCertificateDetailNhcbcy) mainData;
                            targetData.setCrewType(nhcbcy.getType());
                            targetData.setCertPrintNo(nhcbcy.getPrintNo());

                            // 从身份证号提取出生年月日
                            String idCard = targetData.getCertificateHolderCode();
                            if (StringUtils.isNotBlank(idCard) && idCard.length() == 18) {
                                try {
                                    String birthYear = idCard.substring(6, 10);
                                    String birthMonth = idCard.substring(10, 12);
                                    String birthDay = idCard.substring(12, 14);
                                    String birthDate = birthYear + "-" + birthMonth + "-" + birthDay;
                                    targetData.setBirth(birthDate);
                                    // log.info("从身份证号{}中提取出生日期: {}", idCard, birthDate);
                                } catch (Exception e) {
                                    log.warn("从身份证号{}提取出生日期失败: {}", idCard, e.getMessage());
                                }
                            }
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员培训合格证书")) {
                        // 获取子表数据列表
                        List<?> trainingList = convertResult.getSubTableDataList("trainingList");
                        if (trainingList != null && !trainingList.isEmpty()) {
                            StringBuilder trainingNamesCn = new StringBuilder();
                            StringBuilder trainingNamesEn = new StringBuilder();
                            StringBuilder trainingIssueDatesCn = new StringBuilder();
                            StringBuilder trainingIssueDatesEn = new StringBuilder();
                            StringBuilder trainingEffectiveDatesCn = new StringBuilder();
                            StringBuilder trainingEffectiveDatesEn = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : trainingList) {
                                DwdbCtfCertificateDetailHcpxhgTraining training = (DwdbCtfCertificateDetailHcpxhgTraining) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    trainingNamesCn.append("#");
                                    trainingNamesEn.append("#");
                                    trainingIssueDatesCn.append("#");
                                    trainingIssueDatesEn.append("#");
                                    trainingEffectiveDatesCn.append("#");
                                    trainingEffectiveDatesEn.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加培训项目名称
                                trainingNamesCn.append(training.getTitleOfTheCertificate1() != null
                                        ? training.getTitleOfTheCertificate1()
                                        : "");
                                trainingNamesEn.append(training.getTitleOfTheCertificate2() != null
                                        ? training.getTitleOfTheCertificate2()
                                        : "");

                                // 添加培训项目签发日期
                                trainingIssueDatesCn
                                        .append(training.getDateOfIssue1() != null ? training.getDateOfIssue1() : "");
                                trainingIssueDatesEn
                                        .append(training.getDateOfIssue2() != null ? training.getDateOfIssue2() : "");

                                // 添加培训项目有效期至
                                trainingEffectiveDatesCn
                                        .append(training.getDateOfExpiry1() != null ? training.getDateOfExpiry1() : "");
                                trainingEffectiveDatesEn
                                        .append(training.getDateOfExpiry2() != null ? training.getDateOfExpiry2() : "");
                            }

                            // 设置到主表对象中
                            targetData.setTrainingNamesCn(trainingNamesCn.toString());
                            targetData.setTrainingNamesEn(trainingNamesEn.toString());
                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCn.toString());
                            targetData.setTrainingIssueDatesEn(trainingIssueDatesEn.toString());
                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCn.toString());
                            targetData.setTrainingEffectiveDatesEn(trainingEffectiveDatesEn.toString());
                        }

                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHcpxhg) {
                            DwdbCtfCertificateDetailHcpxhg hcpxhg = (DwdbCtfCertificateDetailHcpxhg) mainData;
                            targetData.setBirth(hcpxhg.getDateOfBirth1());
                            targetData.setBirthEn(hcpxhg.getDateOfBirth2());
                            targetData.setNameEn(hcpxhg.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hcpxhg.getNationality1());
                            targetData.setCountryEn(hcpxhg.getNationality2());
                            targetData.setSignDeptEn(hcpxhg.getOfficalSeal2());
                            targetData.setCertificateIssuedDateEn(hcpxhg.getIssuedOn2());
                        }
                    } else if (sourceData.getCatalogname().equals("海船高级船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHcgjcy) {
                            DwdbCtfCertificateDetailHcgjcy hcgjcy = (DwdbCtfCertificateDetailHcgjcy) mainData;
                            targetData.setBirth(hcgjcy.getDateOfBirth1());
                            targetData.setBirthEn(hcgjcy.getDateOfBirth2());
                            targetData.setNameEn(hcgjcy.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hcgjcy.getNationality1());
                            targetData.setCountryEn(hcgjcy.getNationality2());
                            targetData.setSignDeptEn(hcgjcy.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(hcgjcy.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(hcgjcy.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailHcgjcyCapacity capacity = (DwdbCtfCertificateDetailHcgjcyCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null
                                        ? capacity.getGradwAndCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null
                                        ? capacity.getGradwAndCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("海船普通船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHcptcysrz) {
                            DwdbCtfCertificateDetailHcptcysrz hcptcysrz = (DwdbCtfCertificateDetailHcptcysrz) mainData;
                            targetData.setBirth(hcptcysrz.getDateOfBirth1());
                            targetData.setBirthEn(hcptcysrz.getDateOfBirth2());
                            targetData.setNameEn(hcptcysrz.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hcptcysrz.getNationality1());
                            targetData.setCountryEn(hcptcysrz.getNationality2());
                            targetData.setSignDeptEn(hcptcysrz.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(hcptcysrz.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(hcptcysrz.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailHcptcysrzCapacity capacity = (DwdbCtfCertificateDetailHcptcysrzCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null
                                        ? capacity.getGradwAndCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null
                                        ? capacity.getGradwAndCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("小型海船适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailXhcsrz) {
                            DwdbCtfCertificateDetailXhcsrz xhcsrz = (DwdbCtfCertificateDetailXhcsrz) mainData;
                            targetData.setBirth(xhcsrz.getDateOfBirth1());
                            targetData.setBirthEn(xhcsrz.getDateOfBirth2());
                            targetData.setNameEn(xhcsrz.getFullNameOfTheHolder2());
                            targetData.setCountryCn(xhcsrz.getNationality1());
                            targetData.setCountryEn(xhcsrz.getNationality2());
                            targetData.setSignDeptEn(xhcsrz.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(xhcsrz.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(xhcsrz.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailXhcsrzCapacity capacity = (DwdbCtfCertificateDetailXhcsrzCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null
                                        ? capacity.getGradwAndCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null
                                        ? capacity.getGradwAndCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员适任证书承认签证")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHccycr) {
                            DwdbCtfCertificateDetailHccycr hccycr = (DwdbCtfCertificateDetailHccycr) mainData;
                            targetData.setBirth(hccycr.getDateOfBirth1());
                            targetData.setBirthEn(hccycr.getDateOfBirth2());
                            targetData.setNameEn(hccycr.getHolderName2());
                            targetData.setCountryCn(hccycr.getNationality1());
                            targetData.setCountryEn(hccycr.getNationality2());
                            targetData.setSignDeptEn(hccycr.getIssuingAminstration2());
                            targetData.setCertificateIssuedDateEn(hccycr.getIssuedOn2());
                            targetData.setCertificateExpiringDateEn(hccycr.getDateOfExpiry2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailHccycrCapacity capacity = (DwdbCtfCertificateDetailHccycrCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()
                                        : "";

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("不参加航行和轮机值班海船船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailBcjhljzb) {
                            DwdbCtfCertificateDetailBcjhljzb bcjhljzb = (DwdbCtfCertificateDetailBcjhljzb) mainData;
                            targetData.setBirth(bcjhljzb.getDateOfBirth1());
                            targetData.setBirthEn(bcjhljzb.getDateOfBirth2());
                            targetData.setNameEn(bcjhljzb.getFullNameoftheHolder2());
                            targetData.setCountryCn(bcjhljzb.getNationality1());
                            targetData.setCountryEn(bcjhljzb.getNationality2());
                            targetData.setSignDeptEn(bcjhljzb.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(bcjhljzb.getCertificateIssuedDate2());
                            targetData.setCertificateExpiringDateEn(bcjhljzb.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailBcjhljzbCapacity capacity = (DwdbCtfCertificateDetailBcjhljzbCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("海上非自航船舶船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHsfhcysrz) {
                            DwdbCtfCertificateDetailHsfhcysrz hsfhcysrz = (DwdbCtfCertificateDetailHsfhcysrz) mainData;
                            targetData.setBirth(hsfhcysrz.getDateOfBirth());
                            targetData.setCountryCn(hsfhcysrz.getPlaceOfBirth());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("shipList");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailHsfhcysrzShip capacity = (DwdbCtfCertificateDetailHsfhcysrzShip) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加职务等级信息
                                crewTypeBuilder.append(capacity.getCapacity() != null ? capacity.getCapacity() : "");
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("引航员船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailYhysrz) {
                            DwdbCtfCertificateDetailYhysrz yhysrz = (DwdbCtfCertificateDetailYhysrz) mainData;
                            targetData.setBirth(yhysrz.getDateOfBirth1());
                            targetData.setBirthEn(yhysrz.getDateOfBirth2());
                            targetData.setNameEn(yhysrz.getFullNameOfTheHolder2());
                            targetData.setCountryCn(yhysrz.getNationality1());
                            targetData.setCountryEn(yhysrz.getNationality2());
                            targetData.setSignDeptEn(yhysrz.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(yhysrz.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(yhysrz.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> rangeList = convertResult.getSubTableDataList("rangeList");
                        if (rangeList != null && !rangeList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : rangeList) {
                                DwdbCtfCertificateDetailYhysrzRange range = (DwdbCtfCertificateDetailYhysrzRange) obj;

                                // 获取原始职务（组合type和level）
                                String type1 = range.getType1() != null ? range.getType1() : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(type1)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                String level1 = range.getLevel1() != null ? range.getLevel1() : "";
                                String type2 = range.getType2() != null ? range.getType2() : "";
                                String level2 = range.getLevel2() != null ? range.getLevel2() : "";

                                String originalCapacityCn = type1 + level1;
                                String originalCapacityEn = type2 + level2;

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("公务船船员适任证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailGwccy) {
                            DwdbCtfCertificateDetailGwccy gwccy = (DwdbCtfCertificateDetailGwccy) mainData;
                            targetData.setBirth(gwccy.getDateOfBirth1());
                            targetData.setBirthEn(gwccy.getDateOfBirth2());
                            targetData.setNameEn(gwccy.getFullNameoftheHolder2());
                            targetData.setCountryCn(gwccy.getNationality1());
                            targetData.setCountryEn(gwccy.getNationality2());
                            targetData.setSignDeptEn(gwccy.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(gwccy.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(gwccy.getCertificateExpiringDate2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacities");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertificateDetailGwccyCapacity capacity = (DwdbCtfCertificateDetailGwccyCapacity) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null
                                        ? capacity.getGradwAndCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null
                                        ? capacity.getGradwAndCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 从职务映射缓存中查找目标职务
                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);
                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);

                                // 添加职务等级信息
                                crewTypeBuilder.append(mappedCapacityCn);
                                crewTypeEnBuilder.append(mappedCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("内河船舶船员培训合格证")
                            || sourceData.getCatalogname().equals("内河船舶船员特殊培训合格证")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailNhpxhg) {
                            DwdbCtfCertificateDetailNhpxhg nhpxhg = (DwdbCtfCertificateDetailNhpxhg) mainData;
                            targetData.setCertPrintNo(nhpxhg.getPrintNo());
                        }

                        // 处理培训项目信息
                        List<?> nhpxhgItems = convertResult.getSubTableDataList("nhpxhgItems");
                        if (nhpxhgItems != null && !nhpxhgItems.isEmpty()) {
                            StringBuilder trainingNamesCnBuilder = new StringBuilder();
                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();
                            StringBuilder trainingEffectiveDatesCnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : nhpxhgItems) {
                                DwdbCtfCertificateDetailNhpxhgItem item = (DwdbCtfCertificateDetailNhpxhgItem) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    trainingNamesCnBuilder.append("#");
                                    trainingIssueDatesCnBuilder.append("#");
                                    trainingEffectiveDatesCnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加培训项目信息
                                trainingNamesCnBuilder.append(item.getProject() != null ? item.getProject() : "");
                                trainingIssueDatesCnBuilder
                                        .append(item.getSignDate() != null ? item.getSignDate() : "");
                                trainingEffectiveDatesCnBuilder
                                        .append(item.getEndDate() != null ? item.getEndDate() : "");
                            }

                            // 设置到主表对象中
                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());
                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());
                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCnBuilder.toString());
                        }

                        // 从身份证号提取出生年月日
                        String idCard = targetData.getCertificateHolderCode();
                        if (StringUtils.isNotBlank(idCard) && idCard.length() == 18) {
                            try {
                                String birthYear = idCard.substring(6, 10);
                                String birthMonth = idCard.substring(10, 12);
                                String birthDay = idCard.substring(12, 14);
                                String birthDate = birthYear + "-" + birthMonth + "-" + birthDay;
                                targetData.setBirth(birthDate);
                                // log.info("从身份证号{}中提取出生日期: {}", idCard, birthDate);
                            } catch (Exception e) {
                                log.warn("从身份证号{}提取出生日期失败: {}", idCard, e.getMessage());
                            }
                        }
                        targetData.setCertificateName("内河船舶船员培训合格证");
                    } else if (sourceData.getCatalogname().equals("特定航线江海直达船舶船员行驶资格证明培训合格证")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailTdhxjh) {
                            DwdbCtfCertificateDetailTdhxjh tdhxjh = (DwdbCtfCertificateDetailTdhxjh) mainData;
                            targetData.setBirth(tdhxjh.getDateOfBirth());
                            targetData.setApplivationsCn(tdhxjh.getApplivations());
                            targetData.setCertificateExpiringDateEn(tdhxjh.getIssuingDate());// 初次签发日期
                        }
                    } else if (sourceData.getCatalogname().equals("船上厨师培训合格证明")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailCscspx) {
                            DwdbCtfCertificateDetailCscspx cscspx = (DwdbCtfCertificateDetailCscspx) mainData;
                            targetData.setBirth(cscspx.getDateOfBirth1());
                            targetData.setBirthEn(cscspx.getDateOfBirth2());
                            targetData.setNameEn(cscspx.getFullNameOfTheHolder2());
                            targetData.setCountryCn(cscspx.getNationality1());
                            targetData.setCountryEn(cscspx.getNationality2());
                            targetData.setSignDeptEn(cscspx.getIssuingBody2());
                            targetData.setCertificateIssuedDateEn(cscspx.getDateOfIssue2());
                            targetData.setTrainManagerNameCn(cscspx.getNameOfTheTraingManager1());
                            targetData.setTrainManagerNameEn(cscspx.getNameOfTheTraingManager2());
                        }
                    } else if (sourceData.getCatalogname().equals("船上膳食服务辅助人员培训证明")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailCsssfzpx) {
                            DwdbCtfCertificateDetailCsssfzpx csssfzpx = (DwdbCtfCertificateDetailCsssfzpx) mainData;
                            targetData.setBirth(csssfzpx.getDateOfBirth1());
                            targetData.setBirthEn(csssfzpx.getDateOfBirth2());
                            targetData.setNameEn(csssfzpx.getFullNameOfTheHolder2());
                            targetData.setCountryCn(csssfzpx.getNationality1());
                            targetData.setCountryEn(csssfzpx.getNationality2());
                            targetData.setSignDeptEn(csssfzpx.getIssuingBody2());
                            targetData.setCertificateIssuedDateEn(csssfzpx.getDateOfIssue2());
                            targetData.setTrainManagerNameCn(csssfzpx.getNameOfTheTraingManager1());
                            targetData.setTrainManagerNameEn(csssfzpx.getNameOfTheTraingManager2());
                        }
                    } else if (sourceData.getCatalogname().equals("海上设施工作人员海上交通安全技能培训合格证明")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHsssjn) {
                            DwdbCtfCertificateDetailHsssjn hsssjn = (DwdbCtfCertificateDetailHsssjn) mainData;
                            targetData.setBirth(hsssjn.getDateOfBirth1());
                            targetData.setBirthEn(hsssjn.getDateOfBirth2());
                            targetData.setNameEn(hsssjn.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hsssjn.getNationality1());
                            targetData.setCountryEn(hsssjn.getNationality2());
                            targetData.setTrainingNamesCn("基础培训（消防、救生）#专业培训（避碰、信号、通信）");
                            targetData.setTrainingIssueDatesCn(
                                    hsssjn.getAnThorityName1() + "#" + hsssjn.getAnThorityName2());
                            targetData.setTrainingEffectiveDatesCn("长期#" + hsssjn.getYear1() + "年" + hsssjn.getMonth1()
                                    + "月" + hsssjn.getDay1() + "日");
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员健康证明")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailJkzm) {
                            DwdbCtfCertificateDetailJkzm jkzm = (DwdbCtfCertificateDetailJkzm) mainData;
                            targetData.setBirth(jkzm.getDateOfBirth1());
                            targetData.setBirthEn(jkzm.getDateOfBirth2());
                            targetData.setNameEn(jkzm.getFullNameOfTheHolder2());
                            targetData.setCountryCn(jkzm.getNationality1());
                            targetData.setCountryEn(jkzm.getNationality2());
                            targetData.setSignDeptEn(jkzm.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(jkzm.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(jkzm.getCertificateExpiringDate2());
                            targetData.setAuthAuthorityCn(jkzm.getAuthorizingAuthority1());
                            targetData.setAuthAuthorityEn(jkzm.getAuthorizingAuthority2());
                        }
                    } else if (sourceData.getCatalogname().equals("船员培训质量管理体系证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailQms) {
                            DwdbCtfCertificateDetailQms qms = (DwdbCtfCertificateDetailQms) mainData;
                            targetData.setNameEn(qms.getFullNameOfTheHolder2());
                            targetData.setSignDeptEn(qms.getEvaluationOrganization2());
                            targetData.setCertificateIssuedDateEn(qms.getDateOfIssue2());
                            targetData.setCertificateExpiringDateEn(qms.getCertificateExpiringDate());
                            targetData.setEvaOrgCn(qms.getEvaluationOrganization1());
                            targetData.setEvaOrgEn(qms.getEvaluationOrganization2());
                        }
                    } else if (sourceData.getCatalogname().equals("海员外派机构资质证书")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailHywpjg) {
                            DwdbCtfCertificateDetailHywpjg hywpjg = (DwdbCtfCertificateDetailHywpjg) mainData;
                            targetData.setNameEn(hywpjg.getAnThorityName2());
                            targetData.setSignDeptEn(hywpjg.getIssuingAuthority2());
                            targetData.setCertificateIssuedDateEn(hywpjg.getDateOfIssue3());
                            targetData.setCertificateExpiringDateEn(hywpjg.getExpiryDate2());
                            targetData.setRepresentativeCn(hywpjg.getRepresentative1());
                            targetData.setRepresentativeEn(hywpjg.getRepresentative2());
                            targetData.setEvaOrgCn(hywpjg.getAnThorityName1()); // 机构名称-中文
                            targetData.setEvaOrgEn(hywpjg.getAnThorityName2()); // 机构名称-英文
                            targetData.setAuthAuthorityCn(hywpjg.getAddress1());// 机构地址-中文
                            targetData.setAuthAuthorityEn(hywpjg.getAddress2());// 机构地址-英文
                            targetData.setApplivationsCn("为外国籍或港澳台地区籍船舶提供配员。");// 服务范围
                            targetData.setApplivationsEn(
                                    "Crew manning for Vessels flying flags of foreign countries or Hong Kong, Macao and Taiwan.");
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员培训许可证")) {
                        // 从主表数据中提取字段到targetData
                        List<?> lists = convertResult.getSubTableDataList("seamanPermit");
                        Object mainData = lists.get(0);
                        if (mainData instanceof DwdbCtfCertificateDetailSeamanPermit) {
                            DwdbCtfCertificateDetailSeamanPermit permit = (DwdbCtfCertificateDetailSeamanPermit) mainData;
                            targetData.setRepresentativeCn(permit.getRepresentative1());
                            targetData.setTrainingEffectiveDatesCn(
                                    permit.getTrainingProgram1() + permit.getTrainingProgram2());
                            targetData.setTrainingInstitutionCode(permit.getTrainingInstitutionCode1());
                            targetData.setTrainingLocation(permit.getTrainingLocation1());
                        }

                        // 处理培训项目信息
                        List<?> itemList = convertResult.getSubTableDataList("itemList");
                        if (itemList != null && !itemList.isEmpty()) {
                            StringBuilder trainingNamesCnBuilder = new StringBuilder();
                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : itemList) {
                                DwdbCtfCertificateDetailSeamanPermitItem item = (DwdbCtfCertificateDetailSeamanPermitItem) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    trainingNamesCnBuilder.append("#");
                                    trainingIssueDatesCnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加培训项目信息
                                trainingNamesCnBuilder
                                        .append(item.getAtrainingProgram() != null ? item.getAtrainingProgram() : "");
                                trainingIssueDatesCnBuilder
                                        .append(item.getTrainingScale() != null ? item.getTrainingScale() : "");
                            }

                            // 设置到主表对象中
                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());
                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("内河船员培训许可证")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertificateDetailNhcyxkz) {
                            DwdbCtfCertificateDetailNhcyxkz nhcyxkz = (DwdbCtfCertificateDetailNhcyxkz) mainData;
                            targetData.setRepresentativeCn(nhcyxkz.getRepresentative1());
                            targetData.setTrainingEffectiveDatesCn(
                                    nhcyxkz.getTrainingProgram1() + nhcyxkz.getTrainingProgram2());
                            targetData.setTrainingInstitutionCode(nhcyxkz.getTrainingInstitutionCode1());
                            targetData.setTrainingLocation(nhcyxkz.getTrainingLocation1());
                        }

                        // 处理培训项目信息
                        List<?> itemList = convertResult.getSubTableDataList("itemList");
                        if (itemList != null && !itemList.isEmpty()) {
                            StringBuilder trainingNamesCnBuilder = new StringBuilder();
                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : itemList) {
                                DwdbCtfCertificateDetailNhcyxkzItem item = (DwdbCtfCertificateDetailNhcyxkzItem) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    trainingNamesCnBuilder.append("#");
                                    trainingIssueDatesCnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加培训项目信息
                                trainingNamesCnBuilder
                                        .append(item.getAtrainingProgram() != null ? item.getAtrainingProgram() : "");
                                trainingIssueDatesCnBuilder
                                        .append(item.getTrainingScale() != null ? item.getTrainingScale() : "");
                            }

                            // 设置到主表对象中
                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());
                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员培训合格证承认签证")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertDetailHcpxhgqz) {
                            DwdbCtfCertDetailHcpxhgqz hcpxhgqz = (DwdbCtfCertDetailHcpxhgqz) mainData;
                            targetData.setBirth(hcpxhgqz.getDateOfBirth1());
                            targetData.setBirthEn(hcpxhgqz.getDateOfBirth2());
                            targetData.setNameEn(hcpxhgqz.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hcpxhgqz.getNationality1());
                            targetData.setCountryEn(hcpxhgqz.getNationality2());
                            targetData.setSignDeptEn(hcpxhgqz.getIssuingAminstration2());
                            targetData.setCertificateIssuedDateEn(hcpxhgqz.getIssuedOn2());
                            targetData.setCertificateExpiringDateEn(hcpxhgqz.getDateOfExpiry2());
                        }

                        // 处理培训项目信息
                        List<?> trainingList = convertResult.getSubTableDataList("trainingList");
                        if (trainingList != null && !trainingList.isEmpty()) {
                            StringBuilder trainingNamesCnBuilder = new StringBuilder();
                            StringBuilder trainingNamesEnBuilder = new StringBuilder();
                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();
                            StringBuilder trainingIssueDatesEnBuilder = new StringBuilder();
                            StringBuilder trainingEffectiveDatesCnBuilder = new StringBuilder();
                            StringBuilder trainingEffectiveDatesEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : trainingList) {
                                DwdbCtfCertDetailHcpxqzTrain training = (DwdbCtfCertDetailHcpxqzTrain) obj;

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    trainingNamesCnBuilder.append("#");
                                    trainingNamesEnBuilder.append("#");
                                    trainingIssueDatesCnBuilder.append("#");
                                    trainingIssueDatesEnBuilder.append("#");
                                    trainingEffectiveDatesCnBuilder.append("#");
                                    trainingEffectiveDatesEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加培训项目信息
                                trainingNamesCnBuilder.append(training.getTitleOfTheCertificate1() != null
                                        ? training.getTitleOfTheCertificate1()
                                        : "");
                                trainingNamesEnBuilder.append(training.getTitleOfTheCertificate2() != null
                                        ? training.getTitleOfTheCertificate2()
                                        : "");
                                trainingIssueDatesCnBuilder.append(
                                        training.getCertificateNo1() != null ? training.getCertificateNo1() : "");
                                trainingIssueDatesEnBuilder.append(
                                        training.getCertificateNo2() != null ? training.getCertificateNo2() : "");
                                trainingEffectiveDatesCnBuilder
                                        .append(training.getDateOfExpiry3() != null ? training.getDateOfExpiry3() : "");
                                trainingEffectiveDatesEnBuilder
                                        .append(training.getDateOfExpiry4() != null ? training.getDateOfExpiry4() : "");
                            }

                            // 设置到主表对象中
                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());
                            targetData.setTrainingNamesEn(trainingNamesEnBuilder.toString());
                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());
                            targetData.setTrainingIssueDatesEn(trainingIssueDatesEnBuilder.toString());
                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCnBuilder.toString());
                            targetData.setTrainingEffectiveDatesEn(trainingEffectiveDatesEnBuilder.toString());
                        }
                    } else if (sourceData.getCatalogname().equals("海船船员特免证明")) {
                        // 从主表数据中提取字段到targetData
                        Object mainData = convertResult.getMainTableData();
                        if (mainData instanceof DwdbCtfCertDetailHccytm) {
                            DwdbCtfCertDetailHccytm hccytm = (DwdbCtfCertDetailHccytm) mainData;
                            targetData.setBirth(hccytm.getDateOfBirth1());
                            targetData.setBirthEn(hccytm.getDateOfBirth2());
                            targetData.setNameEn(hccytm.getFullNameOfTheHolder2());
                            targetData.setCountryCn(hccytm.getNationality1());
                            targetData.setCountryEn(hccytm.getNationality2());
                            targetData.setSignDeptEn(hccytm.getIssuingAminstration2());
                            targetData.setCertificateIssuedDateEn(hccytm.getIssuedOn2());
                            targetData.setCertificateExpiringDateEn(hccytm.getDateOfExpiry2());
                        }

                        // 处理职务等级信息
                        List<?> capacityList = convertResult.getSubTableDataList("capacityList");
                        if (capacityList != null && !capacityList.isEmpty()) {
                            StringBuilder crewTypeBuilder = new StringBuilder();
                            StringBuilder crewTypeEnBuilder = new StringBuilder();

                            boolean isFirst = true;
                            for (Object obj : capacityList) {
                                DwdbCtfCertDetailHccytmCap capacity = (DwdbCtfCertDetailHccytmCap) obj;

                                // 获取原始职务
                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()
                                        : "";
                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()
                                        : "";

                                // 如果中文职务是"空白"，则跳过此记录
                                if ("空白".equals(originalCapacityCn)) {
                                    continue;
                                }

                                // 不是第一个元素时添加分隔符
                                if (!isFirst) {
                                    crewTypeBuilder.append("#");
                                    crewTypeEnBuilder.append("#");
                                } else {
                                    isFirst = false;
                                }

                                // 添加职务等级信息（直接使用，不进行映射）
                                crewTypeBuilder.append(originalCapacityCn);
                                crewTypeEnBuilder.append(originalCapacityEn);
                            }

                            // 设置到主表对象中
                            targetData.setCrewType(crewTypeBuilder.toString());
                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());
                        }
                    }

                    // 收集主表数据
                    if (convertResult.getMainTableData() != null) {
                        // 根据证书类型分类存储主表数据
                        String certificateType = convertResult.getCertificateType();
                        if (!subTableDataMap.containsKey(certificateType + "_main")) {
                            subTableDataMap.put(certificateType + "_main", new ArrayList<>());
                        }
                        subTableDataMap.get(certificateType + "_main").add(convertResult.getMainTableData());
                    }

                    // 收集子表数据
                    for (Map.Entry<String, List<?>> entry : convertResult.getSubTableDataMap().entrySet()) {
                        String key = convertResult.getCertificateType() + "_" + entry.getKey();
                        if (!subTableDataMap.containsKey(key)) {
                            subTableDataMap.put(key, new ArrayList<>());
                        }
                        subTableDataMap.get(key).addAll(entry.getValue());

                        // log.info("开始处理子表数据, key: {}, valeu:{}", key, entry.getValue());

                    }

                    // 收集未使用的属性(不再直接保存)
                    if (convertResult.hasUnusedAttributes()) {
                        unusedAttributesList.addAll(convertResult.getUnusedAttributes());
                    }

                    // 记录成功处理的数据
                    result.addSuccess(sourceData.getDataid());
                } else {
                    // 记录失败处理的数据
                    result.addFail(sourceData.getDataid(), convertResult.getErrorMessage());
                    log.error("数据转换失败, sourceData: {}, error: {}",
                            sourceData.getDataid(), convertResult.getErrorMessage());

                    // 将转换失败的数据写入重处理表
                    saveErrorRecord(sourceData, convertResult.getErrorMessage());
                }
            } catch (Exception e) {
                // 记录失败处理的数据
                result.addFail(sourceData.getDataid(), e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()));
                log.error("数据处理异常, sourceData: " + sourceData.getDataid(), e);

                // 将处理异常的数据写入重处理表
                saveErrorRecord(sourceData, e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()));
            }
        }

        // 批量插入转换后的数据
        if (!targetDataList.isEmpty()) {
            try {
                // 尝试批量插入
                // dwdbCertificateDataMapper.batchInsert(targetDataList);
                batchInsertMainData(targetDataList);
            } catch (Exception e) {
                log.error("批量插入失败,尝试单条插入", e);

                // 批量插入失败,改为逐条插入
                for (DwdbCertificateData targetData : targetDataList) {
                    try {
                        // 修改这行:
                        // dwdbCertificateDataMapper.insert(targetData);
                        // 改为:
                        List<DwdbCertificateData> singleDataList = new ArrayList<>();
                        singleDataList.add(targetData);
                        batchInsertMainData(singleDataList);
                    } catch (Exception ex) {
                        log.error("单条插入失败, dataId: " + targetData.getDataId(), ex);
                        result.addFail(targetData.getDataId(),
                                ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                        // 对于插入失败的记录写入重处理表
                        saveErrorRecord(targetData, ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                    }
                }
            }
        }

        // 批量保存各类子表数据
        for (Map.Entry<String, List<Object>> entry : subTableDataMap.entrySet()) {
            if (!entry.getValue().isEmpty()) {
                String tableType = entry.getKey();
                List<Object> dataList = entry.getValue();
                try {
                    // 尝试批量插入
                    batchInsertByTableType(tableType, dataList);
                } catch (Exception e) {
                    log.error("批量保存子表数据失败,尝试单条插入, tableType: " + tableType, e);

                    // 批量插入失败,改为逐条插入
                    for (Object data : dataList) {
                        try {
                            List<Object> singleDataList = new ArrayList<>();
                            singleDataList.add(data);
                            batchInsertByTableType(tableType, singleDataList);
                        } catch (Exception ex) {
                            log.error("单条插入数据失败", ex);
                            String dataId = getDataIdFromObject(data);
                            if (dataId != null) {
                                log.error("单条插入数据失败, dataId: " + dataId, ex);
                                result.addFail(dataId, ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                            }
                            saveErrorRecord(data, ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                        }
                    }
                }
            }
        }

        // 最后批量保存未使用的属性
        if (!unusedAttributesList.isEmpty()) {
            try {
                // 先尝试批量插入
                batchInsertAttributes(unusedAttributesList);
            } catch (Exception e) {
                log.error("批量插入属性失败,尝试逐条插入", e);

                // 批量插入失败,改为逐条插入
                for (DwdbCertificateDataAttribute attribute : unusedAttributesList) {
                    try {
                        List<DwdbCertificateDataAttribute> singleAttributeList = new ArrayList<>();
                        singleAttributeList.add(attribute);
                        batchInsertAttributes(singleAttributeList);
                    } catch (Exception ex) {
                        log.error("插入属性失败. dataId: " + attribute.getDataId() +
                                ", attributeName: " + attribute.getAttributeColumnName(), ex);
                        result.addFail(attribute.getDataId(),
                                ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                        // 将失败的数据写入重处理表
                        saveErrorRecord(attribute, "插入属性失败: " + attribute.getAttributeColumnName() + ", "
                                + ex.getMessage() + "\n" + Arrays.toString(ex.getStackTrace()));
                    }
                }
            }
        }

        // 批量保存新增的机构查询辅助表数据
        if (!newQueryOrgs.isEmpty()) {
            try {
                certQueryOrgMapper.batchInsert(newQueryOrgs);
                log.info("批量保存机构查询辅助表数据成功，共 {} 条", newQueryOrgs.size());
                // 清空新增列表
                newQueryOrgs.clear();
            } catch (Exception e) {
                log.error("批量保存机构查询辅助表数据失败", e);
            }
        }

        return result;
    }

    /**
     * 根据表类型执行批量插入
     * 
     * @param tableType 表类型
     * @param dataList  数据列表
     */
    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    private void batchInsertByTableType(String tableType, List<Object> dataList) {
        try {
            // 根据表类型选择对应的Mapper进行批量插入
            if (tableType.contains("_main")) {
                // 处理主表数据
                String certificateType = tableType.split("_main")[0];
                switch (certificateType) {
                    case "不参加航行和轮机值班海船船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertBcjhljzb(castList(dataList, DwdbCtfCertificateDetailBcjhljzb.class));
                        break;
                    case "公务船船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertGwccy(castList(dataList, DwdbCtfCertificateDetailGwccy.class));
                        break;
                    case "船员适任证书申请表":
                        dwdbCertificateDataMapper
                                .batchInsertCysrzsqb(castList(dataList, DwdbCtfCertificateDetailCysrzsqb.class));
                        break;
                    case "海船高级船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertHcgjcy(castList(dataList, DwdbCtfCertificateDetailHcgjcy.class));
                        break;
                    case "海船普通船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertHcptcysrz(castList(dataList, DwdbCtfCertificateDetailHcptcysrz.class));
                        break;
                    case "海船船员培训合格证书":
                        dwdbCertificateDataMapper
                                .batchInsertHcpxhg(castList(dataList, DwdbCtfCertificateDetailHcpxhg.class));
                        break;
                    case "海上非自航船舶船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertHsfhcysrz(castList(dataList, DwdbCtfCertificateDetailHsfhcysrz.class));
                        break;
                    case "海员外派机构资质证书":
                        dwdbCertificateDataMapper
                                .batchInsertHywpjg(castList(dataList, DwdbCtfCertificateDetailHywpjg.class));
                        break;
                    case "海船船员健康证明":
                        dwdbCertificateDataMapper
                                .batchInsertJkzm(castList(dataList, DwdbCtfCertificateDetailJkzm.class));
                        break;
                    case "内河船舶船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertNhcbcy(castList(dataList, DwdbCtfCertificateDetailNhcbcy.class));
                        break;
                    case "海船船员内河航线行驶资格证明":
                        dwdbCertificateDataMapper
                                .batchInsertNhhxxs(castList(dataList, DwdbCtfCertificateDetailNhhxxs.class));
                        break;
                    case "内河船舶船员培训合格证":
                    case "内河船舶船员特殊培训合格证":
                        dwdbCertificateDataMapper
                                .batchInsertNhpxhg(castList(dataList, DwdbCtfCertificateDetailNhpxhg.class));
                        break;
                    case "船员培训质量管理体系证书":
                        dwdbCertificateDataMapper.batchInsertQms(castList(dataList, DwdbCtfCertificateDetailQms.class));
                        break;
                    case "海船船员培训许可证":
                        dwdbCertificateDataMapper
                                .batchInsertSeamanInfo(castList(dataList, DwdbCtfCertificateDetailSeamanInfo.class));
                        break;
                    case "特定航线江海直达船舶船员行驶资格证明培训合格证":
                        dwdbCertificateDataMapper
                                .batchInsertTdhxjh(castList(dataList, DwdbCtfCertificateDetailTdhxjh.class));
                        break;
                    case "小型海船适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertXhcsrz(castList(dataList, DwdbCtfCertificateDetailXhcsrz.class));
                        break;
                    case "引航员船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertYhysrz(castList(dataList, DwdbCtfCertificateDetailYhysrz.class));
                        break;
                    case "游艇驾驶证":
                    case "游艇驾驶证（海上）":
                    case "游艇驾驶证（内河）":
                    case "游艇驾驶证海上":
                    case "游艇驾驶证内河":
                        dwdbCertificateDataMapper
                                .batchInsertYtjsz(castList(dataList, DwdbCtfCertificateDetailYtjsz.class));
                        break;
                    case "船上厨师培训合格证明":
                        dwdbCertificateDataMapper
                                .batchInsertCscspx(castList(dataList, DwdbCtfCertificateDetailCscspx.class));
                        break;
                    case "船上膳食服务辅助人员培训证明":
                        dwdbCertificateDataMapper
                                .batchInsertCsssfzpx(castList(dataList, DwdbCtfCertificateDetailCsssfzpx.class));
                        break;
                    case "海船船员适任证书承认签证":
                        dwdbCertificateDataMapper
                                .batchInsertHccycr(castList(dataList, DwdbCtfCertificateDetailHccycr.class));
                        break;
                    // case "海船船员适任证书承认签证_capacityList":
                    // dwdbCertificateDataMapper.batchInsertHccycrCapacity(castList(dataList,
                    // DwdbCtfCertificateDetailHccycrCapacity.class));
                    // break;
                    case "海船船员适任证书承认签证_functionList":
                        dwdbCertificateDataMapper.batchInsertHccycrFunction(
                                castList(dataList, DwdbCtfCertificateDetailHccycrFunction.class));
                        break;
                    case "海上设施工作人员海上交通安全技能培训合格证明":
                        dwdbCertificateDataMapper
                                .batchInsertHsssjn(castList(dataList, DwdbCtfCertificateDetailHsssjn.class));
                        break;
                    case "海船不参加船员适任证书":
                        dwdbCertificateDataMapper
                                .batchInsertHcbcjcy(castList(dataList, DwdbCtfCertificateDetailHcbcjcy.class));
                        break;

                    case "内河船员培训许可证": // 新增内河船员培训许可证主表的处理
                        dwdbCertificateDataMapper
                                .batchInsertNhcyxkz(castList(dataList, DwdbCtfCertificateDetailNhcyxkz.class));
                        break;

                    case "海船船员特免证明":
                        dwdbCertificateDataMapper.batchInsertHccytm(castList(dataList, DwdbCtfCertDetailHccytm.class));
                        break;

                    case "海船船员培训合格证承认签证":
                        dwdbCertificateDataMapper
                                .batchInsertHcpxhgqz(castList(dataList, DwdbCtfCertDetailHcpxhgqz.class));
                        break;

                    default:
                        log.warn("未知的证书类型: {}, 无法批量保存数据", certificateType);
                        throw new RuntimeException("未知的证书类型: " + certificateType + ", 无法批量保存数据");
                }
            } else {
                // 处理子表数据
                if (tableType.contains("_capacities")) {
                    if (tableType.contains("不参加航行和轮机值班海船船员适任证书")) {
                        // log.info("开始处理不参加航行和轮机值班海船船员适任证书的能力列表数据");
                        dwdbCertificateDataMapper.batchInsertBcjhljzbCapacity(
                                castList(dataList, DwdbCtfCertificateDetailBcjhljzbCapacity.class));
                    } else if (tableType.contains("海船高级船员适任证书")) {
                        dwdbCertificateDataMapper.batchInsertHcgjcyCapacity(
                                castList(dataList, DwdbCtfCertificateDetailHcgjcyCapacity.class));
                    } else if (tableType.contains("海船普通船员适任证书")) {
                        dwdbCertificateDataMapper.batchInsertHcptcysrzCapacity(
                                castList(dataList, DwdbCtfCertificateDetailHcptcysrzCapacity.class));
                    } else if (tableType.contains("小型海船适任证书")) {
                        dwdbCertificateDataMapper.batchInsertXhcsrzCapacity(
                                castList(dataList, DwdbCtfCertificateDetailXhcsrzCapacity.class));
                    } else if (tableType.contains("海船船员适任证书承认签证")) {
                        dwdbCertificateDataMapper.batchInsertHccycrCapacity(
                                castList(dataList, DwdbCtfCertificateDetailHccycrCapacity.class));
                    } else if (tableType.contains("海船不参加船员适任证书")) {
                        dwdbCertificateDataMapper.batchInsertHcbcjcyCapacity(
                                castList(dataList, DwdbCtfCertificateDetailHcbcjcyCapacity.class));
                    } else if (tableType.contains("公务船船员适任证书")) {
                        // log.info("开始处理公务船船员适任证书的职务等级列表数据");
                        dwdbCertificateDataMapper.batchInsertGwccyCapacity(
                                castList(dataList, DwdbCtfCertificateDetailGwccyCapacity.class));
                    } else if (tableType.contains("海船船员特免证明")) {
                        dwdbCertificateDataMapper
                                .batchInsertHccytmCapacity(castList(dataList, DwdbCtfCertDetailHccytmCap.class));
                    } else {
                        throw new RuntimeException("_capacities未知的子表类型: " + tableType + ", 无法处理数据");
                    }
                } else if (tableType.contains("_functions")) {
                    if (tableType.contains("海船高级船员适任证书")) {
                        dwdbCertificateDataMapper.batchInsertHcgjcyFunction(
                                castList(dataList, DwdbCtfCertificateDetailHcgjcyFunction.class));
                    } else if (tableType.contains("海船普通船员适任证书")) {
                        dwdbCertificateDataMapper.batchInsertHcptcysrzFunction(
                                castList(dataList, DwdbCtfCertificateDetailHcptcysrzFunction.class));
                    } else if (tableType.contains("小型海船适任证书")) {
                        dwdbCertificateDataMapper.batchInsertXhcsrzFunction(
                                castList(dataList, DwdbCtfCertificateDetailXhcsrzFunction.class));
                    } else if (tableType.contains("海船船员适任证书承认签证")) {
                        dwdbCertificateDataMapper.batchInsertHccycrFunction(
                                castList(dataList, DwdbCtfCertificateDetailHccycrFunction.class));
                    } else if (tableType.contains("海船船员特免证明")) {
                        dwdbCertificateDataMapper
                                .batchInsertHccytmFunction(castList(dataList, DwdbCtfCertDetailHccytmFunc.class));
                    } else {
                        throw new RuntimeException("_functions未知的子表类型: " + tableType + ", 无法处理数据");
                    }
                } else if (tableType.contains("_shipList")) {
                    dwdbCertificateDataMapper
                            .batchInsertHsfhcysrzShip(castList(dataList, DwdbCtfCertificateDetailHsfhcysrzShip.class));
                } else if (tableType.contains("_experienceList")) {
                    dwdbCertificateDataMapper.batchInsertCysrzsqbExperience(
                            castList(dataList, DwdbCtfCertificateDetailCysrzsqbExperience.class));
                } else if (tableType.contains("_optionsList")) {
                    dwdbCertificateDataMapper.batchInsertCysrzsqbOptions(
                            castList(dataList, DwdbCtfCertificateDetailCysrzsqbOptions.class));
                } else if (tableType.contains("_nhpxhgItems")) {
                    dwdbCertificateDataMapper
                            .batchInsertNhpxhgItem(castList(dataList, DwdbCtfCertificateDetailNhpxhgItem.class));
                } else if (tableType.contains("_seamanPermit")) {
                    // log.info("开始处理船员培训许可证数据");
                    dwdbCertificateDataMapper
                            .batchInsertSeamanPermit(castList(dataList, DwdbCtfCertificateDetailSeamanPermit.class));
                } else if (tableType.contains("_itemList")) {
                    if (tableType.contains("海船船员培训许可证")) {
                        dwdbCertificateDataMapper.batchInsertSeamanPermitItem(
                                castList(dataList, DwdbCtfCertificateDetailSeamanPermitItem.class));
                    } else if (tableType.contains("内河船员培训许可证")) { // 新增内河船员培训许可证子表的处理
                        dwdbCertificateDataMapper
                                .batchInsertNhcyxkzItems(castList(dataList, DwdbCtfCertificateDetailNhcyxkzItem.class));
                    }
                } else if (tableType.contains("_rangeList")) {
                    if (tableType.contains("引航员船员适任证书")) {
                        dwdbCertificateDataMapper
                                .batchInsertYhysrzRange(castList(dataList, DwdbCtfCertificateDetailYhysrzRange.class));
                    } else {
                        throw new RuntimeException("rangeList 未知的子表类型: " + tableType + ", 无法处理数据");
                    }
                } else if (tableType.contains("_trainingList")) {
                    if (tableType.contains("海船船员培训合格证书")) {
                        // log.info("开始处理海船船员培训合格证书的培训列表数据");
                        dwdbCertificateDataMapper.batchInsertHcpxhgTraining(
                                castList(dataList, DwdbCtfCertificateDetailHcpxhgTraining.class));
                    } else if (tableType.contains("内河船舶船员培训合格证") || tableType.contains("内河船舶船员特殊培训合格证")) {
                        // log.info("开始处理内河船舶船员培训合格证的培训列表数据");
                        dwdbCertificateDataMapper
                                .batchInsertNhpxhgItem(castList(dataList, DwdbCtfCertificateDetailNhpxhgItem.class));
                    } else if (tableType.contains("海船船员培训合格证承认签证")) {
                        // log.info("开始处理海船船员培训合格证承认签证的培训列表数据");
                        dwdbCertificateDataMapper
                                .batchInsertHcpxqzTrain(castList(dataList, DwdbCtfCertDetailHcpxqzTrain.class));
                    }
                } else {
                    throw new RuntimeException("未知的子表类型: " + tableType + ", 无法处理数据");
                }
            }
        } catch (Exception e) {
            log.error("批量插入失败，准备重试: {}", e.getMessage());
            throw e;
        }
    }

    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public void batchInsertMainData(List<DwdbCertificateData> targetDataList) {
        try {
            dwdbCertificateDataMapper.batchInsert(targetDataList);
        } catch (Exception e) {
            log.error("批量插入主表数据失败，准备重试: {}", e.getMessage());
            throw e;
        }
    }

    @Recover
    public void recoverBatchInsert(Exception e, List<DwdbCertificateData> targetDataList) {
        log.error("主表数据批量插入重试3次后仍然失败，需要人工介入处理: {}", e.getMessage());
        // 可以添加告警逻辑
    }

    /**
     * 保存错误记录
     * 
     * @param data     数据对象
     * @param errorMsg 错误信息
     */
    private void saveErrorRecord(Object data, String errorMsg) {
        try {
            // 获取数据ID
            String dataId = getDataIdFromObject(data);
            if (dataId == null) {
                log.error("无法获取数据ID,无法保存到重处理表");
                return;
            }

            // 检查重处理表中是否已存在该记录
            if (dwdbCertificateDataMapper.existsInRedoTable(dataId)) {
                log.info("数据已存在于重处理表中,跳过插入, dataId: {}", dataId);
                return;
            }

            // 查询原始数据
            OdsCertificateData originalData = findOriginalData(dataId);
            if (originalData == null) {
                log.error("无法找到原始数据,dataId: {}", dataId);
                return;
            }

            // 保存到重处理表
            dwdbCertificateDataMapper.insertRedoData(
                    originalData.getDataid(),
                    originalData.getCertificateid(),
                    originalData.getCatalogid(),
                    originalData.getCatalogname(),
                    originalData.getTemplateid(),
                    originalData.getCertificatetype(),
                    originalData.getCertificatetypecode(),
                    originalData.getIssuedept(),
                    originalData.getIssuedeptcode(),
                    originalData.getCertificateareacode(),
                    originalData.getCertificateholder(),
                    originalData.getCertificateholdercode(),
                    originalData.getCertificateholdertype(),
                    originalData.getCertificatenumber(),
                    originalData.getIssuedate(),
                    originalData.getValidbegindate(),
                    originalData.getValidenddate(),
                    originalData.getSurfacedata(),
                    originalData.getStatus(),
                    originalData.getCreator(),
                    originalData.getCreatetime(),
                    originalData.getOperator(),
                    originalData.getUpdatetime(),
                    originalData.getFilepath(),
                    originalData.getSyncstatus(),
                    originalData.getRemarks(),
                    originalData.getDeptid(),
                    originalData.getApplynum(),
                    originalData.getAffairname(),
                    originalData.getAffairtype(),
                    originalData.getServebusiness(),
                    originalData.getAffairid(),
                    originalData.getAffairnum(),
                    originalData.getQztype(),
                    originalData.getZztype(),
                    originalData.getDrafturl(),
                    originalData.getIsview(),
                    originalData.getSortname(),
                    originalData.getCol1(),
                    originalData.getVerifydate(),
                    originalData.getVerification(),
                    originalData.getCreditcode(),
                    originalData.getSealname(),
                    originalData.getFcdcDate(),
                    "0", // redo_status 设为0表示待处理
                    errorMsg // 错误原因
            );

            log.info("数据已添加到重处理表, dataId: {}", dataId);
        } catch (Exception e) {
            log.error("保存到重处理表失败", e);
        }
    }

    /**
     * 根据数据ID查找原始数据
     * 
     * @param dataId 数据ID
     * @return 原始数据
     */
    private OdsCertificateData findOriginalData(String dataId) {
        try {
            return odsCertificateDataMapper.selectById(dataId);
        } catch (Exception e) {
            log.error("查询原始数据失败, dataId: " + dataId, e);
            return null;
        }
    }

    /**
     * 从对象中获取数据ID
     * 
     * @param obj 对象
     * @return 数据ID
     */
    private String getDataIdFromObject(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            // 尝试获取 dataId 字段
            if (obj instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) obj;
                if (map.containsKey("dataId")) {
                    return String.valueOf(map.get("dataId"));
                } else if (map.containsKey("dataid")) {
                    return String.valueOf(map.get("dataid"));
                }
            } else {
                // 使用反射获取字段值
                try {
                    java.lang.reflect.Method getDataId = obj.getClass().getMethod("getDataId");
                    Object result = getDataId.invoke(obj);
                    if (result != null) {
                        return result.toString();
                    }
                } catch (NoSuchMethodException e) {
                    // 尝试获取 dataid 字段
                    try {
                        java.lang.reflect.Method getDataid = obj.getClass().getMethod("getDataid");
                        Object result = getDataid.invoke(obj);
                        if (result != null) {
                            return result.toString();
                        }
                    } catch (NoSuchMethodException ex) {
                        // 两个方法都不存在
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取数据ID失败", e);
        }

        return null;
    }

    /**
     * 类型转换辅助方法
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> castList(List<?> sourceList, Class<T> clazz) {
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (Object obj : sourceList) {
            targetList.add((T) obj);
        }
        return targetList;
    }

    /**
     * 处理结果类
     */
    private static class ProcessResult {
        private Map<String, Boolean> processResults = new HashMap<>();
        private Map<String, String> failReasons = new HashMap<>();
        private int successCount = 0;
        private int failCount = 0;

        public void addSuccess(String dataId) {
            processResults.put(dataId, true);
            successCount++;
        }

        public void addFail(String dataId, String reason) {
            processResults.put(dataId, false);
            failReasons.put(dataId, reason);
            failCount++;
        }

        public boolean isSuccess(String dataId) {
            return processResults.getOrDefault(dataId, false);
        }

        public String getFailReason(String dataId) {
            return failReasons.get(dataId);
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailCount() {
            return failCount;
        }
    }

    /**
     * 获取数据列表中的最大fcdcDate
     * 
     * @param dataList 数据列表
     * @return 最大的fcdcDate
     */
    private LocalDateTime getMaxFcdcDate(List<OdsCertificateData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        LocalDateTime maxDate = null;
        for (OdsCertificateData data : dataList) {
            LocalDateTime fcdcDate = data.getFcdcDate();
            if (fcdcDate != null) {
                if (maxDate == null || fcdcDate.isAfter(maxDate)) {
                    maxDate = fcdcDate;
                }
            }
        }

        return maxDate;
    }

    /**
     * 获取数据列表中最后一条数据的ID
     * 
     * @param dataList 数据列表
     * @return 最后一条数据的ID
     */
    private String getLastDataId(List<OdsCertificateData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return null;
        }

        // 按照fcdcDate排序,获取最后一条数据的ID
        OdsCertificateData lastData = null;
        LocalDateTime maxDate = null;

        for (OdsCertificateData data : dataList) {
            LocalDateTime fcdcDate = data.getFcdcDate();
            if (fcdcDate != null) {
                if (maxDate == null || fcdcDate.isAfter(maxDate)) {
                    maxDate = fcdcDate;
                    lastData = data;
                }
            }
        }

        return lastData != null ? lastData.getDataid() : null;
    }

    /**
     * 清理目标表中的数据
     * 
     * @param sourceData 源数据
     */
    private void cleanupTargetData(OdsCertificateData sourceData) {
        String dataId = sourceData.getDataid();
        String certificateType = sourceData.getCatalogname();

        // 新增逻辑：通过certificateId查询dataId
        String certificateId = sourceData.getCertificateid();
        if (StringUtils.isNotBlank(certificateId)) {
            List<String> existingDataIds = dwdbCertificateDataMapper.findDataIdsByCertificateId(certificateId);
            if (!CollectionUtils.isEmpty(existingDataIds)) {
                // 如果找到了记录，使用第一条记录的dataId
                log.info("原来的dataId变量: {}", dataId);
                dataId = existingDataIds.get(0);
                log.info("通过certificateId={}找到已存在记录，使用dataId={}", certificateId, dataId);
            }
        }

        try {
            // 1. 删除主表数据
            dwdbCertificateDataMapper.deleteByDataId(dataId);

            // 2. 根据证书类型删除对应的明细表数据
            switch (certificateType) {
                case "不参加航行和轮机值班海船船员适任证书":
                    dwdbCertificateDataMapper.deleteBcjhljzbCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteBcjhljzbByDataId(dataId);
                    break;
                case "公务船船员适任证书":
                    dwdbCertificateDataMapper.deleteGwccyCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteGwccyByDataId(dataId);
                    break;
                case "船员适任证书申请表":
                    dwdbCertificateDataMapper.deleteCysrzsqbExperienceByDataId(dataId);
                    dwdbCertificateDataMapper.deleteCysrzsqbOptionsByDataId(dataId);
                    dwdbCertificateDataMapper.deleteCysrzsqbByDataId(dataId);
                    break;
                case "海船高级船员适任证书":
                    dwdbCertificateDataMapper.deleteHcgjcyCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcgjcyFunctionByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcgjcyByDataId(dataId);
                    break;
                case "海船普通船员适任证书":
                    dwdbCertificateDataMapper.deleteHcptcysrzCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcptcysrzFunctionByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcptcysrzByDataId(dataId);
                    break;
                case "海船船员培训合格证书":
                    dwdbCertificateDataMapper.deleteHcpxhgTrainingByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcpxhgByDataId(dataId);
                    break;
                case "海上非自航船舶船员适任证书":
                    dwdbCertificateDataMapper.deleteHsfhcysrzShipByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHsfhcysrzByDataId(dataId);
                    break;
                case "海员外派机构资质证书":
                    dwdbCertificateDataMapper.deleteHywpjgByDataId(dataId);
                    break;
                case "海船船员健康证明":
                    dwdbCertificateDataMapper.deleteJkzmByDataId(dataId);
                    break;
                case "内河船舶船员适任证书":
                    dwdbCertificateDataMapper.deleteNhcbcyByDataId(dataId);
                    break;
                case "海船船员内河航线行驶资格证明":
                    dwdbCertificateDataMapper.deleteNhhxxsByDataId(dataId);
                    break;
                case "内河船舶船员培训合格证":
                case "内河船舶船员特殊培训合格证":
                    dwdbCertificateDataMapper.deleteNhpxhgItemByDataId(dataId);
                    dwdbCertificateDataMapper.deleteNhpxhgByDataId(dataId);
                    break;
                case "船员培训质量管理体系证书":
                    dwdbCertificateDataMapper.deleteQmsByDataId(dataId);
                    break;
                case "海船船员培训许可证":
                    dwdbCertificateDataMapper.deleteSeamanPermitItemByDataId(dataId);
                    dwdbCertificateDataMapper.deleteSeamanInfoByDataId(dataId);
                    dwdbCertificateDataMapper.deleteSeamanPermitByDataId(dataId);
                    break;
                case "特定航线江海直达船舶船员行驶资格证明培训合格证":
                    dwdbCertificateDataMapper.deleteTdhxjhByDataId(dataId);
                    break;
                case "小型海船适任证书":
                    dwdbCertificateDataMapper.deleteXhcsrzCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteXhcsrzFunctionByDataId(dataId);
                    dwdbCertificateDataMapper.deleteXhcsrzByDataId(dataId);
                    break;
                case "引航员船员适任证书":
                    dwdbCertificateDataMapper.deleteYhysrzByDataId(dataId);
                    break;
                case "游艇驾驶证（海上）":
                case "游艇驾驶证（内河）":
                case "游艇驾驶证内河":
                case "游艇驾驶证海上":
                case "游艇驾驶证":
                    dwdbCertificateDataMapper.deleteYtjszByDataId(dataId);
                    break;
                case "船上厨师培训合格证明":
                    dwdbCertificateDataMapper.deleteCscspxByDataId(dataId);
                    break;
                case "船上膳食服务辅助人员培训证明":
                    dwdbCertificateDataMapper.deleteCsssfzpxByDataId(dataId);
                    break;
                case "海船船员适任证书承认签证":
                    dwdbCertificateDataMapper.deleteHccycrCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHccycrFunctionByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHccycrByDataId(dataId);
                    break;
                case "海上设施工作人员海上交通安全技能培训合格证明":
                    dwdbCertificateDataMapper.deleteHsssjnByDataId(dataId);
                    break;
                case "海船不参加船员适任证书":
                    dwdbCertificateDataMapper.deleteHcbcjcyCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcbcjcyByDataId(dataId);
                    break;
                case "内河船员培训许可证": // 新增内河船员培训许可证的处理分支
                    dwdbCertificateDataMapper.deleteNhcyxkzItemsByDataId(dataId);
                    dwdbCertificateDataMapper.deleteNhcyxkzByDataId(dataId);
                    break;
                case "海船船员特免证明":
                    dwdbCertificateDataMapper.deleteHccytmCapacityByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHccytmFunctionByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHccytmByDataId(dataId);
                    break;
                case "海船船员培训合格证承认签证":
                    dwdbCertificateDataMapper.deleteHcpxqzTrainByDataId(dataId);
                    dwdbCertificateDataMapper.deleteHcpxhgqzByDataId(dataId);
                    break;
                default:
                    log.warn("未知的证书类型: {}, 无法清理对应的明细表数据", certificateType);
                    break;
            }

            log.info("已清理目标表中的数据, dataId: {}, certificateType: {}", dataId, certificateType);
        } catch (Exception e) {
            log.error("清理目标表数据失败, dataId: " + dataId, e);
            throw new RuntimeException("清理目标表数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()),
                    e);
        }
    }

    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public void batchInsertAttributes(List<DwdbCertificateDataAttribute> attributes) {
        try {
            dwdbCertificateDataMapper.batchInsertAttributes(attributes);
        } catch (Exception e) {
            log.error("插入属性失败，准备重试: {}", e.getMessage());
            throw e;
        }
    }

    @Recover
    public void recover(Exception e, List<DwdbCertificateDataAttribute> attributes) {
        log.error("重试3次后仍然失败，需要人工介入处理: {}", e.getMessage());
        // 可以将失败的数据写入重试表或发送告警
    }

    /**
     * 初始化任务配置
     * 
     * @param taskName 任务名称
     * @return 任务配置
     */
    private DataReceptionTask initTask(String taskName) {
        log.info("初始化任务配置: {}", taskName);
        DataReceptionTask task = new DataReceptionTask();
        task.setTaskName(taskName);
        task.setLastCompletedTime(LocalDateTime.of(1970, 1, 1, 0, 0, 0));
        task.setLastDataId("0");

        // 保存到数据库
        dataReceptionTaskMapper.insert(task);

        return task;
    }

    // 添加处理机构查询辅助表的方法
    private void processQueryOrg(String issuedept, String certificateType) {
        if (StringUtils.isBlank(issuedept)) {
            return;
        }

        // 检查缓存中是否已存在
        if (!queryOrgCache.containsKey(issuedept)) {
            // 创建新记录
            CertQueryOrg queryOrg = new CertQueryOrg();
            queryOrg.setQueryOrgId(UUID.randomUUID().toString());
            queryOrg.setQueryOrgName(issuedept);
            queryOrg.setQueryOrgType(getOrgTypeByCategory(certificateType)); // 根据证书类型设置机构类型
            queryOrg.setRecCreateDate(new Date());
            queryOrg.setRecModifyDate(new Date());
            queryOrg.setNew(true);

            // 添加到缓存和新增列表
            queryOrgCache.put(issuedept, queryOrg);
            newQueryOrgs.add(queryOrg);

            log.debug("添加新的机构查询辅助表记录: {}, 类型: {}", issuedept, queryOrg.getQueryOrgType());
        }
    }

    // 根据证书类型获取机构类型
    private String getOrgTypeByCategory(String certificateType) {
        if (StringUtils.isBlank(certificateType)) {
            return "1"; // 默认为签发机关
        }

        switch (certificateType) {
            case "海员外派机构资质证书":
                return "4"; // 外派机构
            case "海船船员健康证明":
                return "3"; // 主管医师机构
            case "船员培训质量管理体系证书":
                return "5"; // 审核机构
            case "船上厨师培训合格证明":
            case "船上膳食服务辅助人员培训证明":
                return "2"; // 培训机构
            default:
                return "1"; // 签发机关
        }
    }

    /**
     * 获取映射后的职务名称
     * 先从缓存中查询，如果缓存没有命中或目标职务为空，则从数据库中查询
     * 如果数据库中也没有命中，则插入一条新记录并加入缓存
     * 
     * @param originalCapName 原始职务名称
     * @return 映射后的职务名称
     */
    private String getMappedCapacity(String originalCapName) {
        if (StringUtils.isBlank(originalCapName)) {
            return originalCapName;
        }
        originalCapName = originalCapName.trim();
        // 先从缓存中查询
        String mappedCapName = capMappingCache.get(originalCapName);

        // 如果缓存没有命中或目标职务为空，则从数据库中查询
        if (mappedCapName == null || StringUtils.isBlank(mappedCapName)) {
            CertCapMapping mapping = certCapMappingMapper.selectBySourceCapName(originalCapName);

            if (mapping != null) {
                // 如果数据库中查到了映射关系，则加入到缓存中
                mappedCapName = mapping.getDestCapName();
                capMappingCache.put(originalCapName, mappedCapName);
            } else {
                // 如果数据库中没有记录，则插入一条新记录
                CertCapMapping newMapping = new CertCapMapping();
                newMapping.setCapMappingId(UUID.randomUUID().toString());
                newMapping.setSourceCapName(originalCapName);
                newMapping.setDestCapName(""); // 目标职务设为空
                newMapping.setDelFlag("0");
                newMapping.setRecCreateDate(new Date());
                newMapping.setRecModifyDate(new Date());

                try {
                    certCapMappingMapper.insert(newMapping);
                    // 加入到缓存中
                    capMappingCache.put(originalCapName, "");
                    log.info("新增职务映射记录：sourceCapName={}", originalCapName);
                } catch (Exception e) {
                    log.error("插入职务映射记录失败：sourceCapName={}, error={}", originalCapName, e.getMessage());
                }
            }
        }

        // 如果没有找到映射或映射的目标职务为空，则使用原职务
        return (mappedCapName != null && !StringUtils.isBlank(mappedCapName)) ? mappedCapName : originalCapName;
    }
}