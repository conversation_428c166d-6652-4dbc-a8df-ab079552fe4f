package com.example.certificate.task;

import com.example.certificate.config.TaskConfig;
import com.example.certificate.service.CertificateEtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Component
@EnableScheduling
public class CertificateEtlTask {

    private final AtomicBoolean isRunning = new AtomicBoolean(false);

    @Autowired
    private CertificateEtlService certificateEtlService;

    @Autowired
    private TaskConfig taskConfig;

    @Scheduled(cron = "${task.certificate.cron}")
    public void execute() {
        // 如果任务正在运行，则跳过本次执行
        if (!isRunning.compareAndSet(false, true)) {
            log.info("上一次任务还在执行中，本次任务将跳过执行");
            return;
        }

        try {
            log.info("开始执行定时任务...");
            String result = certificateEtlService.executeEtlTask(taskConfig.getTaskName());
            log.info("定时任务执行结果: {}", result);
        } catch (Exception e) {
            log.error("定时任务执行异常", e);
            System.exit(1); // 遇到不可恢复的异常时退出进程
        } finally {
            isRunning.set(false);
        }
    }
} 