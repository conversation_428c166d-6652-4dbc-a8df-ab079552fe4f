package com.example.certificate.util;

import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.DwdbCertificateData;
import com.example.certificate.entity.standard.CertTypeDirectory;
import com.example.certificate.entity.standard.DictYthOrgMapping;
import com.example.certificate.entity.standard.CtfSysDept;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbExperience;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbOptions;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrzShip;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHywpjg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailJkzm;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcbcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhhxxs;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhgItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailQms;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanInfo;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermit;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermitItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailTdhxjh;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhgTraining;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrzRange;
import com.example.certificate.mapper.standard.CertTypeDirectoryMapper;
import com.example.certificate.mapper.standard.DictYthOrgMappingMapper;
import com.example.certificate.mapper.standard.CtfSysDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import com.example.certificate.dto.CertificateConvertResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.HashSet;
import java.util.Set;
import com.example.certificate.entity.standard.DwdbCertificateDataAttribute;
import java.util.function.Consumer;
import java.util.Arrays;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYtjsz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCscspx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCsssfzpx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycr;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsssjn;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzbCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkzItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkz;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytm;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmCap;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmFunc;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxhgqz;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxqzTrain;

@Slf4j
@Component
@Service
public class CertificateConverter {

    @Autowired
    private CertTypeDirectoryMapper certTypeDirectoryMapper;

    @Autowired
    private DictYthOrgMappingMapper dictYthOrgMappingMapper;

    @Autowired
    private CtfSysDeptMapper ctfSysDeptMapper;

    // 修改缓存Map的声明方式，不再使用字段注入
    private Map<String, CertTypeDirectory> certTypeCache;
    private Map<String, DictYthOrgMapping> orgMappingCache;
    private Map<String, CtfSysDept> deptInfoCache;

    // 使用构造函数注入缓存，确保在创建Bean时就注入缓存
    @Autowired
    public CertificateConverter(
            @Qualifier("certTypeCache") Map<String, CertTypeDirectory> certTypeCache,
            @Qualifier("orgMappingCache") Map<String, DictYthOrgMapping> orgMappingCache,
            @Qualifier("deptInfoCache") Map<String, CtfSysDept> deptInfoCache) {

        this.certTypeCache = certTypeCache;
        this.orgMappingCache = orgMappingCache;
        this.deptInfoCache = deptInfoCache;

        log.info("CertificateConverter初始化完成，缓存对象注入成功");
    }

    // 添加方法接收静态缓存
    public void setCaches(
            Map<String, CertTypeDirectory> certTypeCache,
            Map<String, DictYthOrgMapping> orgMappingCache,
            Map<String, CtfSysDept> deptInfoCache) {

        this.certTypeCache = certTypeCache;
        this.orgMappingCache = orgMappingCache;
        this.deptInfoCache = deptInfoCache;

        log.info("CertificateConverter手动设置缓存完成，缓存大小: certType={}, orgMapping={}, deptInfo={}",
                certTypeCache.size(), orgMappingCache.size(), deptInfoCache.size());
    }

    private String convertHolderCategory(String category) {
        if (StringUtils.isBlank(category)) {
            return "其他";
        }
        switch (category) {
            case "自然人":
                return "111";
            case "法人或其他组织":
                return "001";
            case "混合":
                return "099";
            default:
                return "999";
        }
    }

    /**
     * 生成电子证照唯一标识码
     * 规则: 根代码.证照类型代码.颁发机构代码.流水号.版本号.校验位
     * 
     * @param target 目标数据对象
     * @return 生成的电子证照唯一标识码
     */
    private String generateCertificateIdentifier(DwdbCertificateData target) {
        // 1. 电子证照根代码
        String rootCode = "1.2.156.3005.2";

        // 2. 证照类型代码
        String typeCode = target.getCertificateTypeCode();
        if (StringUtils.isBlank(typeCode)) {
            log.warn("证照类型代码为空,使用默认值");
            typeCode = "UNKNOWN";
        }

        // 3. 证照颁发机构代码
        String issuingCode = target.getCertificateIssuingAuthorityCode();
        if (StringUtils.isBlank(issuingCode)) {
            log.warn("证照颁发机构代码为空,使用默认值");
            issuingCode = "UNKNOWN";
        }

        // 4. 流水号
        String serialNumber = target.getCertificateId();
        if (StringUtils.isBlank(serialNumber)) {
            log.warn("证照ID为空,使用随机UUID");
            serialNumber = UUID.randomUUID().toString().replace("-", "");
        }

        // 5. 版本号
        String versionNumber = "999";

        // 6. 计算校验位
        String checkContent = typeCode + issuingCode + serialNumber + versionNumber;
        String checkDigit = calculateCheckDigit(checkContent);

        // 7. 组合生成最终标识码
        return String.format("%s.%s.%s.%s.%s.%s",
                rootCode, typeCode, issuingCode, serialNumber, versionNumber, checkDigit);
    }

    /**
     * 基于GB/T 17710-2008中的"ISO/IEC 7064 MOD 37, 36"校验算法计算校验位
     * 
     * @param content 需要计算校验位的内容
     * @return 校验位
     */
    private String calculateCheckDigit(String content) {
        if (StringUtils.isBlank(content)) {
            return "0";
        }

        // 字符集: 0-9, A-Z, 不包含I,O (共34个字符)
        String charset = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";
        int modulus = 36; // MOD 37,36 中的36

        int sum = 0;
        for (int i = 0; i < content.length(); i++) {
            char c = content.charAt(i);
            int value;

            if (Character.isDigit(c)) {
                value = c - '0';
            } else if (Character.isLetter(c)) {
                c = Character.toUpperCase(c);
                if (c == 'I')
                    c = 'J'; // I被替换为J
                if (c == 'O')
                    c = 'P'; // O被替换为P
                value = c - 'A' + 10;
            } else {
                // 忽略非字母数字字符
                continue;
            }

            // 计算加权和
            sum = (sum + value) % modulus;
            sum = (sum * 2) % modulus;
        }

        // 计算校验位
        int checkValue = (modulus - sum) % modulus;

        // 确保checkValue在有效范围内
        if (checkValue < 0 || checkValue >= charset.length()) {
            checkValue = checkValue % charset.length();
        }

        // 转换为字符
        return String.valueOf(charset.charAt(checkValue));
    }

    private static String convertHolderTypeName(String holderType) {
        if (StringUtils.isBlank(holderType)) {
            return "其他";
        }
        switch (holderType) {
            case "1":
                return "统一社会信用代码";
            case "2":
                return "公民身份号码";
            case "3":
                return "护照号";
            default:
                return "其他";
        }
    }

    private void setBasicFields(OdsCertificateData source, DwdbCertificateData target) {
        // 直接映射字段
        target.setDataId(source.getDataid());
        target.setCertificateId(source.getCertificateid());
        target.setTemplateId(source.getTemplateid());
        target.setCertificateName(source.getCatalogname());
        target.setCertificateNumber(source.getCertificatenumber());
        target.setCertificateIssuingAuthorityName(source.getIssuedept());
        target.setCertificateIssuedDate(source.getIssuedate());
        target.setCertificateHolderName(source.getCertificateholder());
        target.setCertificateHolderCode(source.getCertificateholdercode());
        target.setCertificateEffectiveDate(source.getValidbegindate());
        target.setCertificateExpiringDate(source.getValidenddate());
        target.setIssueDeptCode2(source.getIssuedeptcode());
        target.setCertificateAreaCode(source.getCertificateareacode());
        // target.setSurfaceData(source.getSurfacedata());
        target.setCertificateStatus(source.getStatus());
        target.setCreatorId(source.getCreator());
        target.setCreateTime(source.getCreatetime());
        target.setOperatorId(source.getOperator());
        target.setUpdateTime(source.getUpdatetime());
        target.setFilePath(source.getFilepath());
        target.setSyncStatus(source.getSyncstatus());
        target.setRemarks(source.getRemarks());
        target.setDeptId(source.getDeptid());
        target.setApplyNum(source.getApplynum());
        target.setServeBusiness(source.getServebusiness());
        target.setQzType(source.getQztype());
        target.setDraftUrl(source.getDrafturl());
        target.setSortName(source.getSortname());
        target.setSealname(source.getSealname());
    }

    public DwdbCertificateData convert(OdsCertificateData source) throws Exception {
        if (source == null) {
            return null;
        }

        DwdbCertificateData target = new DwdbCertificateData();

        try {
            // 使用缓存获取证书类型信息
            String catalogid = source.getCatalogid();
            // 游艇驾驶证海上、游艇驾驶证内河 兼容游艇驾驶证的配置
            if (catalogid.equals("a3842fcff22549f8bc28b84d03c45ae7")
                    || catalogid.equals("83d2bb123ee54fdf8425126067b1b616")) {
                catalogid = "d89558cf8da043999a6c7b40f67a0a88";
            }
            CertTypeDirectory certTypeInfo = certTypeCache.get(catalogid);
            if (certTypeInfo == null) {
                // 缓存中没有，再从数据库查询
                certTypeInfo = certTypeDirectoryMapper.getCertTypeInfo(catalogid);
                if (certTypeInfo != null) {
                    // 更新缓存
                    certTypeCache.put(catalogid, certTypeInfo);
                }
            }

            if (certTypeInfo != null) {
                // log.info("证照类型信息: {}", certTypeInfo.toString());
                target.setCertificateTypeName(certTypeInfo.getCertificateTypeName());
                target.setCertificateTypeCode(certTypeInfo.getCertificateTypeCode());
                target.setCertificateDefineAuthorityName(certTypeInfo.getCertificateDefineAuthorityName());
                target.setCertificateDefineAuthorityCode(certTypeInfo.getCertificateDefineAuthorityCode());
                target.setRelatedItemName(certTypeInfo.getRelatedItemName());
                target.setRelatedItemCode(certTypeInfo.getRelatedItemCode());
                target.setCertificateHolderCategory(certTypeInfo.getCertificateHolderCategory());
                target.setCertificateHolderCategoryName(
                        convertHolderCategory(certTypeInfo.getCertificateHolderCategory()));
                target.setValidityRange(certTypeInfo.getValidityRange());
                target.setAffairType(certTypeInfo.getRelatedItemName());
                target.setAffairId(certTypeInfo.getRelatedItemId()); // 使用新增的 relatedItemId 字段
                target.setAffairNum(certTypeInfo.getRelatedItemCode());
            } else {
                log.warn("未找到证照类型信息，templateId: {}", source.getTemplateid());
                throw new Exception("未找到证照类型信息，查询条件为catalogId: " + source.getCatalogid());
            }

            // 3. 处理证照颁发机构代码
            DictYthOrgMapping orgMapping = orgMappingCache.get(source.getIssuedeptcode());
            // log.info("orgMappingCache的总数: {}, issueDeptCode: {}", orgMappingCache.size(),
            // source.getIssuedeptcode());
            if (orgMapping != null) {
                // log.info("机构映射信息: {}", orgMapping.toString());
                String orgCode = orgMapping.getOrgCode();

                // 检查机构编码前缀是否为010299（地方海事局或交通运输厅的机构）
                if (orgCode != null && orgCode.startsWith("010299")) {
                    // 如果是地方海事局或交通运输厅的机构，issue_dept_code3保持和issue_dept_code2一致
                    target.setIssueDeptCode3(source.getIssuedeptcode());
                    log.debug("检测到地方海事局或交通运输厅机构，orgCode: {}, issueDeptCode3设置为: {}", orgCode,
                            source.getIssuedeptcode());
                } else {
                    // 其他机构正常设置
                    target.setIssueDeptCode3(orgCode);
                }

                target.setMsaOrgCode(orgCode);

                // 获取统一社会信用代码
                CtfSysDept deptInfo = deptInfoCache.get(orgCode);
                if (deptInfo != null) {
                    // log.info("机构信息: {}", deptInfo.toString());
                    target.setCertificateIssuingAuthorityCode(deptInfo.getUscc());
                } else {
                    log.warn("未找到机构信息，orgCode: {}", orgCode);
                    throw new Exception("获取统一社会信用代码时，未找到机构信息，orgCode: " + orgCode);
                }
            } else {
                // log.warn("未找到机构映射信息，issueDeptCode: {}", source.getIssuedeptcode());
                // 先临时处理，写死
                target.setIssueDeptCode3(source.getIssuedeptcode());
                target.setMsaOrgCode(source.getIssuedeptcode());
                target.setCertificateIssuingAuthorityCode("555000007842028302");
                // throw new Exception("获取统一社会信用代码时，未找到机构映射信息，issueDeptCode
            }

            // 4. 设置持证主体代码类型
            target.setCertificateHolderTypeName(convertHolderTypeName(source.getCertificateholdertype()));

            // 5. 设置源系统代码
            target.setSourceCode("CYGLXT"); // 默认值

            // 6. 设置其他基础字段
            setBasicFields(source, target);

            // 2. 生成电子证照唯一标识码
            target.setCertificateIdentifier(generateCertificateIdentifier(target));

            // 7. 设置记录创建日期、记录修改日期
            target.setRecCreateDate(new Date());
            target.setRecModifyDate(new Date());

        } catch (Exception e) {
            log.error("数据转换异常，sourceId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "数据转换过程中发生异常" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return target;
    }

    public void validateRequiredFields(DwdbCertificateData data) throws IllegalArgumentException {
        if (StringUtils.isBlank(data.getDataId())) {
            throw new IllegalArgumentException("数据ID不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateId())) {
            throw new IllegalArgumentException("证照标识码不能为空");
        }
        if (StringUtils.isBlank(data.getTemplateId())) {
            throw new IllegalArgumentException("模板ID不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateName())) {
            throw new IllegalArgumentException("证照名称不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateNumber())) {
            throw new IllegalArgumentException("证照编号不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateIssuingAuthorityName())) {
            throw new IllegalArgumentException("证照颁发机构不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateIssuingAuthorityCode())) {
            throw new IllegalArgumentException("证照颁发机构代码（统一社会信用代码）");
        }
        if (StringUtils.isBlank(data.getCertificateIssuedDate())) {
            throw new IllegalArgumentException("颁证日期不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateHolderName())) {
            throw new IllegalArgumentException("持证者名称不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateHolderCode())) {
            throw new IllegalArgumentException("持证者代码不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateExpiringDate())) {
            throw new IllegalArgumentException("有效期截止日不能为空");
        }
        if (data.getRecCreateDate() == null) {
            throw new IllegalArgumentException("创建记录日期不能为空");
        }
        if (data.getRecModifyDate() == null) {
            throw new IllegalArgumentException("记录修改日期不能为空");
        }
        if (StringUtils.isBlank(data.getSyncStatus())) {
            throw new IllegalArgumentException("同步状态不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateTypeName())) {
            throw new IllegalArgumentException("证照类型名称不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateTypeCode())) {
            throw new IllegalArgumentException("证照类型代码不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateDefineAuthorityName())) {
            throw new IllegalArgumentException("证照定义机构不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateDefineAuthorityCode())) {
            throw new IllegalArgumentException("证照定义机构代码不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateHolderCategoryName())) {
            throw new IllegalArgumentException("持证主体类别名称不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateIdentifier())) {
            throw new IllegalArgumentException("电子证照唯一标识码不能为空");
        }
        if (StringUtils.isBlank(data.getCertificateHolderTypeName())) {
            throw new IllegalArgumentException("持证主体代码类型不能为空");
        }
        if (StringUtils.isBlank(data.getMsaOrgCode())) {
            throw new IllegalArgumentException("MSA机构代码不能为空");
        }
        if (StringUtils.isBlank(data.getSourceCode())) {
            throw new IllegalArgumentException("来源代码不能为空");
        }
    }

    /**
     * 处理不参加航行和轮机值班海船船员适任证书的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的证书明细对象
     */
    private Map<String, Object> convertBcjhljzbCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertificateDetailBcjhljzbCapacity> capacityList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailBcjhljzb mainData = new DwdbCtfCertificateDetailBcjhljzb();
            String bcjhljzbId = UUID.randomUUID().toString();
            mainData.setBcjhljzbId(bcjhljzbId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameoftheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameoftheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("certificateIssuedDate1", dataMap, usedAttributes, mainData::setCertificateIssuedDate1);
            setFieldAndMarkUsed("certificateIssuedDate2", dataMap, usedAttributes, mainData::setCertificateIssuedDate2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);

            // 2. 处理职务等级信息表数据
            boolean hasOthersCapacity = false; // 标记是否已经添加过"其他/Others"记录
            for (int i = 1; i <= 31; i++) { // 假设最多31组数据
                int baseIndex = (i - 1) * 2 + 1; // 计算json属性的基础索引

                String capacity1 = dataMap.get("capacity" + baseIndex);
                String capacity2 = dataMap.get("capacity" + (baseIndex + 1));
                String applivations1 = dataMap.get("applivations" + baseIndex);
                String applivations2 = dataMap.get("applivations" + (baseIndex + 1));

                // 检查是否有数据
                if (StringUtils.isAllBlank(capacity1, capacity2, applivations1, applivations2)) {
                    continue;
                }

                // 去重逻辑：如果是"其他/Others"且已经添加过，则跳过
                if ("其他".equals(capacity1) && "Others".equals(capacity2) && hasOthersCapacity) {
                    // 标记这些字段为已使用，但不创建记录
                    setFieldAndMarkUsed("capacity" + baseIndex, dataMap, usedAttributes, value -> {
                    });
                    setFieldAndMarkUsed("capacity" + (baseIndex + 1), dataMap, usedAttributes, value -> {
                    });
                    setFieldAndMarkUsed("applivations" + baseIndex, dataMap, usedAttributes, value -> {
                    });
                    setFieldAndMarkUsed("applivations" + (baseIndex + 1), dataMap, usedAttributes, value -> {
                    });
                    continue;
                }

                // 创建新的职务等级记录
                DwdbCtfCertificateDetailBcjhljzbCapacity capacity = new DwdbCtfCertificateDetailBcjhljzbCapacity();
                capacity.setBcjhljzbCapacityId(UUID.randomUUID().toString());
                capacity.setDataId(source.getDataid());
                capacity.setBcjhljzbId(bcjhljzbId);
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置字段值并标记已使用
                setFieldAndMarkUsed("capacity" + baseIndex, dataMap, usedAttributes, capacity::setCapacity1);
                setFieldAndMarkUsed("capacity" + (baseIndex + 1), dataMap, usedAttributes, capacity::setCapacity2);
                setFieldAndMarkUsed("applivations" + baseIndex, dataMap, usedAttributes, capacity::setApplivations1);
                setFieldAndMarkUsed("applivations" + (baseIndex + 1), dataMap, usedAttributes,
                        capacity::setApplivations2);

                capacityList.add(capacity);

                // 如果这是"其他/Others"记录，标记已添加
                if ("其他".equals(capacity1) && "Others".equals(capacity2)) {
                    hasOthersCapacity = true;
                }
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);

        } catch (Exception e) {
            log.error("解析不参加航行和轮机值班海船船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析不参加航行和轮机值班海船船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    private void setFieldAndMarkUsed(String key, Map<String, String> dataMap,
            Set<String> usedAttributes, Consumer<String> setter) {
        if (dataMap.containsKey(key)) {
            setter.accept(dataMap.get(key));
            usedAttributes.add(key);
        }
    }

    /**
     * 处理船员适任证书申请表的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的数据对象Map
     */
    private Map<String, Object> convertCysrzsqbCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String cysrzsqbId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailCysrzsqb mainData = new DwdbCtfCertificateDetailCysrzsqb();
            mainData.setCysrzsqbId(cysrzsqbId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("sqYear", dataMap, usedAttributes, mainData::setSqYear);
            setFieldAndMarkUsed("sqMonth", dataMap, usedAttributes, mainData::setSqMonth);
            setFieldAndMarkUsed("sqDay", dataMap, usedAttributes, mainData::setSqDay);
            setFieldAndMarkUsed("number", dataMap, usedAttributes, mainData::setNumber);
            setFieldAndMarkUsed("name", dataMap, usedAttributes, mainData::setName);
            setFieldAndMarkUsed("py", dataMap, usedAttributes, mainData::setPy);
            setFieldAndMarkUsed("sex", dataMap, usedAttributes, mainData::setSex);
            setFieldAndMarkUsed("idCode", dataMap, usedAttributes, mainData::setIdCode);
            setFieldAndMarkUsed("birthYear", dataMap, usedAttributes, mainData::setBirthYear);
            setFieldAndMarkUsed("birthMonth", dataMap, usedAttributes, mainData::setBirthMonth);
            setFieldAndMarkUsed("birthDay", dataMap, usedAttributes, mainData::setBirthDay);
            setFieldAndMarkUsed("company", dataMap, usedAttributes, mainData::setCompany);
            setFieldAndMarkUsed("education", dataMap, usedAttributes, mainData::setEducation);
            setFieldAndMarkUsed("school", dataMap, usedAttributes, mainData::setSchool);
            setFieldAndMarkUsed("special", dataMap, usedAttributes, mainData::setSpecial);
            setFieldAndMarkUsed("certNumber", dataMap, usedAttributes, mainData::setCertNumber);
            setFieldAndMarkUsed("certDate", dataMap, usedAttributes, mainData::setCertDate);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setPhoto);
            setFieldAndMarkUsed("gmdssJob", dataMap, usedAttributes, mainData::setGmdssJob);
            setFieldAndMarkUsed("gmdssNo", dataMap, usedAttributes, mainData::setGmdssNo);
            setFieldAndMarkUsed("gYear", dataMap, usedAttributes, mainData::setGYear);
            setFieldAndMarkUsed("gMonth", dataMap, usedAttributes, mainData::setGMonth);
            setFieldAndMarkUsed("gDay", dataMap, usedAttributes, mainData::setGDay);
            setFieldAndMarkUsed("area2", dataMap, usedAttributes, mainData::setArea2);
            setFieldAndMarkUsed("level2", dataMap, usedAttributes, mainData::setLevel2);
            setFieldAndMarkUsed("job2", dataMap, usedAttributes, mainData::setJob2);
            setFieldAndMarkUsed("limit", dataMap, usedAttributes, mainData::setLimitInfo);
            setFieldAndMarkUsed("hgzNo", dataMap, usedAttributes, mainData::setHgzNo);
            setFieldAndMarkUsed("year1", dataMap, usedAttributes, mainData::setYear1);
            setFieldAndMarkUsed("month1", dataMap, usedAttributes, mainData::setMonth1);
            setFieldAndMarkUsed("day1", dataMap, usedAttributes, mainData::setDay1);
            setFieldAndMarkUsed("signature", dataMap, usedAttributes, mainData::setSignature);
            setFieldAndMarkUsed("seal", dataMap, usedAttributes, mainData::setSeal);
            setFieldAndMarkUsed("link", dataMap, usedAttributes, mainData::setLink);
            setFieldAndMarkUsed("tel", dataMap, usedAttributes, mainData::setTel);

            setFieldAndMarkUsed("certNo1", dataMap, usedAttributes, mainData::setCertNo1);
            setFieldAndMarkUsed("sDay", dataMap, usedAttributes, mainData::setSDay);
            setFieldAndMarkUsed("job1", dataMap, usedAttributes, mainData::setJob1);
            setFieldAndMarkUsed("sYear", dataMap, usedAttributes, mainData::setSYear);
            setFieldAndMarkUsed("level1", dataMap, usedAttributes, mainData::setLevel1);
            setFieldAndMarkUsed("sMonth", dataMap, usedAttributes, mainData::setSMonth);
            setFieldAndMarkUsed("area1", dataMap, usedAttributes, mainData::setArea1);

            // 2. 处理服务资历信息表数据
            List<DwdbCtfCertificateDetailCysrzsqbExperience> experienceList = new ArrayList<>();
            for (int i = 1; i <= 31; i++) { // 假设最多10条记录
                // 先获取所有字段值，用于检查是否全部为空
                String jobs = dataMap.get("jobs" + i);
                String shipName = dataMap.get("shipName" + i);
                String type = dataMap.get("type" + i);
                String class_ = dataMap.get("class" + i);
                String weight = dataMap.get("weight" + i);
                String rzDate = dataMap.get("rzDate" + i);
                String lzDate = dataMap.get("lzDate" + i);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(jobs) && StringUtils.isBlank(shipName) &&
                        StringUtils.isBlank(type) && StringUtils.isBlank(class_) &&
                        StringUtils.isBlank(weight) && StringUtils.isBlank(rzDate) &&
                        StringUtils.isBlank(lzDate)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailCysrzsqbExperience exp = new DwdbCtfCertificateDetailCysrzsqbExperience();
                exp.setDataId(source.getDataid()); // 设置源数据ID
                exp.setCysrzsqbExperienceId(UUID.randomUUID().toString());
                exp.setCysrzsqbId(cysrzsqbId);
                exp.setCreateTime(new Date());
                exp.setUpdateTime(new Date());

                // 设置字段并记录已使用的属性
                setFieldAndMarkUsed("jobs" + i, dataMap, usedAttributes, exp::setJobs);
                setFieldAndMarkUsed("shipName" + i, dataMap, usedAttributes, exp::setShipName);
                setFieldAndMarkUsed("type" + i, dataMap, usedAttributes, exp::setType);
                setFieldAndMarkUsed("class" + i, dataMap, usedAttributes, exp::setClass_);
                setFieldAndMarkUsed("weight" + i, dataMap, usedAttributes, exp::setWeight);
                setFieldAndMarkUsed("rzDate" + i, dataMap, usedAttributes, exp::setRzDate);
                setFieldAndMarkUsed("lzDate" + i, dataMap, usedAttributes, exp::setLzDate);

                experienceList.add(exp);

            }

            // 3. 处理选项信息表数据
            List<DwdbCtfCertificateDetailCysrzsqbOptions> optionsList = new ArrayList<>();
            // 处理s1-s9选项
            for (int i = 1; i <= 31; i++) {

                // 先获取所有字段值，用于检查是否全部为空
                String s = dataMap.get("s" + i);
                String c = dataMap.get("c" + i);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (s == null && c == null) {
                    continue; // 跳过空记录
                }

                String key = "s" + i;
                String value = "c" + i;

                DwdbCtfCertificateDetailCysrzsqbOptions opt = new DwdbCtfCertificateDetailCysrzsqbOptions();
                opt.setDataId(source.getDataid()); // 设置源数据ID
                opt.setCysrzsqbOptionsId(UUID.randomUUID().toString());
                opt.setCysrzsqbId(cysrzsqbId);
                opt.setCreateTime(new Date());
                opt.setUpdateTime(new Date());
                opt.setOptionKey(key);
                opt.setOptionValue(value);
                setFieldAndMarkUsed(key, dataMap, usedAttributes, opt::setOptionKey);
                setFieldAndMarkUsed(value, dataMap, usedAttributes, opt::setOptionValue);
                optionsList.add(opt);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("experienceList", experienceList);
            result.put("optionsList", optionsList);

        } catch (Exception e) {
            log.error("解析船员适任证书申请表数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析船员适任证书申请表数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理公务船船员适任证书的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的证书明细对象
     */
    private Map<String, Object> convertGwccyCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertificateDetailGwccyCapacity> capacityList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailGwccy target = new DwdbCtfCertificateDetailGwccy();
            String gwccyId = UUID.randomUUID().toString();
            target.setGwccyId(gwccyId);
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, target::setFullNameoftheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, target::setFullNameoftheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, target::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, target::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, target::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, target::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, target::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, target::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, target::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    target::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    target::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, target::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, target::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, target::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, target::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, target::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, target::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, target::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, target::setOfficialUseOnly2);

            // 2. 处理职务等级信息表数据
            for (int i = 1; i <= 62; i += 2) { // 每次递增2,因为1,2是一组,3,4是一组
                String baseIndex = String.valueOf(i);
                String nextIndex = String.valueOf(i + 1);

                // 获取当前组的所有字段值
                String gradwAndCapacity1 = dataMap.get("gradwAndCapacity" + baseIndex);
                String gradwAndCapacity2 = dataMap.get("gradwAndCapacity" + nextIndex);
                String alimitationsApplying1 = dataMap.get("limiationsApplying" + baseIndex);
                String alimitationsApplying2 = dataMap.get("limiationsApplying" + nextIndex);

                // 检查是否所有字段都为空
                if (StringUtils.isAllBlank(gradwAndCapacity1, gradwAndCapacity2,
                        alimitationsApplying1, alimitationsApplying2)) {
                    continue;
                }

                // 创建新的职务等级记录
                DwdbCtfCertificateDetailGwccyCapacity capacity = new DwdbCtfCertificateDetailGwccyCapacity();
                capacity.setGwccyCapacityId(UUID.randomUUID().toString());
                capacity.setGwccyId(gwccyId);
                capacity.setDataId(source.getDataid());
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置字段值并标记已使用
                setFieldAndMarkUsed("gradwAndCapacity" + baseIndex, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity1);
                setFieldAndMarkUsed("gradwAndCapacity" + nextIndex, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity2);
                setFieldAndMarkUsed("limiationsApplying" + baseIndex, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("limiationsApplying" + nextIndex, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 将职务等级列表放入主表对象
            result.put("mainData", target);
            result.put("capacityList", capacityList);

        } catch (Exception e) {
            log.error("解析公务船船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析公务船船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船高级船员适任证书的数据转换
     */
    private Map<String, Object> convertHcgjcyCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String hcgjcyId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHcgjcy mainData = new DwdbCtfCertificateDetailHcgjcy();
            mainData.setHcgjcyId(hcgjcyId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("articleNumber1", dataMap, usedAttributes, mainData::setArticleNumber1);
            setFieldAndMarkUsed("articleNumber2", dataMap, usedAttributes, mainData::setArticleNumber2);
            setFieldAndMarkUsed("articleNumber3", dataMap, usedAttributes, mainData::setArticleNumber3);
            setFieldAndMarkUsed("articleNumber4", dataMap, usedAttributes, mainData::setArticleNumber4);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);

            // 2. 处理职务等级信息表数据
            List<DwdbCtfCertificateDetailHcgjcyCapacity> capacityList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String capacityPrefix1 = String.valueOf(i);
                String capacityPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String gradwAndCapacity1 = getFirstNonBlankValue(dataMap, usedAttributes,
                        "gradwAndCapacity" + capacityPrefix1, "capacity" + capacityPrefix1);// dataMap.get("gradwAndCapacity"
                                                                                            // + capacityPrefix1);
                String gradwAndCapacity2 = getFirstNonBlankValue(dataMap, usedAttributes,
                        "gradwAndCapacity" + capacityPrefix2, "capacity" + capacityPrefix2);// dataMap.get("gradwAndCapacity"
                                                                                            // + capacityPrefix2);
                String alimitationsApplying1 = dataMap.get("alimitationsApplying" + capacityPrefix1);
                String alimitationsApplying2 = dataMap.get("alimitationsApplying" + capacityPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(gradwAndCapacity1) && StringUtils.isBlank(gradwAndCapacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHcgjcyCapacity capacity = new DwdbCtfCertificateDetailHcgjcyCapacity();
                capacity.setDataId(source.getDataid()); // 设置源数据ID
                capacity.setHcgjcyCapacityId(UUID.randomUUID().toString());
                capacity.setHcgjcyId(hcgjcyId);
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置职务等级信息，同时记录已使用的属性
                // gradwAndCapacity1是职务等级中文，gradwAndCapacity2是职务等级英文
                // gradwAndCapacity3是职务等级补充中文，gradwAndCapacity4是职务等级补充英文
                // setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix1, dataMap,
                // usedAttributes, capacity::setGradwAndCapacity1);
                // setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix2, dataMap,
                // usedAttributes, capacity::setGradwAndCapacity2);
                capacity.setGradwAndCapacity1(gradwAndCapacity1);
                capacity.setGradwAndCapacity2(gradwAndCapacity2);
                // alimitationsApplying1是职务限制中文，alimitationsApplying2是职务限制英文
                // alimitationsApplying3是职务限制补充1，alimitationsApplying4是职务限制补充2
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 3. 处理职能信息表数据
            List<DwdbCtfCertificateDetailHcgjcyFunction> functionList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String functionPrefix1 = String.valueOf(i);
                String functionPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String function1 = dataMap.get("function" + functionPrefix1);
                String function2 = dataMap.get("function" + functionPrefix2);
                String level1 = dataMap.get("level" + functionPrefix1);
                String level2 = dataMap.get("level" + functionPrefix2);
                String limitationsApplying1 = dataMap.get("limitationsApplying" + functionPrefix1);
                String limitationsApplying2 = dataMap.get("limitationsApplying" + functionPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(function1) && StringUtils.isBlank(function2) &&
                        StringUtils.isBlank(limitationsApplying1) && StringUtils.isBlank(limitationsApplying2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHcgjcyFunction func = new DwdbCtfCertificateDetailHcgjcyFunction();
                func.setDataId(source.getDataid()); // 设置源数据ID
                func.setHcgjcyFunctionId(UUID.randomUUID().toString());
                func.setHcgjcyId(hcgjcyId);
                func.setCreateTime(new Date());
                func.setUpdateTime(new Date());

                // 设置职能信息，同时记录已使用的属性
                // function1是中文，function2是英文
                setFieldAndMarkUsed("function" + functionPrefix1, dataMap, usedAttributes, func::setFunction1);
                setFieldAndMarkUsed("function" + functionPrefix2, dataMap, usedAttributes, func::setFunction2);

                // level1是中文，level2是英文
                setFieldAndMarkUsed("level" + functionPrefix1, dataMap, usedAttributes, func::setLevel1);
                setFieldAndMarkUsed("level" + functionPrefix2, dataMap, usedAttributes, func::setLevel2);

                // limitationsApplying1是中文，limitationsApplying2是英文
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix1, dataMap, usedAttributes,
                        func::setLimitationsApplying1);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix2, dataMap, usedAttributes,
                        func::setLimitationsApplying2);

                functionList.add(func);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);
            result.put("functionList", functionList);

        } catch (Exception e) {
            log.error("解析海船高级船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析海船高级船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船普通船员适任证书的数据转换
     */
    private Map<String, Object> convertHcptcysrzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String hcptcysrzId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHcptcysrz mainData = new DwdbCtfCertificateDetailHcptcysrz();
            mainData.setHcptcysrzId(hcptcysrzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("capacity1", dataMap, usedAttributes, mainData::setCapacity1);
            setFieldAndMarkUsed("capacity2", dataMap, usedAttributes, mainData::setCapacity2);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);

            // 2. 处理职务等级信息表数据
            List<DwdbCtfCertificateDetailHcptcysrzCapacity> capacityList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String capacityPrefix1 = String.valueOf(i);
                String capacityPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String gradwAndCapacity1 = dataMap.get("gradwAndCapacity" + capacityPrefix1);
                String gradwAndCapacity2 = dataMap.get("gradwAndCapacity" + capacityPrefix2);
                String alimitationsApplying1 = dataMap.get("alimitationsApplying" + capacityPrefix1);
                String alimitationsApplying2 = dataMap.get("alimitationsApplying" + capacityPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(gradwAndCapacity1) && StringUtils.isBlank(gradwAndCapacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHcptcysrzCapacity capacity = new DwdbCtfCertificateDetailHcptcysrzCapacity();
                capacity.setDataId(source.getDataid()); // 设置源数据ID
                capacity.setHcptcysrzCapacityId(UUID.randomUUID().toString());
                capacity.setHcptcysrzId(hcptcysrzId);
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置职务等级信息，同时记录已使用的属性
                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity1);
                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity2);

                // alimitationsApplying1是职务限制中文，alimitationsApplying2是职务限制英文
                // alimitationsApplying3是职务限制补充1，alimitationsApplying4是职务限制补充2
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 3. 处理职能信息表数据
            List<DwdbCtfCertificateDetailHcptcysrzFunction> functionList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String functionPrefix1 = String.valueOf(i);
                String functionPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String function1 = dataMap.get("function" + functionPrefix1);
                String function2 = dataMap.get("function" + functionPrefix2);
                String level1 = dataMap.get("level" + functionPrefix1);
                String level2 = dataMap.get("level" + functionPrefix2);
                String limitationsApplying1 = dataMap.get("limitationsApplying" + functionPrefix1);
                String limitationsApplying2 = dataMap.get("limitationsApplying" + functionPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(function1) && StringUtils.isBlank(function2) &&
                        StringUtils.isBlank(limitationsApplying1) && StringUtils.isBlank(limitationsApplying2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHcptcysrzFunction func = new DwdbCtfCertificateDetailHcptcysrzFunction();
                func.setDataId(source.getDataid()); // 设置源数据ID
                func.setHcptcysrzFunctionId(UUID.randomUUID().toString());
                func.setHcptcysrzId(hcptcysrzId);
                func.setCreateTime(new Date());
                func.setUpdateTime(new Date());

                // 设置职能信息，同时记录已使用的属性
                // function1是中文，function2是英文
                setFieldAndMarkUsed("function" + functionPrefix1, dataMap, usedAttributes, func::setFunction1);
                setFieldAndMarkUsed("function" + functionPrefix2, dataMap, usedAttributes, func::setFunction2);

                // level1是中文，level2是英文
                setFieldAndMarkUsed("level" + functionPrefix1, dataMap, usedAttributes, func::setLevel1);
                setFieldAndMarkUsed("level" + functionPrefix2, dataMap, usedAttributes, func::setLevel2);

                // limitationsApplying1是中文，limitationsApplying2是英文
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix1, dataMap, usedAttributes,
                        func::setLimitationsApplying1);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix2, dataMap, usedAttributes,
                        func::setLimitationsApplying2);

                functionList.add(func);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);
            result.put("functionList", functionList);
            // 打印functionList的汇总信息（个数）
            log.debug("functionList的汇总信息（个数）: " + functionList.size());

        } catch (Exception e) {
            log.error("解析海船普通船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析海船普通船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海上非自航船舶船员适任证书的数据转换
     */
    private Map<String, Object> convertHsfhcysrzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String hsfhcysrzId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHsfhcysrz mainData = new DwdbCtfCertificateDetailHsfhcysrz();
            mainData.setHsfhcysrzId(hsfhcysrzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("fullNameoftheHolder", dataMap, usedAttributes, mainData::setFullNameOfTheHolder);
            setFieldAndMarkUsed("dateOfBirth", dataMap, usedAttributes, mainData::setDateOfBirth);
            setFieldAndMarkUsed("placeOfBirth", dataMap, usedAttributes, mainData::setPlaceOfBirth);
            setFieldAndMarkUsed("dateOfExpirty", dataMap, usedAttributes, mainData::setDateOfExpirty);
            setFieldAndMarkUsed("dateOfIssue", dataMap, usedAttributes, mainData::setDateOfIssue);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial);
            setFieldAndMarkUsed("remark", dataMap, usedAttributes, mainData::setRemark);

            // 2. 处理船舶信息表数据
            List<DwdbCtfCertificateDetailHsfhcysrzShip> shipList = new ArrayList<>();

            // 遍历可能的记录（假设最多31条记录）
            for (int i = 1; i <= 31; i++) {
                String prefix = String.valueOf(i);

                // 先获取所有字段值，用于检查是否全部为空
                String shipType = dataMap.get("shipType" + prefix);
                String level = dataMap.get("level" + prefix);
                String capacity = dataMap.get("capacity" + prefix);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(shipType) && StringUtils.isBlank(level) &&
                        StringUtils.isBlank(capacity)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHsfhcysrzShip ship = new DwdbCtfCertificateDetailHsfhcysrzShip();
                ship.setDataId(source.getDataid()); // 设置源数据ID
                ship.setHsfhcysrzShipId(UUID.randomUUID().toString());
                ship.setHsfhcysrzId(hsfhcysrzId);
                ship.setCreateTime(new Date());
                ship.setUpdateTime(new Date());

                // 设置船舶信息，同时记录已使用的属性
                setFieldAndMarkUsed("shipType" + prefix, dataMap, usedAttributes, ship::setShipType);
                setFieldAndMarkUsed("level" + prefix, dataMap, usedAttributes, ship::setLevel);
                setFieldAndMarkUsed("capacity" + prefix, dataMap, usedAttributes, ship::setCapacity);

                shipList.add(ship);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("shipList", shipList);

        } catch (Exception e) {
            log.error("解析海上非自航船舶船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析海上非自航船舶船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海员外派机构资质证书的数据转换
     */
    private DwdbCtfCertificateDetailHywpjg convertHywpjgCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailHywpjg target = new DwdbCtfCertificateDetailHywpjg();

        try {
            target.setHywpjgId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());
            String yesOrNo9 = dataMap.get("yesOrNo9");
            // 使用setFieldAndMarkUsed方法设置字段
            setFieldAndMarkUsed("permitNumber1", dataMap, usedAttributes, target::setPermitNumber1);
            setFieldAndMarkUsed("permitNumber2", dataMap, usedAttributes, target::setPermitNumber2);
            setFieldAndMarkUsed("anThorityName1", dataMap, usedAttributes, target::setAnThorityName1);
            setFieldAndMarkUsed("anThorityName2", dataMap, usedAttributes, target::setAnThorityName2);
            setFieldAndMarkUsed("anThorityName3", dataMap, usedAttributes, target::setAnThorityName3);
            setFieldAndMarkUsed("anThorityName4", dataMap, usedAttributes, target::setAnThorityName4);
            setFieldAndMarkUsed("address1", dataMap, usedAttributes, target::setAddress1);
            setFieldAndMarkUsed("address2", dataMap, usedAttributes, target::setAddress2);
            setFieldAndMarkUsed("address3", dataMap, usedAttributes, target::setAddress3);
            setFieldAndMarkUsed("address4", dataMap, usedAttributes, target::setAddress4);
            setFieldAndMarkUsed("representative1", dataMap, usedAttributes, target::setRepresentative1);
            setFieldAndMarkUsed("representative2", dataMap, usedAttributes, target::setRepresentative2);
            setFieldAndMarkUsed("representative3", dataMap, usedAttributes, target::setRepresentative3);
            setFieldAndMarkUsed("representative4", dataMap, usedAttributes, target::setRepresentative4);
            setFieldAndMarkUsed("expiryDate1", dataMap, usedAttributes, target::setExpiryDate1);
            setFieldAndMarkUsed("expiryDate2", dataMap, usedAttributes, target::setExpiryDate2);
            setFieldAndMarkUsed("dateofIssue1", dataMap, usedAttributes, target::setDateOfIssue1);
            setFieldAndMarkUsed("dateofIssue3", dataMap, usedAttributes, target::setDateOfIssue3);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, target::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, target::setIssuingAuthority2);
            setFieldAndMarkUsed("issuingAuthority3", dataMap, usedAttributes, target::setIssuingAuthority3);
            setFieldAndMarkUsed("issuingAuthority4", dataMap, usedAttributes, target::setIssuingAuthority4);
            setFieldAndMarkUsed("remark1", dataMap, usedAttributes, target::setRemark1);
            setFieldAndMarkUsed("remark2", dataMap, usedAttributes, target::setRemark2);
            setFieldAndMarkUsed("annualExamination", dataMap, usedAttributes, target::setAnnualExamination);
        } catch (Exception e) {
            log.error("解析海员外派机构资质证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海员外派机构资质证书数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }
        return target;
    }

    /**
     * 处理海船船员健康证明的数据转换
     */
    private DwdbCtfCertificateDetailJkzm convertJkzmCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailJkzm target = new DwdbCtfCertificateDetailJkzm();

        try {
            target.setJkzmId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 设置JSON中的字段，同时记录已使用的属性
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, target::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, target::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, target::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, target::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, target::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, target::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, target::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, target::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, target::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    target::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    target::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, target::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, target::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, target::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, target::setInformationOfPhoto);
            setFieldAndMarkUsed("yesOrNo1", dataMap, usedAttributes, target::setYesOrNo1);
            setFieldAndMarkUsed("yesOrNo2", dataMap, usedAttributes, target::setYesOrNo2);
            setFieldAndMarkUsed("yesOrNo3", dataMap, usedAttributes, target::setYesOrNo3);
            setFieldAndMarkUsed("yesOrNo4", dataMap, usedAttributes, target::setYesOrNo4);
            setFieldAndMarkUsed("yesOrNo5", dataMap, usedAttributes, target::setYesOrNo5);
            setFieldAndMarkUsed("yesOrNo6", dataMap, usedAttributes, target::setYesOrNo6);
            setFieldAndMarkUsed("yesOrNo7", dataMap, usedAttributes, target::setYesOrNo7);
            setFieldAndMarkUsed("yesOrNo8", dataMap, usedAttributes, target::setYesOrNo8);

            Object rawValue = dataMap.get("yesOrNo9");
            if (rawValue instanceof String) {
                String value = ((String) rawValue).trim();

                // 只处理英文逗号、空格，保留中文内容不被切割
                value = value.replaceAll(",+", ","); // 合并逗号，跳过空格
                value = value.replaceAll("^,+|,+$", ""); // 去掉首尾逗号

                dataMap.put("yesOrNo9", value);
            }
            setFieldAndMarkUsed("yesOrNo9", dataMap, usedAttributes, target::setYesOrNo9);
            setFieldAndMarkUsed("authorizingAuthority1", dataMap, usedAttributes, target::setAuthorizingAuthority1);
            setFieldAndMarkUsed("authorizingAuthority2", dataMap, usedAttributes, target::setAuthorizingAuthority2);
            setFieldAndMarkUsed("doctorName1", dataMap, usedAttributes, target::setDoctorName1);
            setFieldAndMarkUsed("doctorName2", dataMap, usedAttributes, target::setDoctorName2);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, target::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, target::setIssuingAuthority2);
            setFieldAndMarkUsed("department1", dataMap, usedAttributes, target::setDepartment1);
            setFieldAndMarkUsed("department2", dataMap, usedAttributes, target::setDepartment2);

        } catch (Exception e) {
            log.error("解析海船船员健康证明数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析海船船员健康证明数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return target;
    }

    /**
     * 处理内河船舶船员适任证书的数据转换
     */
    private DwdbCtfCertificateDetailNhcbcy convertNhcbcyCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailNhcbcy target = new DwdbCtfCertificateDetailNhcbcy();

        try {
            target.setNhcbcyId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 使用setFieldAndMarkUsed方法设置字段
            setFieldAndMarkUsed("name", dataMap, usedAttributes, target::setName);
            setFieldAndMarkUsed("sex", dataMap, usedAttributes, target::setSex);
            setFieldAndMarkUsed("number", dataMap, usedAttributes, target::setNumber);
            setFieldAndMarkUsed("type", dataMap, usedAttributes, target::setType);
            setFieldAndMarkUsed("endDate", dataMap, usedAttributes, target::setEndDate);
            setFieldAndMarkUsed("signDept", dataMap, usedAttributes, target::setSignDept);
            setFieldAndMarkUsed("printNo", dataMap, usedAttributes, target::setPrintNo);
            setFieldAndMarkUsed("scope", dataMap, usedAttributes, target::setScope);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, target::setPhoto);
            setFieldAndMarkUsed("year", dataMap, usedAttributes, target::setYear);
            setFieldAndMarkUsed("month", dataMap, usedAttributes, target::setMonth);
            setFieldAndMarkUsed("day", dataMap, usedAttributes, target::setDay);
            setFieldAndMarkUsed("issueDept", dataMap, usedAttributes, target::setIssueDept);
            setFieldAndMarkUsed("signDate", dataMap, usedAttributes, target::setSignDate);

        } catch (Exception e) {
            log.error("解析内河船舶船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析内河船舶船员适任证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }
        return target;
    }

    /**
     * 处理海船船员内河航线行驶资格证明的数据转换
     */
    private DwdbCtfCertificateDetailNhhxxs convertNhhxxsCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailNhhxxs target = new DwdbCtfCertificateDetailNhhxxs();

        try {
            target.setNhhxxsId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 使用setFieldAndMarkUsed方法设置字段
            setFieldAndMarkUsed("name", dataMap, usedAttributes, target::setName);
            setFieldAndMarkUsed("gender", dataMap, usedAttributes, target::setGender);
            setFieldAndMarkUsed("creditCode", dataMap, usedAttributes, target::setCreditCode);
            setFieldAndMarkUsed("dateOfIssue", dataMap, usedAttributes, target::setDateOfIssue);
            setFieldAndMarkUsed("expiryDate", dataMap, usedAttributes, target::setExpiryDate);
            setFieldAndMarkUsed("issuingDate", dataMap, usedAttributes, target::setIssuingDate);
            setFieldAndMarkUsed("applivations", dataMap, usedAttributes, target::setApplivations);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, target::setPhoto);
            setFieldAndMarkUsed("issuingAuthority", dataMap, usedAttributes, target::setIssuingAuthority);
            setFieldAndMarkUsed("numberOfCertificate", dataMap, usedAttributes, target::setNumberOfCertificate);

        } catch (Exception e) {
            log.error("解析海船船员内河航线行驶资格证明数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船船员内河航线行驶资格证明数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage()
                    + "\n" + Arrays.toString(e.getStackTrace()), e);
        }
        return target;
    }

    /**
     * 转换内河船舶船员培训合格证数据
     */
    private DwdbCtfCertificateDetailNhpxhg convertNhpxhg(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailNhpxhg target = new DwdbCtfCertificateDetailNhpxhg();

        try {
            target.setNhpxhgId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 设置JSON中的字段，同时记录已使用的属性
            setFieldAndMarkUsed("name", dataMap, usedAttributes, target::setName);
            setFieldAndMarkUsed("sex", dataMap, usedAttributes, target::setSex);
            setFieldAndMarkUsed("number", dataMap, usedAttributes, target::setNumber);
            setFieldAndMarkUsed("printNo", dataMap, usedAttributes, target::setPrintNo);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, target::setPhoto);
            setFieldAndMarkUsed("year", dataMap, usedAttributes, target::setYear);
            setFieldAndMarkUsed("month", dataMap, usedAttributes, target::setMonth);
            setFieldAndMarkUsed("day", dataMap, usedAttributes, target::setDay);

            // 处理培训项目子表数据
            List<DwdbCtfCertificateDetailNhpxhgItem> items = new ArrayList<>();

            // 遍历1-9,提取多条记录
            for (int i = 1; i <= 31; i++) {
                String prefix = String.valueOf(i);

                // 先获取所有字段值，用于检查是否全部为空
                String project = dataMap.get("project" + prefix);
                String signDept = dataMap.get("signDept" + prefix);
                String signDate = dataMap.get("signDate" + prefix);
                String endDate = dataMap.get("endDate" + prefix);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(project) && StringUtils.isBlank(signDept) &&
                        StringUtils.isBlank(signDate) && StringUtils.isBlank(endDate)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailNhpxhgItem item = new DwdbCtfCertificateDetailNhpxhgItem();
                item.setDataId(source.getDataid()); // 设置源数据ID
                item.setNhpxhgItemId(UUID.randomUUID().toString());
                item.setNhpxhgId(target.getNhpxhgId());
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());

                // 设置培训项目信息，同时记录已使用的属性
                setFieldAndMarkUsed("project" + prefix, dataMap, usedAttributes, item::setProject);
                setFieldAndMarkUsed("signDept" + prefix, dataMap, usedAttributes, item::setSignDept);
                setFieldAndMarkUsed("signDate" + prefix, dataMap, usedAttributes, item::setSignDate);
                setFieldAndMarkUsed("endDate" + prefix, dataMap, usedAttributes, item::setEndDate);

                items.add(item);
            }

            target.setItems(items);

        } catch (Exception e) {
            log.error("转换内河船舶船员培训合格证数据失败", e);
            throw new RuntimeException(
                    "转换内河船舶船员培训合格证数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return target;
    }

    /**
     * 从JSON中获取字符串值,不存在返回空字符串
     */
    private String getStringValue(JsonNode json, String field) {
        JsonNode node = json.get(field);
        return node != null ? node.asText() : "";
    }

    /**
     * 处理船员培训质量管理体系证书的数据转换
     */
    private DwdbCtfCertificateDetailQms convertQmsCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailQms target = new DwdbCtfCertificateDetailQms();

        try {
            target.setQmsId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 使用setFieldAndMarkUsed方法设置字段
            setFieldAndMarkUsed("number1", dataMap, usedAttributes, target::setNumber1);
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, target::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, target::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("year1", dataMap, usedAttributes, target::setYear1);
            setFieldAndMarkUsed("month1", dataMap, usedAttributes, target::setMonth1);
            setFieldAndMarkUsed("day1", dataMap, usedAttributes, target::setDay1);
            setFieldAndMarkUsed("certificateExpiringDate", dataMap, usedAttributes, target::setCertificateExpiringDate);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    target::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("evaluationOrganization1", dataMap, usedAttributes, target::setEvaluationOrganization1);
            setFieldAndMarkUsed("evaluationOrganization2", dataMap, usedAttributes, target::setEvaluationOrganization2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, target::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, target::setDateOfIssue2);
            setFieldAndMarkUsed("number2", dataMap, usedAttributes, target::setNumber2);
            setFieldAndMarkUsed("remarks", dataMap, usedAttributes, target::setRemarks);

        } catch (Exception e) {
            log.error("解析船员培训质量管理体系证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析船员培训质量管理体系证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }
        return target;
    }

    /**
     * 处理海船船员培训许可证的数据转换
     */
    private Map<String, Object> convertSeamanPermitCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String seamanPermitId = UUID.randomUUID().toString();
        List<DwdbCtfCertificateDetailSeamanPermit> permitList = new ArrayList<>(); // 新增列表

        try {
            // 1. 处理个人信息表
            DwdbCtfCertificateDetailSeamanInfo seamanInfo = new DwdbCtfCertificateDetailSeamanInfo();
            seamanInfo.setSeamanInfoId(UUID.randomUUID().toString());
            seamanInfo.setDataId(source.getDataid());
            seamanInfo.setCreateTime(new Date());
            seamanInfo.setUpdateTime(new Date());

            // 使用setFieldAndMarkUsed方法设置字段
            setFieldAndMarkUsed("number", dataMap, usedAttributes, seamanInfo::setNumber);
            setFieldAndMarkUsed("nameCn", dataMap, usedAttributes, seamanInfo::setNameCn);
            setFieldAndMarkUsed("nameEn", dataMap, usedAttributes, seamanInfo::setNameEn);
            setFieldAndMarkUsed("sexCn", dataMap, usedAttributes, seamanInfo::setSexCn);
            setFieldAndMarkUsed("sexEn", dataMap, usedAttributes, seamanInfo::setSexEn);
            setFieldAndMarkUsed("countryCn", dataMap, usedAttributes, seamanInfo::setCountryCn);
            setFieldAndMarkUsed("countryEn", dataMap, usedAttributes, seamanInfo::setCountryEn);
            setFieldAndMarkUsed("birthCn", dataMap, usedAttributes, seamanInfo::setBirthCn);
            setFieldAndMarkUsed("birthEn", dataMap, usedAttributes, seamanInfo::setBirthEn);
            setFieldAndMarkUsed("fileNoCn", dataMap, usedAttributes, seamanInfo::setFileNoCn);
            setFieldAndMarkUsed("fileNoEn", dataMap, usedAttributes, seamanInfo::setFileNoEn);
            setFieldAndMarkUsed("qualificationCn", dataMap, usedAttributes, seamanInfo::setQualificationCn);
            setFieldAndMarkUsed("qualificationEn", dataMap, usedAttributes, seamanInfo::setQualificationEn);
            setFieldAndMarkUsed("initialDateCn", dataMap, usedAttributes, seamanInfo::setInitialDateCn);
            setFieldAndMarkUsed("initialDateEn", dataMap, usedAttributes, seamanInfo::setInitialDateEn);
            setFieldAndMarkUsed("expiryDateCn", dataMap, usedAttributes, seamanInfo::setExpiryDateCn);
            setFieldAndMarkUsed("expiryDateEn", dataMap, usedAttributes, seamanInfo::setExpiryDateEn);
            setFieldAndMarkUsed("signDeptCn", dataMap, usedAttributes, seamanInfo::setSignDeptCn);
            setFieldAndMarkUsed("signDeptEn", dataMap, usedAttributes, seamanInfo::setSignDeptEn);
            setFieldAndMarkUsed("officeOfIssueCn", dataMap, usedAttributes, seamanInfo::setOfficeOfIssueCn);
            setFieldAndMarkUsed("officeOfIssueEn", dataMap, usedAttributes, seamanInfo::setOfficeOfIssueEn);
            setFieldAndMarkUsed("date", dataMap, usedAttributes, seamanInfo::setDate);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, seamanInfo::setPhoto);
            setFieldAndMarkUsed("year", dataMap, usedAttributes, seamanInfo::setYear);
            setFieldAndMarkUsed("month", dataMap, usedAttributes, seamanInfo::setMonth);
            setFieldAndMarkUsed("day", dataMap, usedAttributes, seamanInfo::setDay);

            // 2. 处理主表信息
            DwdbCtfCertificateDetailSeamanPermit seamanPermit = new DwdbCtfCertificateDetailSeamanPermit();
            seamanPermit.setSeamanPermitId(seamanPermitId);
            seamanPermit.setDataId(source.getDataid());
            seamanPermit.setCreateTime(new Date());
            seamanPermit.setUpdateTime(new Date());

            // 设置主表字段，同时记录已使用的属性
            setFieldAndMarkUsed("permitNumber1", dataMap, usedAttributes, seamanPermit::setPermitNumber1);
            setFieldAndMarkUsed("permitNumber2", dataMap, usedAttributes, seamanPermit::setPermitNumber2);
            setFieldAndMarkUsed("anThorityName1", dataMap, usedAttributes, seamanPermit::setAnThorityName1);
            setFieldAndMarkUsed("anThorityName2", dataMap, usedAttributes, seamanPermit::setAnThorityName2);
            setFieldAndMarkUsed("trainingInstitutionCode1", dataMap, usedAttributes,
                    seamanPermit::setTrainingInstitutionCode1);
            setFieldAndMarkUsed("representative1", dataMap, usedAttributes, seamanPermit::setRepresentative1);
            setFieldAndMarkUsed("representative2", dataMap, usedAttributes, seamanPermit::setRepresentative2);
            setFieldAndMarkUsed("trainingProgram1", dataMap, usedAttributes, seamanPermit::setTrainingProgram1);
            setFieldAndMarkUsed("trainingProgram2", dataMap, usedAttributes, seamanPermit::setTrainingProgram2);
            setFieldAndMarkUsed("registeredAddress1", dataMap, usedAttributes, seamanPermit::setRegisteredAddress1);
            setFieldAndMarkUsed("registeredAddress2", dataMap, usedAttributes, seamanPermit::setRegisteredAddress2);
            setFieldAndMarkUsed("trainingLocation1", dataMap, usedAttributes, seamanPermit::setTrainingLocation1);
            setFieldAndMarkUsed("trainingLocation2", dataMap, usedAttributes, seamanPermit::setTrainingLocation2);
            setFieldAndMarkUsed("periodOfValidity1", dataMap, usedAttributes, seamanPermit::setPeriodOfValidity1);
            setFieldAndMarkUsed("periodOfValidity2", dataMap, usedAttributes, seamanPermit::setPeriodOfValidity2);
            setFieldAndMarkUsed("periodOfValidity3", dataMap, usedAttributes, seamanPermit::setPeriodOfValidity3);
            setFieldAndMarkUsed("periodOfValidity4", dataMap, usedAttributes, seamanPermit::setPeriodOfValidity4);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, seamanPermit::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, seamanPermit::setIssuingAuthority2);
            setFieldAndMarkUsed("dateofIssue1", dataMap, usedAttributes, seamanPermit::setDateofIssue1);
            setFieldAndMarkUsed("dateofIssue2", dataMap, usedAttributes, seamanPermit::setDateofIssue2);
            setFieldAndMarkUsed("remarks", dataMap, usedAttributes, seamanPermit::setRemarks);

            permitList.add(seamanPermit); // 添加到列表

            // 3. 处理培训项目子表信息
            List<DwdbCtfCertificateDetailSeamanPermitItem> itemList = new ArrayList<>();
            for (int i = 1; i <= 31; i++) {

                // 先获取所有字段值，用于检查是否全部为空
                String number = dataMap.get("number" + i);
                String atrainingProgram = dataMap.get("atrainingProgram" + i);
                String trainingScale = dataMap.get("trainingScale" + i);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(number) && StringUtils.isBlank(atrainingProgram) &&
                        StringUtils.isBlank(trainingScale)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailSeamanPermitItem item = new DwdbCtfCertificateDetailSeamanPermitItem();
                item.setDataId(source.getDataid()); // 设置源数据ID
                item.setSeamanPermitItemId(UUID.randomUUID().toString());
                item.setSeamanPermitId(seamanPermitId);
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());

                // 设置培训项目信息，同时记录已使用的属性
                setFieldAndMarkUsed("number" + i, dataMap, usedAttributes, item::setNumber);
                setFieldAndMarkUsed("atrainingProgram" + i, dataMap, usedAttributes, item::setAtrainingProgram);
                setFieldAndMarkUsed("trainingScale" + i, dataMap, usedAttributes, item::setTrainingScale);

                itemList.add(item);
            }

            // 将所有数据放入结果Map
            result.put("seamanInfo", seamanInfo);
            result.put("seamanPermit", permitList);
            result.put("itemList", itemList);

        } catch (Exception e) {
            log.error("解析海船船员培训许可证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船船员培训许可证数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理特定航线江海直达船舶船员行驶资格证明培训合格证的数据转换
     */
    private DwdbCtfCertificateDetailTdhxjh convertTdhxjhCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        DwdbCtfCertificateDetailTdhxjh target = new DwdbCtfCertificateDetailTdhxjh();

        try {
            target.setTdhxjhId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 设置JSON中的字段，同时记录已使用的属性
            setFieldAndMarkUsed("numberOfCertificate", dataMap, usedAttributes, target::setNumberOfCertificate);
            setFieldAndMarkUsed("name", dataMap, usedAttributes, target::setName);
            setFieldAndMarkUsed("dateOfBirth", dataMap, usedAttributes, target::setDateOfBirth);
            setFieldAndMarkUsed("creditCode", dataMap, usedAttributes, target::setCreditCode);
            setFieldAndMarkUsed("gender", dataMap, usedAttributes, target::setGender);
            setFieldAndMarkUsed("dateOfIssue", dataMap, usedAttributes, target::setDateOfIssue);
            setFieldAndMarkUsed("expiryDate", dataMap, usedAttributes, target::setExpiryDate);
            setFieldAndMarkUsed("applivations", dataMap, usedAttributes, target::setApplivations);
            setFieldAndMarkUsed("limitationsApplying", dataMap, usedAttributes, target::setLimitationsApplying);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, target::setPhoto);
            setFieldAndMarkUsed("issuingAuthority", dataMap, usedAttributes, target::setIssuingAuthority);
            setFieldAndMarkUsed("issuingDate", dataMap, usedAttributes, target::setIssuingDate);

        } catch (Exception e) {
            log.error("解析特定航线江海直达船舶船员行驶资格证明培训合格证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析特定航线江海直达船舶船员行驶资格证明培训合格证数据失败，dataId: " + source.getDataid() + "\n"
                    + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return target;
    }

    /**
     * 处理小型海船适任证书的数据转换
     */
    private Map<String, Object> convertXhcsrzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String xhcsrzId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailXhcsrz mainData = new DwdbCtfCertificateDetailXhcsrz();
            mainData.setXhcsrzId(xhcsrzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("articleNumber1", dataMap, usedAttributes, mainData::setArticleNumber1);
            setFieldAndMarkUsed("articleNumber2", dataMap, usedAttributes, mainData::setArticleNumber2);
            setFieldAndMarkUsed("articleNumber3", dataMap, usedAttributes, mainData::setArticleNumber3);
            setFieldAndMarkUsed("articleNumber4", dataMap, usedAttributes, mainData::setArticleNumber4);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息表数据
            List<DwdbCtfCertificateDetailXhcsrzCapacity> capacityList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String capacityPrefix1 = String.valueOf(i);
                String capacityPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String gradwAndCapacity1 = dataMap.get("gradwAndCapacity" + capacityPrefix1);
                String gradwAndCapacity2 = dataMap.get("gradwAndCapacity" + capacityPrefix2);
                String alimitationsApplying1 = dataMap.get("alimitationsApplying" + capacityPrefix1);
                String alimitationsApplying2 = dataMap.get("alimitationsApplying" + capacityPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(gradwAndCapacity1) && StringUtils.isBlank(gradwAndCapacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertificateDetailXhcsrzCapacity capacity = new DwdbCtfCertificateDetailXhcsrzCapacity();
                capacity.setXhcsrzCapacityId(UUID.randomUUID().toString());
                capacity.setXhcsrzId(xhcsrzId);
                capacity.setDataId(source.getDataid());
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置职务等级信息
                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity1);
                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity2);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 3. 处理职能信息表数据
            List<DwdbCtfCertificateDetailXhcsrzFunction> functionList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String functionPrefix1 = String.valueOf(i);
                String functionPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String function1 = dataMap.get("function" + functionPrefix1);
                String function2 = dataMap.get("function" + functionPrefix2);
                String level1 = dataMap.get("level" + functionPrefix1);
                String level2 = dataMap.get("level" + functionPrefix2);
                String limitationsApplying1 = dataMap.get("limitationsApplying" + functionPrefix1);
                String limitationsApplying2 = dataMap.get("limitationsApplying" + functionPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(function1) && StringUtils.isBlank(function2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2) &&
                        StringUtils.isBlank(limitationsApplying1) && StringUtils.isBlank(limitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertificateDetailXhcsrzFunction func = new DwdbCtfCertificateDetailXhcsrzFunction();
                func.setXhcsrzFunctionId(UUID.randomUUID().toString());
                func.setXhcsrzId(xhcsrzId);
                func.setDataId(source.getDataid());
                func.setCreateTime(new Date());
                func.setUpdateTime(new Date());

                // 设置职能信息
                setFieldAndMarkUsed("function" + functionPrefix1, dataMap, usedAttributes, func::setFunction1);
                setFieldAndMarkUsed("function" + functionPrefix2, dataMap, usedAttributes, func::setFunction2);
                setFieldAndMarkUsed("level" + functionPrefix1, dataMap, usedAttributes, func::setLevel1);
                setFieldAndMarkUsed("level" + functionPrefix2, dataMap, usedAttributes, func::setLevel2);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix1, dataMap, usedAttributes,
                        func::setLimitationsApplying1);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix2, dataMap, usedAttributes,
                        func::setLimitationsApplying2);

                functionList.add(func);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);
            result.put("functionList", functionList);

        } catch (Exception e) {
            log.error("解析小型海船适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析小型海船适任证书数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理引航员船员适任证书的数据转换
     */
    private Map<String, Object> convertYhysrzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String yhysrzId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailYhysrz mainData = new DwdbCtfCertificateDetailYhysrz();
            mainData.setYhysrzId(yhysrzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("remarks", dataMap, usedAttributes, mainData::setRemarks);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理引航范围表数据
            List<DwdbCtfCertificateDetailYhysrzRange> rangeList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 62; i += 2) {
                String prefix1 = String.valueOf(i);
                String prefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String type1 = dataMap.get("type" + prefix1);
                String type2 = dataMap.get("type" + prefix2);
                String level1 = dataMap.get("level" + prefix1);
                String level2 = dataMap.get("level" + prefix2);
                String pilotageArea1 = dataMap.get("pilotageArea" + prefix1);
                String pilotageArea2 = dataMap.get("pilotageArea" + prefix2);
                String limitationOfPolotage1 = dataMap.get("limitationOfPolotage" + prefix1);
                String limitationOfPolotage2 = dataMap.get("limitationOfPolotage" + prefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(type1) && StringUtils.isBlank(type2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2) &&
                        StringUtils.isBlank(pilotageArea1) && StringUtils.isBlank(pilotageArea2) &&
                        StringUtils.isBlank(limitationOfPolotage1) && StringUtils.isBlank(limitationOfPolotage2)) {
                    continue;
                }

                DwdbCtfCertificateDetailYhysrzRange range = new DwdbCtfCertificateDetailYhysrzRange();
                range.setYhysrzRangeId(UUID.randomUUID().toString());
                range.setYhysrzId(yhysrzId);
                range.setDataId(source.getDataid());
                range.setCreateTime(new Date());
                range.setUpdateTime(new Date());

                // 设置引航范围信息
                setFieldAndMarkUsed("type" + prefix1, dataMap, usedAttributes, range::setType1);
                setFieldAndMarkUsed("type" + prefix2, dataMap, usedAttributes, range::setType2);
                setFieldAndMarkUsed("level" + prefix1, dataMap, usedAttributes, range::setLevel1);
                setFieldAndMarkUsed("level" + prefix2, dataMap, usedAttributes, range::setLevel2);
                setFieldAndMarkUsed("pilotageArea" + prefix1, dataMap, usedAttributes, range::setPilotageArea1);
                setFieldAndMarkUsed("pilotageArea" + prefix2, dataMap, usedAttributes, range::setPilotageArea2);
                setFieldAndMarkUsed("limitationOfPolotage" + prefix1, dataMap, usedAttributes,
                        range::setLimitationOfPolotage1);
                setFieldAndMarkUsed("limitationOfPolotage" + prefix2, dataMap, usedAttributes,
                        range::setLimitationOfPolotage2);

                rangeList.add(range);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("rangeList", rangeList);

        } catch (Exception e) {
            log.error("解析引航员船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析引航员船员适任证书数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船船员培训合格证书的数据转换
     */
    private Map<String, Object> convertHcpxhgCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        String hcpxhgId = UUID.randomUUID().toString();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHcpxhg mainData = new DwdbCtfCertificateDetailHcpxhg();
            mainData.setHcpxhgId(hcpxhgId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段，同时记录已使用的属性
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("issuedOn1", dataMap, usedAttributes, mainData::setIssuedOn1);
            setFieldAndMarkUsed("issuedOn2", dataMap, usedAttributes, mainData::setIssuedOn2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("signatureOfDulyAutOffi1", dataMap, usedAttributes,
                    mainData::setSignatureOfDulyAutOffi1);
            setFieldAndMarkUsed("signatureOfDulyAutOffi2", dataMap, usedAttributes,
                    mainData::setSignatureOfDulyAutOffi2);
            setFieldAndMarkUsed("nameOfDulyAutOffi1", dataMap, usedAttributes, mainData::setNameOfDulyAutOffi1);
            setFieldAndMarkUsed("nameOfDulyAutOffi2", dataMap, usedAttributes, mainData::setNameOfDulyAutOffi2);
            setFieldAndMarkUsed("officalSeal1", dataMap, usedAttributes, mainData::setOfficalSeal1);
            setFieldAndMarkUsed("officalSeal2", dataMap, usedAttributes, mainData::setOfficalSeal2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);

            // 2. 处理培训项目信息表数据
            List<DwdbCtfCertificateDetailHcpxhgTraining> trainingList = new ArrayList<>();

            // 遍历可能的记录，按照2个一组处理
            for (int i = 1; i <= 31; i++) {
                String recordIndex = String.valueOf(i);
                int titleIndex = (i * 2) - 1; // 计算title索引: 1,3,5,7...
                int dateIndex = (i * 2) - 1; // 计算date索引: 1,3,5,7...

                // 先获取所有字段值，用于检查是否全部为空
                String prefix = dataMap.get("prefix" + recordIndex);
                String titleOfTheCertificate1 = dataMap.get("titleOfTheCertificate" + titleIndex);
                String titleOfTheCertificate2 = dataMap.get("titleOfTheCertificate" + (titleIndex + 1));
                String level = dataMap.get("level" + recordIndex);
                String dateOfIssue1 = dataMap.get("dateOfIssue" + dateIndex);
                String dateOfIssue2 = dataMap.get("dateOfIssue" + (dateIndex + 1));
                String dateOfExpiry1 = dataMap.get("dateOfExpiry" + dateIndex);
                String dateOfExpiry2 = dataMap.get("dateOfExpiry" + (dateIndex + 1));

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(prefix) && StringUtils.isBlank(titleOfTheCertificate1) &&
                        StringUtils.isBlank(titleOfTheCertificate2) && StringUtils.isBlank(level) &&
                        StringUtils.isBlank(dateOfIssue1) && StringUtils.isBlank(dateOfIssue2) &&
                        StringUtils.isBlank(dateOfExpiry1) && StringUtils.isBlank(dateOfExpiry2)) {
                    continue; // 跳过空记录
                }

                DwdbCtfCertificateDetailHcpxhgTraining training = new DwdbCtfCertificateDetailHcpxhgTraining();
                training.setDataId(source.getDataid()); // 设置源数据ID
                training.setHcpxhgTrainingId(UUID.randomUUID().toString());
                training.setHcpxhgId(hcpxhgId);
                training.setCreateTime(new Date());
                training.setUpdateTime(new Date());

                // 设置培训项目信息，同时记录已使用的属性
                // prefix1是第一条记录、prefix2是第二条记录...
                setFieldAndMarkUsed("prefix" + recordIndex, dataMap, usedAttributes, training::setPrefix);

                // titleOfTheCertificate1是培训项目名称(中文)、titleOfTheCertificate2是培训项目名称(英文)
                setFieldAndMarkUsed("titleOftheCertificate" + titleIndex, dataMap, usedAttributes,
                        training::setTitleOfTheCertificate1);
                setFieldAndMarkUsed("titleOftheCertificate" + (titleIndex + 1), dataMap, usedAttributes,
                        training::setTitleOfTheCertificate2);

                // level1是第一条记录、level2是第二条记录...
                setFieldAndMarkUsed("level" + recordIndex, dataMap, usedAttributes, training::setLevel);

                // dateOfIssue1、dateOfIssue2是第一条记录的数据
                setFieldAndMarkUsed("dateOfIssue" + dateIndex, dataMap, usedAttributes, training::setDateOfIssue1);
                setFieldAndMarkUsed("dateOfIssue" + (dateIndex + 1), dataMap, usedAttributes,
                        training::setDateOfIssue2);

                // dateOfExpiry1、dateOfExpiry2是第一条记录的数据
                setFieldAndMarkUsed("dateOfExpiry" + dateIndex, dataMap, usedAttributes, training::setDateOfExpiry1);
                setFieldAndMarkUsed("dateOfExpiry" + (dateIndex + 1), dataMap, usedAttributes,
                        training::setDateOfExpiry2);

                trainingList.add(training);
            }

            // 将所有数据放入结果Map
            result.put("mainData", mainData);
            result.put("trainingList", trainingList);

        } catch (Exception e) {
            log.error("解析海船船员培训合格证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException(
                    "解析海船船员培训合格证书数据失败" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 根据证照类型处理surface_data字段
     * 
     * @return 返回统一的转换结果对象，包含主表数据和子表数据
     */
    public CertificateConvertResult convertSurfaceData(OdsCertificateData source) throws Exception {
        CertificateConvertResult result = new CertificateConvertResult();
        result.setDataId(source.getDataid());
        result.setCertificateType(source.getCatalogname());

        if (source == null || StringUtils.isBlank(source.getSurfacedata())) {
            result.setHasError(true);
            result.setErrorMessage("源数据为空或surface_data为空");
            throw new RuntimeException("源数据为空或surface_data为空");
        }

        try {
            // 解析JSON数组
            JSONArray jsonArray = JSON.parseArray(source.getSurfacedata());
            Map<String, String> dataMap = new HashMap<>();
            Set<String> usedAttributes = new HashSet<>(); // 记录已使用的属性

            // 将JSON数据转换为Map
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i);
                String name = item.getString("name");
                String value = item.getString("value");
                dataMap.put(name, value);
            }

            // 根据证书类型进行转换
            switch (source.getCatalogname()) {
                case "不参加航行和轮机值班海船船员适任证书":
                    Map<String, Object> bcjhljzbData = convertBcjhljzbCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(bcjhljzbData.get("mainData"));
                    result.setSubTableDataList("capacities", (List<?>) bcjhljzbData.get("capacityList"));
                    break;
                case "船员适任证书申请表":
                    Map<String, Object> cysrzsqbData = convertCysrzsqbCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(cysrzsqbData.get("mainData"));
                    result.setSubTableDataList("experienceList", (List<?>) cysrzsqbData.get("experienceList"));
                    result.setSubTableDataList("optionsList", (List<?>) cysrzsqbData.get("optionsList"));
                    break;
                case "公务船船员适任证书":
                    Map<String, Object> gwccyData = convertGwccyCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(gwccyData.get("mainData"));
                    result.setSubTableDataList("capacities", (List<?>) gwccyData.get("capacityList"));
                    if (gwccyData.get("functionList") != null) {
                        result.setSubTableDataList("functions", (List<?>) gwccyData.get("functionList"));
                    }
                    break;
                case "海船高级船员适任证书":
                    Map<String, Object> hcgjcyData = convertHcgjcyCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hcgjcyData.get("mainData"));
                    if (hcgjcyData.get("capacityList") != null) {
                        result.setSubTableDataList("capacities", (List<?>) hcgjcyData.get("capacityList"));
                    }
                    if (hcgjcyData.get("functionList") != null) {
                        result.setSubTableDataList("functions", (List<?>) hcgjcyData.get("functionList"));
                    }
                    break;
                case "海船普通船员适任证书":
                    Map<String, Object> hcptcysrzData = convertHcptcysrzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hcptcysrzData.get("mainData"));
                    if (hcptcysrzData.get("capacityList") != null) {
                        result.setSubTableDataList("capacities", (List<?>) hcptcysrzData.get("capacityList"));
                    }
                    if (hcptcysrzData.get("functionList") != null) {
                        result.setSubTableDataList("functions", (List<?>) hcptcysrzData.get("functionList"));
                    }
                    break;
                case "海上非自航船舶船员适任证书":
                    Map<String, Object> hsfhcysrzData = convertHsfhcysrzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hsfhcysrzData.get("mainData"));
                    result.setSubTableDataList("shipList", (List<?>) hsfhcysrzData.get("shipList"));
                    break;
                case "海员外派机构资质证书":
                    DwdbCtfCertificateDetailHywpjg hywpjg = convertHywpjgCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hywpjg);
                    break;
                case "海船船员健康证明":
                    DwdbCtfCertificateDetailJkzm jkzm = convertJkzmCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(jkzm);
                    break;
                case "内河船舶船员适任证书":
                    DwdbCtfCertificateDetailNhcbcy nhcbcy = convertNhcbcyCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(nhcbcy);
                    break;
                case "海船船员内河航线行驶资格证明":
                    DwdbCtfCertificateDetailNhhxxs nhhxxs = convertNhhxxsCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(nhhxxs);
                    break;
                case "内河船舶船员培训合格证":
                case "内河船舶船员特殊培训合格证":
                    DwdbCtfCertificateDetailNhpxhg nhpxhg = convertNhpxhg(source, dataMap, usedAttributes);
                    result.setMainTableData(nhpxhg);
                    result.setSubTableDataList("nhpxhgItems", nhpxhg.getItems()); // 添加子表数据
                    break;
                case "船员培训质量管理体系证书":
                    DwdbCtfCertificateDetailQms qms = convertQmsCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(qms);
                    break;
                case "海船船员培训许可证":
                    Map<String, Object> seamanPermitData = convertSeamanPermitCertificate(source, dataMap,
                            usedAttributes);
                    result.setMainTableData(seamanPermitData.get("seamanInfo"));
                    result.setSubTableDataList("seamanPermit", seamanPermitData.get("seamanPermit"));
                    result.setSubTableDataList("itemList", seamanPermitData.get("itemList"));
                    break;
                case "特定航线江海直达船舶船员行驶资格证明培训合格证":
                    DwdbCtfCertificateDetailTdhxjh tdhxjh = convertTdhxjhCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(tdhxjh);
                    break;
                case "小型海船适任证书":
                    Map<String, Object> xhcsrzData = convertXhcsrzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(xhcsrzData.get("mainData"));
                    result.setSubTableDataList("capacities", (List<?>) xhcsrzData.get("capacityList"));
                    result.setSubTableDataList("functions", (List<?>) xhcsrzData.get("functionList"));
                    break;
                case "引航员船员适任证书":
                    Map<String, Object> yhysrzData = convertYhysrzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(yhysrzData.get("mainData"));
                    result.setSubTableDataList("rangeList", (List<?>) yhysrzData.get("rangeList"));
                    break;
                case "海船船员培训合格证书":
                    Map<String, Object> hcpxhgData = convertHcpxhgCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hcpxhgData.get("mainData"));
                    result.setSubTableDataList("trainingList", hcpxhgData.get("trainingList"));
                    break;
                case "游艇驾驶证（海上）":
                case "游艇驾驶证（内河）":
                case "游艇驾驶证内河":
                case "游艇驾驶证海上":
                case "游艇驾驶证":
                    Map<String, Object> ytjszData = convertYtjszCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(ytjszData.get("mainData"));
                    break;
                case "船上厨师培训合格证明":
                    Map<String, Object> cscspxData = convertCscspxCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(cscspxData.get("mainData"));
                    break;
                case "船上膳食服务辅助人员培训证明":
                    Map<String, Object> csssfzpxData = convertCsssfzpxCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(csssfzpxData.get("mainData"));
                    break;
                case "海船船员适任证书承认签证":
                    Map<String, Object> hccycrData = convertHccycrCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hccycrData.get("mainData"));
                    result.setSubTableDataList("capacities", (List<?>) hccycrData.get("capacityList"));
                    result.setSubTableDataList("functions", (List<?>) hccycrData.get("functionList"));
                    break;
                case "海上设施工作人员海上交通安全技能培训合格证明":
                    Map<String, Object> hsssjnData = convertHsssjnCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hsssjnData.get("mainData"));
                    break;
                case "海船不参加船员适任证书":
                    Map<String, Object> hcbcjcyData = convertHcbcjcyCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hcbcjcyData.get("mainData"));
                    result.setSubTableDataList("capacityList", (List<?>) hcbcjcyData.get("capacityList"));
                    break;
                case "内河船员培训许可证": // 添加内河船员培训许可证的处理分支
                    Map<String, Object> nhcyxkzData = convertNhcyxkzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(nhcyxkzData.get("mainData"));
                    result.setSubTableDataList("itemList", (List<?>) nhcyxkzData.get("itemList"));
                    break;
                case "海船船员特免证明":
                    Map<String, Object> hccytmData = convertHccytmCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hccytmData.get("mainData"));
                    result.setSubTableDataList("capacities", (List<?>) hccytmData.get("capacityList"));
                    result.setSubTableDataList("functions", (List<?>) hccytmData.get("functionList"));
                    break;
                case "海船船员培训合格证承认签证":
                    Map<String, Object> hcpxhgqzData = convertHcpxhgqzCertificate(source, dataMap, usedAttributes);
                    result.setMainTableData(hcpxhgqzData.get("mainData"));
                    result.setSubTableDataList("trainingList", (List<?>) hcpxhgqzData.get("trainingList"));
                    break;
                default:
                    // log.warn("未知的证照目录类型：{}", source.getCatalogname());
                    // result.setHasError(true);
                    // result.setErrorMessage("未知的证照目录类型：" + source.getCatalogname());
                    // 移除异常抛出，让程序继续执行
                    // throw new Exception("未知的证照目录类型：" + source.getCatalogname());
                    return result;
            }

            // 处理未使用的属性
            List<DwdbCertificateDataAttribute> unusedAttributes = new ArrayList<>();
            for (Map.Entry<String, String> entry : dataMap.entrySet()) {
                if (!usedAttributes.contains(entry.getKey())) {
                    DwdbCertificateDataAttribute attribute = new DwdbCertificateDataAttribute();
                    attribute.setCertificateAttributeId(UUID.randomUUID().toString());
                    attribute.setDataId(source.getDataid());
                    attribute.setAttributeColumnName(entry.getKey());
                    attribute.setAttributeValue(entry.getValue());
                    attribute.setRecCreateDate(new Date());
                    attribute.setRecModifyDate(new Date());
                    unusedAttributes.add(attribute);
                }
            }

            if (!unusedAttributes.isEmpty()) {
                result.setUnusedAttributes(unusedAttributes);
            }

        } catch (Exception e) {
            result.setHasError(true);
            result.setErrorMessage("转换失败: " + e.getMessage());
            log.error("转换失败", e);
            throw new RuntimeException(
                    "证照转换过程中发生异常" + "\n" + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理游艇驾驶证书的数据转换
     */
    private Map<String, Object> convertYtjszCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        DwdbCtfCertificateDetailYtjsz target = new DwdbCtfCertificateDetailYtjsz();

        try {
            // 1. 处理主表数据
            target.setYtjszId(UUID.randomUUID().toString());
            target.setDataId(source.getDataid());
            target.setCreateTime(new Date());
            target.setUpdateTime(new Date());

            // 设置基础字段 - 带有优先级的字段映射
            String certificateNo = getFirstNonBlankValue(dataMap, usedAttributes, "number", "certificateNo");
            target.setCertificateNo(certificateNo);

            String fullNameOfTheHolder1 = getFirstNonBlankValue(dataMap, usedAttributes, "name", "nameCn",
                    "fullNameOfTheHolder1");
            target.setFullNameOfTheHolder1(fullNameOfTheHolder1);

            String fullNameOfTheHolder2 = getFirstNonBlankValue(dataMap, usedAttributes, "nameEn",
                    "fullNameOfTheHolder2");
            target.setFullNameOfTheHolder2(fullNameOfTheHolder2);

            String nationality1 = getFirstNonBlankValue(dataMap, usedAttributes, "nationality", "countryCn",
                    "nationality1");
            target.setNationality1(nationality1);

            String nationality2 = getFirstNonBlankValue(dataMap, usedAttributes, "countryEn", "nationality2");
            target.setNationality2(nationality2);

            String dateOfBirth1 = getFirstNonBlankValue(dataMap, usedAttributes, "dateOfBirth", "birthCn",
                    "dateOfBirth1");
            target.setDateOfBirth1(dateOfBirth1);

            String dateOfBirth2 = getFirstNonBlankValue(dataMap, usedAttributes, "birthEn", "dateOfBirth2");
            target.setDateOfBirth2(dateOfBirth2);

            String gender1 = getFirstNonBlankValue(dataMap, usedAttributes, "sex", "sexCn", "gender1");
            target.setGender1(gender1);

            String gender2 = getFirstNonBlankValue(dataMap, usedAttributes, "sexEn", "gender2");
            target.setGender2(gender2);

            String dateOfExpiry1 = getFirstNonBlankValue(dataMap, usedAttributes, "dateOfExpiry", "expiryDateCn",
                    "dateOfExpiry1");
            target.setDateOfExpiry1(dateOfExpiry1);

            String dateOfExpiry2 = getFirstNonBlankValue(dataMap, usedAttributes, "expiryDateEn", "dateOfExpiry2");
            target.setDateOfExpiry2(dateOfExpiry2);

            String issuedOn1 = getFirstNonBlankValue(dataMap, usedAttributes, "initialDate", "initialDateCn",
                    "issuedOn1");
            target.setIssuedOn1(issuedOn1);

            String issuedOn2 = getFirstNonBlankValue(dataMap, usedAttributes, "initialDateEn", "issuedOn2");
            target.setIssuedOn2(issuedOn2);

            String initialDateCn = getFirstNonBlankValue(dataMap, usedAttributes, "issuedOn1", "initialDateCn",
                    "initialDate");
            target.setInitialDateCn(initialDateCn);

            String officeOfIssueCn = getFirstNonBlankValue(dataMap, usedAttributes, "issuingAdministration1",
                    "officeOfIssueCn", "officeOfissue");
            target.setOfficeOfIssueCn(officeOfIssueCn);

            String officeOfIssueEn = getFirstNonBlankValue(dataMap, usedAttributes, "issuingAdministration2",
                    "officeOfIssueEn");
            target.setOfficeOfIssueEn(officeOfIssueEn);

            // 设置其他基础字段
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, target::setInformationOfPhoto);
            setFieldAndMarkUsed("fileNoCn", dataMap, usedAttributes, target::setFileNoCn);
            setFieldAndMarkUsed("fileNoEn", dataMap, usedAttributes, target::setFileNoEn);
            setFieldAndMarkUsed("qualificationCn", dataMap, usedAttributes, target::setQualificationCn);
            setFieldAndMarkUsed("qualificationEn", dataMap, usedAttributes, target::setQualificationEn);
            setFieldAndMarkUsed("initialDateEn", dataMap, usedAttributes, target::setInitialDateEn);
            setFieldAndMarkUsed("signDeptCn", dataMap, usedAttributes, target::setSignDeptCn);
            setFieldAndMarkUsed("signDeptEn", dataMap, usedAttributes, target::setSignDeptEn);
            setFieldAndMarkUsed("date", dataMap, usedAttributes, target::setDate);
            setFieldAndMarkUsed("year", dataMap, usedAttributes, target::setYear);
            setFieldAndMarkUsed("month", dataMap, usedAttributes, target::setMonth);
            setFieldAndMarkUsed("day", dataMap, usedAttributes, target::setDay);

            result.put("mainData", target);

        } catch (Exception e) {
            log.error("解析游艇驾驶证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析游艇驾驶证书数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 获取多个字段中第一个非空值
     */
    private String getFirstNonBlankValue(Map<String, String> dataMap, Set<String> usedAttributes,
            String... fieldNames) {
        String value = null;
        for (String fieldName : fieldNames) {
            value = dataMap.get(fieldName);
            if (StringUtils.isNotBlank(value)) {
                usedAttributes.add(fieldName);
                return value;
            }
        }
        // 如果所有字段都为空，标记最后一个字段为已使用
        if (fieldNames.length > 0) {
            usedAttributes.add(fieldNames[fieldNames.length - 1]);
        }
        return "";
    }

    /**
     * 处理船上厨师培训合格证的数据转换
     */
    private Map<String, Object> convertCscspxCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailCscspx mainData = new DwdbCtfCertificateDetailCscspx();
            mainData.setCscspxId(UUID.randomUUID().toString());
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("nameOfTheTraingManager1", dataMap, usedAttributes,
                    mainData::setNameOfTheTraingManager1);
            setFieldAndMarkUsed("nameOfTheTraingManager2", dataMap, usedAttributes,
                    mainData::setNameOfTheTraingManager2);
            setFieldAndMarkUsed("issuingBody1", dataMap, usedAttributes, mainData::setIssuingBody1);
            setFieldAndMarkUsed("issuingBody2", dataMap, usedAttributes, mainData::setIssuingBody2);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingBody", dataMap, usedAttributes, mainData::setIssuingBody);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);

            // 将数据放入结果Map
            result.put("mainData", mainData);

        } catch (Exception e) {
            log.error("解析船上厨师培训合格证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析船上厨师培训合格证数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理船上膳食服务辅助人员培训证明的数据转换
     */
    private Map<String, Object> convertCsssfzpxCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailCsssfzpx mainData = new DwdbCtfCertificateDetailCsssfzpx();
            mainData.setCsssfzpxId(UUID.randomUUID().toString());
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("nameOfTheTraingManager1", dataMap, usedAttributes,
                    mainData::setNameOfTheTraingManager1);
            setFieldAndMarkUsed("nameOfTheTraingManager2", dataMap, usedAttributes,
                    mainData::setNameOfTheTraingManager2);
            setFieldAndMarkUsed("issuingBody1", dataMap, usedAttributes, mainData::setIssuingBody1);
            setFieldAndMarkUsed("issuingBody2", dataMap, usedAttributes, mainData::setIssuingBody2);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingBody", dataMap, usedAttributes, mainData::setIssuingBody);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);

            // 将数据放入结果Map
            result.put("mainData", mainData);

        } catch (Exception e) {
            log.error("解析船上膳食服务辅助人员培训证明数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析船上膳食服务辅助人员培训证明数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage()
                    + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船船员适任证书承认签证的数据转换
     */
    private Map<String, Object> convertHccycrCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertificateDetailHccycrCapacity> capacityList = new ArrayList<>();
        List<DwdbCtfCertificateDetailHccycrFunction> functionList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHccycr mainData = new DwdbCtfCertificateDetailHccycr();
            String hccycrId = UUID.randomUUID().toString();
            mainData.setHccycrId(hccycrId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameOfTheHolder1", dataMap, usedAttributes, mainData::setHolderName1);
            setFieldAndMarkUsed("fullNameOfTheHolder2", dataMap, usedAttributes, mainData::setHolderName2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("dateOfExpiry1", dataMap, usedAttributes, mainData::setDateOfExpiry1);
            setFieldAndMarkUsed("dateOfExpiry2", dataMap, usedAttributes, mainData::setDateOfExpiry2);
            setFieldAndMarkUsed("issuedOn1", dataMap, usedAttributes, mainData::setIssuedOn1);
            setFieldAndMarkUsed("issuedOn2", dataMap, usedAttributes, mainData::setIssuedOn2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("articleNumber1", dataMap, usedAttributes, mainData::setArticleNumber1);
            setFieldAndMarkUsed("articleNumber2", dataMap, usedAttributes, mainData::setArticleNumber2);
            setFieldAndMarkUsed("articleNumber3", dataMap, usedAttributes, mainData::setArticleNumber3);
            setFieldAndMarkUsed("articleNumber4", dataMap, usedAttributes, mainData::setArticleNumber4);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingAminstration1", dataMap, usedAttributes, mainData::setIssuingAminstration1);
            setFieldAndMarkUsed("issuingAminstration2", dataMap, usedAttributes, mainData::setIssuingAminstration2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息
            for (int i = 1; i <= 62; i += 2) {
                String capacityPrefix1 = String.valueOf(i);
                String capacityPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String capacity1 = dataMap.get("capacity" + capacityPrefix1);
                String capacity2 = dataMap.get("capacity" + capacityPrefix2);
                String alimitationsApplying1 = dataMap.get("alimitationsApplying" + capacityPrefix1);
                String alimitationsApplying2 = dataMap.get("alimitationsApplying" + capacityPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(capacity1) && StringUtils.isBlank(capacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertificateDetailHccycrCapacity capacity = new DwdbCtfCertificateDetailHccycrCapacity();
                capacity.setHccycrCapacityId(UUID.randomUUID().toString());
                capacity.setHccycrId(hccycrId);
                capacity.setDataId(source.getDataid());
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                setFieldAndMarkUsed("capacity" + capacityPrefix1, dataMap, usedAttributes, capacity::setCapacity1);
                setFieldAndMarkUsed("capacity" + capacityPrefix2, dataMap, usedAttributes, capacity::setCapacity2);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 3. 处理职能信息
            for (int i = 1; i <= 62; i += 2) {
                String functionPrefix1 = String.valueOf(i);
                String functionPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String function1 = dataMap.get("function" + functionPrefix1);
                String function2 = dataMap.get("function" + functionPrefix2);
                String level1 = dataMap.get("level" + functionPrefix1);
                String level2 = dataMap.get("level" + functionPrefix2);
                String limitationsApplying1 = dataMap.get("limitationsApplying" + functionPrefix1);
                String limitationsApplying2 = dataMap.get("limitationsApplying" + functionPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(function1) && StringUtils.isBlank(function2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2) &&
                        StringUtils.isBlank(limitationsApplying1) && StringUtils.isBlank(limitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertificateDetailHccycrFunction function = new DwdbCtfCertificateDetailHccycrFunction();
                function.setHccycrFunctionId(UUID.randomUUID().toString());
                function.setHccycrId(hccycrId);
                function.setDataId(source.getDataid());
                function.setCreateTime(new Date());
                function.setUpdateTime(new Date());

                setFieldAndMarkUsed("function" + functionPrefix1, dataMap, usedAttributes, function::setFunction1);
                setFieldAndMarkUsed("function" + functionPrefix2, dataMap, usedAttributes, function::setFunction2);
                setFieldAndMarkUsed("level" + functionPrefix1, dataMap, usedAttributes, function::setLevel1);
                setFieldAndMarkUsed("level" + functionPrefix2, dataMap, usedAttributes, function::setLevel2);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix1, dataMap, usedAttributes,
                        function::setLimitationsApplying1);
                setFieldAndMarkUsed("limitationsApplying" + functionPrefix2, dataMap, usedAttributes,
                        function::setLimitationsApplying2);

                functionList.add(function);
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);
            result.put("functionList", functionList);

        } catch (Exception e) {
            log.error("解析海船船员适任证书承认签证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船船员适任证书承认签证数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海上设施工作人员海上交通安全技能培训合格证明的数据转换
     */
    private Map<String, Object> convertHsssjnCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();

        try {
            DwdbCtfCertificateDetailHsssjn mainData = new DwdbCtfCertificateDetailHsssjn();
            mainData.setHsssjnId(UUID.randomUUID().toString());
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("EditBox1", dataMap, usedAttributes, mainData::setEditBox1);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("anThorityName1", dataMap, usedAttributes, mainData::setAnThorityName1);
            setFieldAndMarkUsed("anThorityName2", dataMap, usedAttributes, mainData::setAnThorityName2);
            setFieldAndMarkUsed("evaluationOrganization", dataMap, usedAttributes,
                    mainData::setEvaluationOrganization1);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("year1", dataMap, usedAttributes, mainData::setYear1);
            setFieldAndMarkUsed("month1", dataMap, usedAttributes, mainData::setMonth1);
            setFieldAndMarkUsed("day1", dataMap, usedAttributes, mainData::setDay1);
            setFieldAndMarkUsed("year2", dataMap, usedAttributes, mainData::setYear2);
            setFieldAndMarkUsed("month2", dataMap, usedAttributes, mainData::setMonth2);
            setFieldAndMarkUsed("day2", dataMap, usedAttributes, mainData::setDay2);
            setFieldAndMarkUsed("IDNumber", dataMap, usedAttributes, mainData::setIDNumber);
            setFieldAndMarkUsed("passportNumber", dataMap, usedAttributes, mainData::setPassportNumber);

            result.put("mainData", mainData);

        } catch (Exception e) {
            log.error("解析海上设施工作人员海上交通安全技能培训合格证明数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海上设施工作人员海上交通安全技能培训合格证明数据失败，dataId: " + source.getDataid() + "\n"
                    + e.getMessage() + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船不参加船员适任证书的数据转换
     */
    private Map<String, Object> convertHcbcjcyCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertificateDetailHcbcjcyCapacity> capacityList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailHcbcjcy mainData = new DwdbCtfCertificateDetailHcbcjcy();
            String hcbcjcyId = UUID.randomUUID().toString();
            mainData.setHcbcjcyId(hcbcjcyId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("fullNameoftheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameoftheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("certificateExpiringDate1", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate1);
            setFieldAndMarkUsed("certificateExpiringDate2", dataMap, usedAttributes,
                    mainData::setCertificateExpiringDate2);
            setFieldAndMarkUsed("dateOfIssue1", dataMap, usedAttributes, mainData::setDateOfIssue1);
            setFieldAndMarkUsed("dateOfIssue2", dataMap, usedAttributes, mainData::setDateOfIssue2);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setInformationOfPhoto);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息
            for (int i = 1; i <= 62; i += 2) {
                String capacityPrefix1 = String.valueOf(i);
                String capacityPrefix2 = String.valueOf(i + 1);

                // 先获取所有字段值，用于检查是否全部为空
                String gradwAndCapacity1 = dataMap.get("gradwAndCapacity" + capacityPrefix1);
                String gradwAndCapacity2 = dataMap.get("gradwAndCapacity" + capacityPrefix2);
                String alimitationsApplying1 = dataMap.get("alimitationsApplying" + capacityPrefix1);
                String alimitationsApplying2 = dataMap.get("alimitationsApplying" + capacityPrefix2);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(gradwAndCapacity1) && StringUtils.isBlank(gradwAndCapacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertificateDetailHcbcjcyCapacity capacity = new DwdbCtfCertificateDetailHcbcjcyCapacity();
                capacity.setHcbcjcyCapacityId(UUID.randomUUID().toString());
                capacity.setHcbcjcyId(hcbcjcyId);
                capacity.setDataId(source.getDataid());
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity1);
                setFieldAndMarkUsed("gradwAndCapacity" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setGradwAndCapacity2);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix1, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying1);
                setFieldAndMarkUsed("alimitationsApplying" + capacityPrefix2, dataMap, usedAttributes,
                        capacity::setAlimitationsApplying2);

                capacityList.add(capacity);
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);

        } catch (Exception e) {
            log.error("解析海船不参加船员适任证书数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船不参加船员适任证书数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理内河船员培训许可证的数据转换
     */
    private Map<String, Object> convertNhcyxkzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertificateDetailNhcyxkzItem> itemList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertificateDetailNhcyxkz mainData = new DwdbCtfCertificateDetailNhcyxkz();
            String nhcyxkzId = UUID.randomUUID().toString();
            mainData.setNhcyxkzId(nhcyxkzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 设置基础字段
            setFieldAndMarkUsed("permitNumber1", dataMap, usedAttributes, mainData::setPermitNumber1);
            setFieldAndMarkUsed("anThorityName1", dataMap, usedAttributes, mainData::setAnThorityName1);
            setFieldAndMarkUsed("trainingInstitutionCode1", dataMap, usedAttributes,
                    mainData::setTrainingInstitutionCode1);
            setFieldAndMarkUsed("representative1", dataMap, usedAttributes, mainData::setRepresentative1);
            setFieldAndMarkUsed("trainingProgram1", dataMap, usedAttributes, mainData::setTrainingProgram1);
            setFieldAndMarkUsed("trainingProgram2", dataMap, usedAttributes, mainData::setTrainingProgram2);
            setFieldAndMarkUsed("registeredAddress1", dataMap, usedAttributes, mainData::setRegisteredAddress1);
            setFieldAndMarkUsed("trainingLocation1", dataMap, usedAttributes, mainData::setTrainingLocation1);
            setFieldAndMarkUsed("periodOfValidity1", dataMap, usedAttributes, mainData::setPeriodOfValidity1);
            setFieldAndMarkUsed("periodOfValidity2", dataMap, usedAttributes, mainData::setPeriodOfValidity2);
            setFieldAndMarkUsed("issuingAuthority1", dataMap, usedAttributes, mainData::setIssuingAuthority1);
            setFieldAndMarkUsed("dateofIssue1", dataMap, usedAttributes, mainData::setDateofIssue1);
            setFieldAndMarkUsed("permitNumber2", dataMap, usedAttributes, mainData::setPermitNumber2);
            setFieldAndMarkUsed("anThorityName2", dataMap, usedAttributes, mainData::setAnThorityName2);
            setFieldAndMarkUsed("registeredAddress2", dataMap, usedAttributes, mainData::setRegisteredAddress2);
            setFieldAndMarkUsed("representative2", dataMap, usedAttributes, mainData::setRepresentative2);
            setFieldAndMarkUsed("trainingLocation2", dataMap, usedAttributes, mainData::setTrainingLocation2);
            setFieldAndMarkUsed("periodOfValidity3", dataMap, usedAttributes, mainData::setPeriodOfValidity3);
            setFieldAndMarkUsed("periodOfValidity4", dataMap, usedAttributes, mainData::setPeriodOfValidity4);
            setFieldAndMarkUsed("remarks", dataMap, usedAttributes, mainData::setRemarks);
            setFieldAndMarkUsed("issuingAuthority2", dataMap, usedAttributes, mainData::setIssuingAuthority2);
            setFieldAndMarkUsed("dateofIssue2", dataMap, usedAttributes, mainData::setDateofIssue2);

            // 2. 处理培训项目子表数据
            for (int i = 1; i <= 20; i++) {
                String numberPrefix = String.valueOf(i);

                // 先获取所有字段值，用于检查是否全部为空
                String number = dataMap.get("number" + numberPrefix);
                String atrainingProgram = dataMap.get("atrainingProgram" + numberPrefix);
                String trainingScale = dataMap.get("trainingScale" + numberPrefix);

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(number) && StringUtils.isBlank(atrainingProgram) &&
                        StringUtils.isBlank(trainingScale)) {
                    continue;
                }

                DwdbCtfCertificateDetailNhcyxkzItem item = new DwdbCtfCertificateDetailNhcyxkzItem();
                item.setNhcyxkzItemId(UUID.randomUUID().toString());
                item.setNhcyxkzId(nhcyxkzId);
                item.setCreateTime(new Date());
                item.setUpdateTime(new Date());

                setFieldAndMarkUsed("number" + numberPrefix, dataMap, usedAttributes, item::setNumber);
                setFieldAndMarkUsed("atrainingProgram" + numberPrefix, dataMap, usedAttributes,
                        item::setAtrainingProgram);
                setFieldAndMarkUsed("trainingScale" + numberPrefix, dataMap, usedAttributes, item::setTrainingScale);

                itemList.add(item);
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("itemList", itemList);

        } catch (Exception e) {
            log.error("解析内河船员培训许可证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析内河船员培训许可证数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船船员特免证明的数据转换
     */
    private Map<String, Object> convertHccytmCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertDetailHccytmCap> capacityList = new ArrayList<>();
        List<DwdbCtfCertDetailHccytmFunc> functionList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertDetailHccytm mainData = new DwdbCtfCertDetailHccytm();
            String hccytmId = UUID.randomUUID().toString();
            mainData.setHccytmId(hccytmId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 根据表字段名直接从JSON中获取值
            setFieldAndMarkUsed("holderName1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("holderName2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("dateOfExpiry1", dataMap, usedAttributes, mainData::setDateOfExpiry1);
            setFieldAndMarkUsed("dateOfExpiry2", dataMap, usedAttributes, mainData::setDateOfExpiry2);
            setFieldAndMarkUsed("issuedOn1", dataMap, usedAttributes, mainData::setIssuedOn1);
            setFieldAndMarkUsed("issuedOn2", dataMap, usedAttributes, mainData::setIssuedOn2);
            setFieldAndMarkUsed("certificateHolderName", dataMap, usedAttributes, mainData::setCertificateHolderName);
            setFieldAndMarkUsed("articleNumber1", dataMap, usedAttributes, mainData::setArticleNumber1);
            setFieldAndMarkUsed("articleNumber2", dataMap, usedAttributes, mainData::setArticleNumber2);
            setFieldAndMarkUsed("articleNumber3", dataMap, usedAttributes, mainData::setArticleNumber3);
            setFieldAndMarkUsed("articleNumber4", dataMap, usedAttributes, mainData::setArticleNumber4);
            setFieldAndMarkUsed("informationOfPhoto", dataMap, usedAttributes, mainData::setPhoto);
            setFieldAndMarkUsed("issuingAminstration1", dataMap, usedAttributes, mainData::setIssuingAminstration1);
            setFieldAndMarkUsed("issuingAminstration2", dataMap, usedAttributes, mainData::setIssuingAminstration2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息表数据
            for (int i = 1; i <= 50; i += 2) { // 假设最多50组数据
                String capacity1 = dataMap.get("capacity" + i);
                String capacity2 = dataMap.get("capacity" + (i + 1));
                String alimitationsApplying1 = dataMap.get("alimitations_applying" + i);
                String alimitationsApplying2 = dataMap.get("alimitations_applying" + (i + 1));

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(capacity1) && StringUtils.isBlank(capacity2) &&
                        StringUtils.isBlank(alimitationsApplying1) && StringUtils.isBlank(alimitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertDetailHccytmCap capacity = new DwdbCtfCertDetailHccytmCap();
                capacity.setHccytmCapacityId(UUID.randomUUID().toString());
                capacity.setHccytmId(hccytmId);
                capacity.setCreateTime(new Date());
                capacity.setUpdateTime(new Date());

                // 设置职务等级字段
                capacity.setCapacity1(StringUtils.isNotBlank(capacity1) ? capacity1 : "");
                capacity.setCapacity2(StringUtils.isNotBlank(capacity2) ? capacity2 : "");
                capacity.setAlimitationsApplying1(
                        StringUtils.isNotBlank(alimitationsApplying1) ? alimitationsApplying1 : "");
                capacity.setAlimitationsApplying2(
                        StringUtils.isNotBlank(alimitationsApplying2) ? alimitationsApplying2 : "");

                // 标记为已使用
                usedAttributes.add("capacity" + i);
                usedAttributes.add("capacity" + (i + 1));
                usedAttributes.add("alimitations_applying" + i);
                usedAttributes.add("alimitations_applying" + (i + 1));

                capacityList.add(capacity);
            }

            // 3. 处理职能信息表数据
            for (int i = 1; i <= 50; i += 2) { // 假设最多50组数据
                String function1 = dataMap.get("function" + i);
                String function2 = dataMap.get("function" + (i + 1));
                String level1 = dataMap.get("level" + i);
                String level2 = dataMap.get("level" + (i + 1));
                String limitationsApplying1 = dataMap.get("limitations_applying" + i);
                String limitationsApplying2 = dataMap.get("limitations_applying" + (i + 1));

                // 检查是否所有字段都为空，如果是则跳过此记录
                if (StringUtils.isBlank(function1) && StringUtils.isBlank(function2) &&
                        StringUtils.isBlank(level1) && StringUtils.isBlank(level2) &&
                        StringUtils.isBlank(limitationsApplying1) && StringUtils.isBlank(limitationsApplying2)) {
                    continue;
                }

                DwdbCtfCertDetailHccytmFunc function = new DwdbCtfCertDetailHccytmFunc();
                function.setHccytmFunctionId(UUID.randomUUID().toString());
                function.setHccytmId(hccytmId);
                function.setCreateTime(new Date());
                function.setUpdateTime(new Date());

                // 设置职能字段
                function.setFunction1(StringUtils.isNotBlank(function1) ? function1 : "");
                function.setFunction2(StringUtils.isNotBlank(function2) ? function2 : "");
                function.setLevel1(StringUtils.isNotBlank(level1) ? level1 : "");
                function.setLevel2(StringUtils.isNotBlank(level2) ? level2 : "");
                function.setLimitationsApplying1(
                        StringUtils.isNotBlank(limitationsApplying1) ? limitationsApplying1 : "");
                function.setLimitationsApplying2(
                        StringUtils.isNotBlank(limitationsApplying2) ? limitationsApplying2 : "");

                // 标记为已使用
                usedAttributes.add("function" + i);
                usedAttributes.add("function" + (i + 1));
                usedAttributes.add("level" + i);
                usedAttributes.add("level" + (i + 1));
                usedAttributes.add("limitations_applying" + i);
                usedAttributes.add("limitations_applying" + (i + 1));

                functionList.add(function);
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("capacityList", capacityList);
            result.put("functionList", functionList);

        } catch (Exception e) {
            log.error("解析海船船员特免证明数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船船员特免证明数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage() + "\n"
                    + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }

    /**
     * 处理海船船员培训合格证承认签证的数据转换
     */
    private Map<String, Object> convertHcpxhgqzCertificate(
            OdsCertificateData source,
            Map<String, String> dataMap,
            Set<String> usedAttributes) {

        Map<String, Object> result = new HashMap<>();
        List<DwdbCtfCertDetailHcpxqzTrain> trainingList = new ArrayList<>();

        try {
            // 1. 处理主表数据
            DwdbCtfCertDetailHcpxhgqz mainData = new DwdbCtfCertDetailHcpxhgqz();
            String hcpxhgqzId = UUID.randomUUID().toString();
            mainData.setHcpxhgqzId(hcpxhgqzId);
            mainData.setDataId(source.getDataid());
            mainData.setCreateTime(new Date());
            mainData.setUpdateTime(new Date());

            // 根据表字段名直接从JSON中获取值
            setFieldAndMarkUsed("fullNameOfTheHolder1", dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);
            setFieldAndMarkUsed("fullNameOfTheHolder2", dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);
            setFieldAndMarkUsed("nationality1", dataMap, usedAttributes, mainData::setNationality1);
            setFieldAndMarkUsed("nationality2", dataMap, usedAttributes, mainData::setNationality2);
            setFieldAndMarkUsed("dateOfBirth1", dataMap, usedAttributes, mainData::setDateOfBirth1);
            setFieldAndMarkUsed("dateOfBirth2", dataMap, usedAttributes, mainData::setDateOfBirth2);
            setFieldAndMarkUsed("gender1", dataMap, usedAttributes, mainData::setGender1);
            setFieldAndMarkUsed("gender2", dataMap, usedAttributes, mainData::setGender2);
            setFieldAndMarkUsed("certificateNo", dataMap, usedAttributes, mainData::setCertificateNo);
            setFieldAndMarkUsed("dateOfExpiry1", dataMap, usedAttributes, mainData::setDateOfExpiry1);
            setFieldAndMarkUsed("dateOfExpiry2", dataMap, usedAttributes, mainData::setDateOfExpiry2);
            setFieldAndMarkUsed("issuedOn1", dataMap, usedAttributes, mainData::setIssuedOn1);
            setFieldAndMarkUsed("issuedOn2", dataMap, usedAttributes, mainData::setIssuedOn2);
            setFieldAndMarkUsed("articleNumber1", dataMap, usedAttributes, mainData::setArticleNumber1);
            setFieldAndMarkUsed("articleNumber2", dataMap, usedAttributes, mainData::setArticleNumber2);
            setFieldAndMarkUsed("articleNumber3", dataMap, usedAttributes, mainData::setArticleNumber3);
            setFieldAndMarkUsed("articleNumber4", dataMap, usedAttributes, mainData::setArticleNumber4);
            setFieldAndMarkUsed("photo", dataMap, usedAttributes, mainData::setPhoto);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial1", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial1);
            setFieldAndMarkUsed("nameOfDulyAuthorizedOfficial2", dataMap, usedAttributes,
                    mainData::setNameOfDulyAuthorizedOfficial2);
            setFieldAndMarkUsed("issuingAminstration1", dataMap, usedAttributes, mainData::setIssuingAminstration1);
            setFieldAndMarkUsed("issuingAminstration2", dataMap, usedAttributes, mainData::setIssuingAminstration2);
            setFieldAndMarkUsed("officialUseOnly1", dataMap, usedAttributes, mainData::setOfficialUseOnly1);
            setFieldAndMarkUsed("officialUseOnly2", dataMap, usedAttributes, mainData::setOfficialUseOnly2);

            // 2. 处理培训项目子表数据
            // title_of_the_certificate1、title_of_the_certificate2 是第一条记录
            // title_of_the_certificate3、title_of_the_certificate4 是第二条记录
            // certificate_no1、certificate_no2... 按照相同规则
            // date_of_expiry3、date_of_expiry4 是第一条记录（从3,4开始）
            // clause1 是第一条记录，clause2 是第二条记录

            int recordIndex = 1;
            while (true) {
                // 计算当前记录的索引偏移
                int titleIndex1 = recordIndex * 2 - 1; // 1, 3, 5, 7...
                int titleIndex2 = recordIndex * 2; // 2, 4, 6, 8...
                int certIndex1 = recordIndex * 2 - 1; // 1, 3, 5, 7...
                int certIndex2 = recordIndex * 2; // 2, 4, 6, 8...
                int expiryIndex1 = recordIndex * 2 + 1; // 3, 5, 7, 9... (从3开始)
                int expiryIndex2 = recordIndex * 2 + 2; // 4, 6, 8, 10... (从4开始)
                int clauseIndex = recordIndex; // 1, 2, 3, 4...

                // 获取当前记录的所有字段值
                String titleOfTheCertificate1 = dataMap.get("titleOfTheCertificate" + titleIndex1);
                String titleOfTheCertificate2 = dataMap.get("titleOfTheCertificate" + titleIndex2);
                String certificateNo1 = dataMap.get("certificateNo" + certIndex1);
                String certificateNo2 = dataMap.get("certificateNo" + certIndex2);
                String dateOfExpiry3 = dataMap.get("dateOfExpiry" + expiryIndex1);
                String dateOfExpiry4 = dataMap.get("dateOfExpiry" + expiryIndex2);
                String clause = dataMap.get("clause" + clauseIndex);

                // 检查是否所有字段都为空，如果是则跳出循环
                if (StringUtils.isBlank(titleOfTheCertificate1) && StringUtils.isBlank(titleOfTheCertificate2) &&
                        StringUtils.isBlank(certificateNo1) && StringUtils.isBlank(certificateNo2) &&
                        StringUtils.isBlank(dateOfExpiry3) && StringUtils.isBlank(dateOfExpiry4) &&
                        StringUtils.isBlank(clause)) {
                    break;
                }

                DwdbCtfCertDetailHcpxqzTrain training = new DwdbCtfCertDetailHcpxqzTrain();
                training.setHcpxhgqzTrainingId(UUID.randomUUID().toString());
                training.setHcpxhgqzId(hcpxhgqzId);
                training.setCreateTime(new Date());
                training.setUpdateTime(new Date());

                // 设置培训项目字段
                training.setTitleOfTheCertificate1(
                        StringUtils.isNotBlank(titleOfTheCertificate1) ? titleOfTheCertificate1 : "");
                training.setTitleOfTheCertificate2(
                        StringUtils.isNotBlank(titleOfTheCertificate2) ? titleOfTheCertificate2 : "");
                training.setCertificateNo1(StringUtils.isNotBlank(certificateNo1) ? certificateNo1 : "");
                training.setCertificateNo2(StringUtils.isNotBlank(certificateNo2) ? certificateNo2 : "");
                training.setDateOfExpiry3(StringUtils.isNotBlank(dateOfExpiry3) ? dateOfExpiry3 : "");
                training.setDateOfExpiry4(StringUtils.isNotBlank(dateOfExpiry4) ? dateOfExpiry4 : "");
                training.setClause(StringUtils.isNotBlank(clause) ? clause : "");

                // 标记为已使用
                usedAttributes.add("titleOfTheCertificate" + titleIndex1);
                usedAttributes.add("titleOfTheCertificate" + titleIndex2);
                usedAttributes.add("certificateNo" + certIndex1);
                usedAttributes.add("certificateNo" + certIndex2);
                usedAttributes.add("dateOfExpiry" + expiryIndex1);
                usedAttributes.add("dateOfExpiry" + expiryIndex2);
                usedAttributes.add("clause" + clauseIndex);

                trainingList.add(training);
                recordIndex++;

                // 防止无限循环，最多处理50条记录
                if (recordIndex > 50) {
                    break;
                }
            }

            // 将数据放入结果Map
            result.put("mainData", mainData);
            result.put("trainingList", trainingList);

        } catch (Exception e) {
            log.error("解析海船船员培训合格证承认签证数据失败，dataId: " + source.getDataid(), e);
            throw new RuntimeException("解析海船船员培训合格证承认签证数据失败，dataId: " + source.getDataid() + "\n" + e.getMessage()
                    + "\n" + Arrays.toString(e.getStackTrace()), e);
        }

        return result;
    }
}