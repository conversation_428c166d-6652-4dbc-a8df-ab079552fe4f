spring:
  datasource:
    # 汇聚库数据源配置
    aggregate:
      jdbc-url: ******************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 10               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 60000                 # 空闲连接超时时间(毫秒)
      connection-timeout: 30000           # 连接超时时间(毫秒)
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: AggregateHikariCP        # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)
      
    # 标准库数据源配置
    standard:
      jdbc-url: ********************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 10               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 60000                 # 空闲连接超时时间(毫秒)
      connection-timeout: 30000           # 连接超时时间(毫秒)
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: StandardHikariCP         # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)

# 任务配置
task:
  certificate:
    cron: "0/10 0 * * * ?" # 每1分钟执行一次
    taskName: "ods_certificate_data" 