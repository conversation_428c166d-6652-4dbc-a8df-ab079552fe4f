spring:
  datasource:
    # 汇聚库数据源配置
    aggregate:
      jdbc-url: ***************************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 20               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 300000                # 空闲连接超时时间(毫秒) - 增加到5分钟
      connection-timeout: 60000           # 连接超时时间(毫秒) - 增加到1分钟
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: StandardHikariCP         # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)
      keepalive-time: 60000              # 心跳检测时间(毫秒) - 新增
      register-mbeans: true              # 注册JMX监控 - 新增
      initialization-fail-timeout: -1     # 连接池初始化失败时无限重试 - 新增
      
    # 标准库数据源配置
    standard:
      jdbc-url: *****************************************************************************************************************
      username: system
      password: system
      driver-class-name: org.postgresql.Driver
      # 连接池优化配置
      maximum-pool-size: 20               # 连接池最大连接数
      minimum-idle: 5                     # 连接池最小空闲连接数
      idle-timeout: 300000                # 空闲连接超时时间(毫秒) - 增加到5分钟
      connection-timeout: 60000           # 连接超时时间(毫秒) - 增加到1分钟
      max-lifetime: 1800000               # 连接最大生命周期(毫秒，30分钟)
      auto-commit: true                   # 自动提交
      pool-name: StandardHikariCP         # 连接池名称
      validation-timeout: 5000            # 验证连接的最大等待时间(毫秒)
      connection-test-query: SELECT 1     # 连接测试查询
      leak-detection-threshold: 60000     # 连接泄露检测阈值(毫秒)
      keepalive-time: 60000              # 心跳检测时间(毫秒) - 新增
      register-mbeans: true              # 注册JMX监控 - 新增
      initialization-fail-timeout: -1     # 连接池初始化失败时无限重试 - 新增

# 任务配置
task:
  certificate:
    cron: "0 0/1 * * * ?" # 每1分钟执行一次
    taskName: "ods_certificate_data" 