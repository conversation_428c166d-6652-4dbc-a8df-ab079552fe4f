spring:
  profiles:
    active: uat

logging:
  file:
    name: logs/certificate-etl.log
  level:
    root: INFO
    com.example.certificate: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.example.certificate.entity 