<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.certificate.mapper.standard.DataReceptionTaskMapper">

    <select id="getTaskByName" resultType="com.example.certificate.entity.standard.DataReceptionTask">
        SELECT * FROM data_reception_tasks WHERE task_name = #{taskName}
    </select>

    <update id="updateLastCompletedTimeAndDataId">
        UPDATE data_reception_tasks 
        SET last_completed_time = #{lastCompletedTime},
            last_data_id = #{lastDataId}
        WHERE task_name = #{taskName}
    </update>

</mapper> 