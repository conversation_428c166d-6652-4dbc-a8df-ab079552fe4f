package com.example.certificate.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

@TestConfiguration
public class TestConfig {

    @Bean
    @Qualifier("aggregateJdbcTemplate")
    public JdbcTemplate aggregateJdbcTemplate(@Qualifier("aggregateDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
    
    @Bean
    @Qualifier("standardJdbcTemplate")
    public JdbcTemplate standardJdbcTemplate(@Qualifier("standardDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}