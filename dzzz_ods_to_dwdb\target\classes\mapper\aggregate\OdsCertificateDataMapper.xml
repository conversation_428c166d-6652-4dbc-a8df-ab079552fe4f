<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.certificate.mapper.aggregate.OdsCertificateDataMapper">

    <select id="selectIncrementalData" resultType="com.example.certificate.entity.aggregate.OdsCertificateData">
        SELECT *
        FROM ods_certificate_data
        WHERE fcdc_date > #{startTime}           
          AND fcdc_date &lt;= (CURRENT_TIMESTAMP - INTERVAL '5 minutes')
        ORDER BY fcdc_date ASC
        LIMIT 100000
    </select>

    <select id="getCurrentTime" resultType="java.util.Date">
        SELECT CURRENT_TIMESTAMP
    </select>

</mapper> 