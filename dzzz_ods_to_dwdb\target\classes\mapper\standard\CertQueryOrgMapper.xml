<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.certificate.mapper.standard.CertQueryOrgMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_query_org (
            QUEYR_ORG_ID, 
            QUERY_ORG_NAME,
            QUERY_ORG_TYPE,
            rec_create_date, 
            rec_modify_date
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.queryOrgId}, 
                #{item.queryOrgName},
                #{item.queryOrgType},
                #{item.recCreateDate}, 
                #{item.recModifyDate}
            )
        </foreach>
    </insert>
</mapper> 