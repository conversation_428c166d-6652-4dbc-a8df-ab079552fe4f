<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.certificate.mapper.standard.DwdbCertificateDataMapper">

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO dwdb_certificate_data (
            data_id, certificate_id, template_id,
            certificate_type_name, certificate_type_code,
            certificate_define_authority_name, certificate_define_authority_code,
            related_item_name, related_item_code,
            certificate_holder_category, certificate_holder_category_name,
            validity_range, certificate_identifier,
            certificate_name, certificate_number,
            certificate_issuing_authority_name, certificate_issuing_authority_code,
            certificate_issued_date, certificate_holder_name,
            certificate_holder_code, certificate_holder_type_name,
            certificate_effective_date, certificate_expiring_date,
            issue_dept_code2, issue_dept_code3,
            certificate_area_code, surface_data,
            certificate_status, creator_id,
            create_time, operator_id,
            update_time, file_path,
            sync_status, remarks,
            dept_id, apply_num,
            affair_type, serve_business,
            affair_id, affair_num,
            qz_type, draft_url,
            sort_name, sealname,
            source_code, rec_create_date,
            rec_modify_date, msa_org_code,
            birth, name_en, country_cn,
            country_en, sign_dept_en, applivations_cn,
            crew_type, qualification_cn,
            birth_en, cert_print_no,
            certificate_effective_date_en, certificate_expiring_date_en,
            certificate_issued_date_en, crew_type_en,
            applivations_en, auth_authority_cn,
            auth_authority_en, eva_org_cn,
            eva_org_en, train_manager_name_cn,
            train_manager_name_en, representative_cn,
            representative_en, training_names_cn,
            training_names_en, training_issue_dates_cn,
            training_issue_dates_en, training_effective_dates_cn,
            training_effective_dates_en, training_institution_code,
            training_location
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.dataId}, #{item.certificateId}, #{item.templateId},
                #{item.certificateTypeName}, #{item.certificateTypeCode},
                #{item.certificateDefineAuthorityName}, #{item.certificateDefineAuthorityCode},
                #{item.relatedItemName}, #{item.relatedItemCode},
                #{item.certificateHolderCategory}, #{item.certificateHolderCategoryName},
                #{item.validityRange}, #{item.certificateIdentifier},
                #{item.certificateName}, #{item.certificateNumber},
                #{item.certificateIssuingAuthorityName}, #{item.certificateIssuingAuthorityCode},
                #{item.certificateIssuedDate}, #{item.certificateHolderName},
                #{item.certificateHolderCode}, #{item.certificateHolderTypeName},
                #{item.certificateEffectiveDate}, #{item.certificateExpiringDate},
                #{item.issueDeptCode2}, #{item.issueDeptCode3},
                #{item.certificateAreaCode}, #{item.surfaceData},
                #{item.certificateStatus}, #{item.creatorId},
                #{item.createTime}, #{item.operatorId},
                #{item.updateTime}, #{item.filePath},
                #{item.syncStatus}, #{item.remarks},
                #{item.deptId}, #{item.applyNum},
                #{item.affairType}, #{item.serveBusiness},
                #{item.affairId}, #{item.affairNum},
                #{item.qzType}, #{item.draftUrl},
                #{item.sortName}, #{item.sealname},
                #{item.sourceCode}, #{item.recCreateDate},
                #{item.recModifyDate}, #{item.msaOrgCode},
                #{item.birth}, #{item.nameEn}, #{item.countryCn},
                #{item.countryEn}, #{item.signDeptEn}, #{item.applivationsCn},
                #{item.crewType}, #{item.qualificationCn},
                #{item.birthEn}, #{item.certPrintNo},
                #{item.certificateEffectiveDateEn}, #{item.certificateExpiringDateEn},
                #{item.certificateIssuedDateEn}, #{item.crewTypeEn},
                #{item.applivationsEn}, #{item.authAuthorityCn},
                #{item.authAuthorityEn}, #{item.evaOrgCn},
                #{item.evaOrgEn}, #{item.trainManagerNameCn},
                #{item.trainManagerNameEn}, #{item.representativeCn},
                #{item.representativeEn}, #{item.trainingNamesCn},
                #{item.trainingNamesEn}, #{item.trainingIssueDatesCn},
                #{item.trainingIssueDatesEn}, #{item.trainingEffectiveDatesCn},
                #{item.trainingEffectiveDatesEn}, #{item.trainingInstitutionCode},
                #{item.trainingLocation}
            )
        </foreach>
    </insert>

    <!-- 不参加航行和轮机值班海船船员适任证书 -->
    <insert id="insertBcjhljzb" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzb">
        INSERT INTO dwdb_ctf_cert_dtl_bcjhljzb (
            bcjhljzb_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, certificate_issued_date1, certificate_issued_date2, 
            certificate_holder_name, information_of_photo, capacity1, 
            capacity2, capacity3, capacity4, applivations1, 
            applivations2, applivations3, applivations4, name_of_duly_authorized_official1, 
            name_of_duly_authorized_official2, issuing_authority1, issuing_authority2, 
            official_use_only1, official_use_only2, create_time, update_time
        ) VALUES (
            #{bcjhljzbId}, #{dataId}, #{fullNameoftheHolder1}, #{fullNameoftheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{certificateNo}, #{certificateExpiringDate1}, 
            #{certificateExpiringDate2}, #{certificateIssuedDate1}, #{certificateIssuedDate2}, 
            #{certificateHolderName}, #{informationOfPhoto}, #{capacity1}, 
            #{capacity2}, #{capacity3}, #{capacity4}, #{applivations1}, 
            #{applivations2}, #{applivations3}, #{applivations4}, #{nameOfDulyAuthorizedOfficial1}, 
            #{nameOfDulyAuthorizedOfficial2}, #{issuingAuthority1}, #{issuingAuthority2}, 
            #{officialUseOnly1}, #{officialUseOnly2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 船员适任证书申请表 -->
    <insert id="insertCysrzsqb" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqb">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb (
            cysrzsqb_id, data_id, sq_year, sq_month, sq_day, 
            number, name, py, sex, id_code, 
            birth_year, birth_month, birth_day, company, education, 
            school, special, cert_number, cert_date, photo, 
            gmdss_job, gmdss_no, g_year, g_month, g_day, 
            area2, level2, job2, limit_info, hgz_no, 
            year1, month1, day1, signature, seal, 
            link, tel, create_time, update_time,
            cert_no1, s_day, job1, s_year, level1, s_month, area1
        ) VALUES (
            #{cysrzsqbId}, #{dataId}, #{sqYear}, #{sqMonth}, #{sqDay}, 
            #{number}, #{name}, #{py}, #{sex}, #{idCode}, 
            #{birthYear}, #{birthMonth}, #{birthDay}, #{company}, #{education}, 
            #{school}, #{special}, #{certNumber}, #{certDate}, #{photo}, 
            #{gmdssJob}, #{gmdssNo}, #{gYear}, #{gMonth}, #{gDay}, 
            #{area2}, #{level2}, #{job2}, #{limitInfo}, #{hgzNo}, 
            #{year1}, #{month1}, #{day1}, #{signature}, #{seal}, 
            #{link}, #{tel}, #{createTime}, #{updateTime},
            #{certNo1}, #{sDay}, #{job1}, #{sYear}, #{level1}, #{sMonth}, #{area1}
        )
    </insert>

    <!-- 船员适任证书申请表经历 -->
    <insert id="insertCysrzsqbExperience" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbExperience">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb_expe (
            cysrzsqb_experience_id, cysrzsqb_id, jobs, ship_name, type, 
            class, weight, rz_date, lz_date, create_time, update_time
        ) VALUES (
            #{cysrzsqbExperienceId}, #{cysrzsqbId}, #{jobs}, #{shipName}, #{type}, 
            #{class}, #{weight}, #{rzDate}, #{lzDate}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 引航员船员适任证书 -->
    <insert id="insertYhysrz" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrz">
        INSERT INTO dwdb_ctf_cert_dtl_yhysrz (
            yhysrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2, gender1,
            gender2, certificate_no, certificate_expiring_date1, certificate_expiring_date2,
            date_of_issue1, date_of_issue2, certificate_holder_name, information_of_photo,
            remarks,
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            issuing_authority1, issuing_authority2, create_time, update_time
        ) VALUES (
            #{yhysrzId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2},
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, #{gender1},
            #{gender2}, #{certificateNo}, #{certificateExpiringDate1}, #{certificateExpiringDate2},
            #{dateOfIssue1}, #{dateOfIssue2}, #{certificateHolderName}, #{informationOfPhoto},
            #{remarks},
            #{nameOfDulyAuthorizedOfficial1}, #{nameOfDulyAuthorizedOfficial2},
            #{issuingAuthority1}, #{issuingAuthority2}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <!-- 公务船船员适任证书 -->
    <insert id="insertGwccy" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccy">
        INSERT INTO dwdb_ctf_cert_dtl_gwccy (
            gwccy_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, information_of_photo, 
            issuing_authority1, issuing_authority2, official_use_only1, official_use_only2,
            create_time, update_time
        ) VALUES (
            #{gwccyId}, #{dataId}, #{fullNameoftheHolder1}, #{fullNameoftheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{certificateNo}, #{certificateExpiringDate1}, 
            #{certificateExpiringDate2}, #{dateOfIssue1}, #{dateOfIssue2}, 
            #{certificateHolderName}, #{informationOfPhoto}, 
            #{issuingAuthority1}, #{issuingAuthority2}, #{officialUseOnly1}, #{officialUseOnly2},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船高级船员适任证书 -->
    <insert id="insertHcgjcy" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcy">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy (
            hcgjcy_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, article_number1, article_number2, 
            article_number3, article_number4, information_of_photo, 
            name_of_duly_authorized_official1, name_of_duly_authorized_official2, 
            issuing_authority1, issuing_authority2, official_use_only1, 
            official_use_only2, create_time, update_time
        ) VALUES (
            #{hcgjcyId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{certificateNo}, #{certificateExpiringDate1}, 
            #{certificateExpiringDate2}, #{dateOfIssue1}, #{dateOfIssue2}, 
            #{certificateHolderName}, #{articleNumber1}, #{articleNumber2}, 
            #{articleNumber3}, #{articleNumber4}, #{informationOfPhoto}, 
            #{nameOfDulyAuthorizedOfficial1}, #{nameOfDulyAuthorizedOfficial2}, 
            #{issuingAuthority1}, #{issuingAuthority2}, #{officialUseOnly1}, 
            #{officialUseOnly2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船高级船员适任证书能力 -->
    <insert id="insertHcgjcyCapacity" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyCapacity">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy_cap (
            hcgjcy_capacity_id, hcgjcy_id, gradw_and_capacity1, gradw_and_capacity2, 
            alimitations_applying1, alimitations_applying2,  
            create_time, update_time
        ) VALUES (
            #{hcgjcyCapacityId}, #{hcgjcyId}, #{gradwAndCapacity1}, #{gradwAndCapacity2}, 
            #{alimitationsApplying1}, #{alimitationsApplying2}, 
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船高级船员适任证书功能 -->
    <insert id="insertHcgjcyFunction" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyFunction">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy_func (
            hcgjcy_function_id, hcgjcy_id, function1, function2, 
            level1, level2, limitations_applying1, limitations_applying2, 
            create_time, update_time
        ) VALUES (
            #{hcgjcyFunctionId}, #{hcgjcyId}, #{function1}, #{function2}, 
            #{level1}, #{level2}, #{limitationsApplying1}, #{limitationsApplying2}, 
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船普通船员适任证书 -->
    <insert id="insertHcptcysrz" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrz">
        INSERT INTO dwdb_ctf_cert_dtl_hcptcysrz (
            hcptcysrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, capacity1, capacity2, information_of_photo, 
            name_of_duly_authorized_official1, 
            name_of_duly_authorized_official2, issuing_authority1, issuing_authority2, 
            official_use_only1, official_use_only2, create_time, update_time
        ) VALUES (
            #{hcptcysrzId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{certificateNo}, #{certificateExpiringDate1}, 
            #{certificateExpiringDate2}, #{dateOfIssue1}, #{dateOfIssue2}, 
            #{certificateHolderName}, #{capacity1}, #{capacity2}, #{informationOfPhoto}, 
            #{nameOfDulyAuthorizedOfficial1}, 
            #{nameOfDulyAuthorizedOfficial2}, #{issuingAuthority1}, #{issuingAuthority2}, 
            #{officialUseOnly1}, #{officialUseOnly2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船普通船员适任证书能力 -->
    <insert id="insertHcptcysrzCapacity" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzCapacity">
        INSERT INTO dwdb_ctf_cert_dtl_hcptcysrz_cap (
            hcptcysrz_capacity_id, hcptcysrz_id, gradw_and_capacity1, gradw_and_capacity2, 
            alimitations_applying1, alimitations_applying2, create_time, update_time
        ) VALUES (
            #{hcptcysrzCapacityId}, #{hcptcysrzId}, #{gradwAndCapacity1}, #{gradwAndCapacity2}, 
            #{alimitationsApplying1}, #{alimitationsApplying2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船船员培训合格证书 -->
    <insert id="insertHcpxhg" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhg">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxhg (
            hcpxhg_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, issued_on1, 
            issued_on2, certificate_holder_name, information_of_photo, 
            signature_of_duly_aut_offi1, signature_of_duly_aut_offi2, 
            name_of_duly_aut_offi1, name_of_duly_aut_offi2, 
            offical_seal1, offical_seal2, official_use_only1, 
            official_use_only2, create_time, update_time
        ) VALUES (
            #{hcpxhgId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{certificateNo}, #{issuedOn1}, 
            #{issuedOn2}, #{certificateHolderName}, #{informationOfPhoto}, 
            #{signatureOfDulyAutOffi1}, #{signatureOfDulyAutOffi2}, 
            #{nameOfDulyAutOffi1}, #{nameOfDulyAutOffi2}, 
            #{officalSeal1}, #{officalSeal2}, #{officialUseOnly1}, 
            #{officialUseOnly2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船船员培训合格证书培训项目 -->
    <insert id="insertHcpxhgTraining" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhgTraining">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxhg_train (
            hcpxhg_training_id, hcpxhg_id, prefix, title_of_the_certificate1, 
            title_of_the_certificate2, level, date_of_issue1, date_of_issue2, 
            date_of_expiry1, date_of_expiry2, create_time, update_time
        ) VALUES (
            #{hcpxhgTrainingId}, #{hcpxhgId}, #{prefix}, #{titleOfTheCertificate1}, 
            #{titleOfTheCertificate2}, #{level}, #{dateOfIssue1}, #{dateOfIssue2}, 
            #{dateOfExpiry1}, #{dateOfExpiry2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海上非自航船舶船员适任证书 -->
    <insert id="insertHsfhcysrz" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrz">
        INSERT INTO dwdb_ctf_cert_dtl_hsfcysrz (
            hsfhcysrz_id, data_id, certificate_no, full_name_of_the_holder, 
            date_of_birth, place_of_birth, date_of_expirty, date_of_issue, 
            certificate_holder_name, information_of_photo, name_of_duly_authorized_official, 
            remark, create_time, update_time
        ) VALUES (
            #{hsfhcysrzId}, #{dataId}, #{certificateNo}, #{fullNameOfTheHolder}, 
            #{dateOfBirth}, #{placeOfBirth}, #{dateOfExpirty}, #{dateOfIssue}, 
            #{certificateHolderName}, #{informationOfPhoto}, #{nameOfDulyAuthorizedOfficial}, 
            #{remark}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海上非自航船舶船员适任证书船舶信息 -->
    <insert id="insertHsfhcysrzShip" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrzShip">
        INSERT INTO dwdb_ctf_cert_dtl_hsfcysrz_ship (
            hsfhcysrz_ship_id, hsfhcysrz_id, ship_type, "level", capacity,
            create_time, update_time
        ) VALUES (
            #{hsfhcysrzShipId}, #{hsfhcysrzId}, #{shipType}, #{level}, #{capacity},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海员外派机构资质证书 -->
    <insert id="insertHywpjg" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailHywpjg">
        INSERT INTO dwdb_ctf_cert_dtl_hywpjg (
            hywpjg_id, data_id, permit_number1, permit_number2, 
            an_thority_name1, an_thority_name2, an_thority_name3, an_thority_name4, 
            address1, address2, address3, address4, 
            representative1, representative2, representative3, representative4, 
            expiry_date1, expiry_date2, date_of_issue1, date_of_issue3, 
            issuing_authority1, issuing_authority2, issuing_authority3, issuing_authority4, 
            remark1, remark2, annual_examination, create_time, update_time
        ) VALUES (
            #{hywpjgId}, #{dataId}, #{permitNumber1}, #{permitNumber2}, 
            #{anThorityName1}, #{anThorityName2}, #{anThorityName3}, #{anThorityName4}, 
            #{address1}, #{address2}, #{address3}, #{address4}, 
            #{representative1}, #{representative2}, #{representative3}, #{representative4}, 
            #{expiryDate1}, #{expiryDate2}, #{dateOfIssue1}, #{dateOfIssue3}, 
            #{issuingAuthority1}, #{issuingAuthority2}, #{issuingAuthority3}, #{issuingAuthority4}, 
            #{remark1}, #{remark2}, #{annualExamination}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船船员健康证明 -->
    <insert id="insertJkzm" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailJkzm">
        INSERT INTO dwdb_ctf_cert_dtl_jkzm (
            jkzm_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, department1, department2, 
            certificate_no, certificate_expiring_date1, certificate_expiring_date2, 
            date_of_issue1, date_of_issue2, certificate_holder_name, information_of_photo, 
            yes_or_no1, yes_or_no2, yes_or_no3, yes_or_no4, 
            yes_or_no5, yes_or_no6, yes_or_no7, yes_or_no8, 
            yes_or_no9, authorizing_authority1, authorizing_authority2, 
            doctor_name1, doctor_name2, issuing_authority1, issuing_authority2, 
            create_time, update_time
        ) VALUES (
            #{jkzmId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2}, 
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, 
            #{gender1}, #{gender2}, #{department1}, #{department2}, 
            #{certificateNo}, #{certificateExpiringDate1}, #{certificateExpiringDate2}, 
            #{dateOfIssue1}, #{dateOfIssue2}, #{certificateHolderName}, #{informationOfPhoto}, 
            #{yesOrNo1}, #{yesOrNo2}, #{yesOrNo3}, #{yesOrNo4}, 
            #{yesOrNo5}, #{yesOrNo6}, #{yesOrNo7}, #{yesOrNo8}, 
            #{yesOrNo9}, #{authorizingAuthority1}, #{authorizingAuthority2}, 
            #{doctorName1}, #{doctorName2}, #{issuingAuthority1}, #{issuingAuthority2}, 
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 内河船舶船员适任证书 -->
    <insert id="insertNhcbcy" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcbcy">
        INSERT INTO dwdb_ctf_cert_dtl_nhcbsr (
            nhcbsr_id, data_id, name, sex, number, 
            type, end_date, sign_dept, print_no, scope, 
            photo, year, month, day, issue_dept, 
            sign_date, create_time, update_time
        ) VALUES (
            #{nhcbcyId}, #{dataId}, #{name}, #{sex}, #{number}, 
            #{type}, #{endDate}, #{signDept}, #{printNo}, #{scope}, 
            #{photo}, #{year}, #{month}, #{day}, #{issueDept}, 
            #{signDate}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船船员内河航线行驶资格证明 -->
    <insert id="insertNhhxxs" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhhxxs">
        INSERT INTO dwdb_ctf_cert_dtl_nhhxxs (
            nhhxxs_id, data_id, number_of_certificate, name, gender,
            credit_code, date_of_issue, expiry_date, issuing_date,
            applivations, photo, issuing_authority, create_time, update_time
        ) VALUES (
            #{nhhxxsId}, #{dataId}, #{numberOfCertificate}, #{name}, #{gender},
            #{creditCode}, #{dateOfIssue}, #{expiryDate}, #{issuingDate},
            #{applivations}, #{photo}, #{issuingAuthority}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 内河船舶船员培训合格证 -->
    <insert id="insertNhpxhg" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhg">
        INSERT INTO dwdb_ctf_cert_dtl_nhpxhg (
            nhpxhg_id, data_id, name, sex, number,
            print_no, photo, year, month, day,            
            create_time, update_time
        ) VALUES (
            #{nhpxhgId}, #{dataId}, #{name}, #{sex}, #{number},
            #{printNo}, #{photo}, #{year}, #{month}, #{day},            
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 船员培训质量管理体系证书 -->
    <insert id="insertQms" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailQms">
        INSERT INTO dwdb_ctf_cert_dtl_qms (
            qms_id, data_id, number1, full_name_of_the_holder1, full_name_of_the_holder2,
            year1, month1, day1, certificate_expiring_date, name_of_duly_authorized_official1,
            evaluation_organization1, evaluation_organization2, date_of_issue1, date_of_issue2,
            number2, remarks,
            create_time, update_time
        ) VALUES (
            #{qmsId}, #{dataId}, #{number1}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2},
            #{year1}, #{month1}, #{day1}, #{certificateExpiringDate}, #{nameOfDulyAuthorizedOfficial1},
            #{evaluationOrganization1}, #{evaluationOrganization2}, #{dateOfIssue1}, #{dateOfIssue2},
            #{number2}, #{remarks},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 海船船员培训许可证机构信息 -->
    <insert id="insertSeamanInfo" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanInfo">
        INSERT INTO dwdb_ctf_cert_dtl_seaman_info (
            seaman_info_id, data_id, number, name_cn, name_en,
            sex_cn, sex_en, country_cn, country_en, birth_cn,
            birth_en, file_no_cn, file_no_en, qualification_cn, qualification_en,
            initial_date_cn, initial_date_en, expiry_date_cn, expiry_date_en,
            sign_dept_cn, sign_dept_en, office_of_issue_cn, office_of_issue_en,
            date, photo, year, month, day, create_time, update_time,
            create_by, update_by
        ) VALUES (
            #{seamanInfoId}, #{dataId}, #{number}, #{nameCn}, #{nameEn},
            #{sexCn}, #{sexEn}, #{countryCn}, #{countryEn}, #{birthCn},
            #{birthEn}, #{fileNoCn}, #{fileNoEn}, #{qualificationCn}, #{qualificationEn},
            #{initialDateCn}, #{initialDateEn}, #{expiryDateCn}, #{expiryDateEn},
            #{signDeptCn}, #{signDeptEn}, #{officeOfIssueCn}, #{officeOfIssueEn},
            #{date}, #{photo}, #{year}, #{month}, #{day}, #{createTime}, #{updateTime},
            #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 海船船员培训许可证 -->
    <insert id="insertSeamanPermit" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermit">
        INSERT INTO dwdb_ctf_cert_dtl_sea_per (
            seaman_permit_id, data_id, permit_number1, an_thority_name1, training_institution_code1,
            representative1, training_program1, training_program2, registered_address1, training_location1,
            period_of_validity1, period_of_validity2, issuing_authority1, dateof_issue1, permit_number2,
            an_thority_name2, registered_address2, representative2, training_location2, period_of_validity3,
            period_of_validity4, remarks, issuing_authority2, dateof_issue2, create_time, update_time,
            create_by, update_by
        ) VALUES (
            #{seamanPermitId}, #{dataId}, #{permitNumber1}, #{anThorityName1}, #{trainingInstitutionCode1},
            #{representative1}, #{trainingProgram1}, #{trainingProgram2}, #{registeredAddress1}, #{trainingLocation1},
            #{periodOfValidity1}, #{periodOfValidity2}, #{issuingAuthority1}, #{dateofIssue1}, #{permitNumber2},
            #{anThorityName2}, #{registeredAddress2}, #{representative2}, #{trainingLocation2}, #{periodOfValidity3},
            #{periodOfValidity4}, #{remarks}, #{issuingAuthority2}, #{dateofIssue2}, #{createTime}, #{updateTime},
            #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 海船船员培训许可证培训项目 -->
    <insert id="insertSeamanPermitItem" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermitItem">
        INSERT INTO dwdb_ctf_cert_dtl_sea_per_item (
            seaman_permit_item_id, seaman_permit_id, number, atraining_program, 
            training_scale, create_time, update_time, create_by, update_by
        ) VALUES (
            #{seamanPermitItemId}, #{seamanPermitId}, #{number}, #{atrainingProgram},
            #{trainingScale}, #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <!-- 特定航线江海直达船舶船员行驶资格证明培训合格证 -->
    <insert id="insertTdhxjh" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailTdhxjh">
        INSERT INTO dwdb_ctf_cert_dtl_tdhxjh (
            tdhxjh_id, data_id, number_of_certificate, name, date_of_birth,
            credit_code, gender, date_of_issue, expiry_date, applivations,
            limitations_applying, photo, issuing_authority, issuing_date,
            create_time, update_time
        ) VALUES (
            #{tdhxjhId}, #{dataId}, #{numberOfCertificate}, #{name}, #{dateOfBirth},
            #{creditCode}, #{gender}, #{dateOfIssue}, #{expiryDate}, #{applivations},
            #{limitationsApplying}, #{photo}, #{issuingAuthority}, #{issuingDate},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 小型海船适任证书 -->
    <insert id="insertXhcsrz" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrz">
        INSERT INTO dwdb_ctf_cert_dtl_xhcsrz (
            xhcsrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2, gender1, gender2,
            certificate_no, certificate_expiring_date1, certificate_expiring_date2,
            date_of_issue1, date_of_issue2, certificate_holder_name, article_number1,
            article_number2, article_number3, article_number4, information_of_photo,            
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            issuing_authority1, issuing_authority2, official_use_only1,official_use_only2,
            create_time, update_time
        ) VALUES (
            #{xhcsrzId}, #{dataId}, #{fullNameOfTheHolder1}, #{fullNameOfTheHolder2},
            #{nationality1}, #{nationality2}, #{dateOfBirth1}, #{dateOfBirth2}, #{gender1}, #{gender2},
            #{certificateNo}, #{certificateExpiringDate1}, #{certificateExpiringDate2},
            #{dateOfIssue1}, #{dateOfIssue2}, #{certificateHolderName}, #{articleNumber1},
            #{articleNumber2}, #{articleNumber3}, #{articleNumber4}, #{informationOfPhoto},            
            #{nameOfDulyAuthorizedOfficial1}, #{nameOfDulyAuthorizedOfficial2},
            #{issuingAuthority1}, #{issuingAuthority2}, #{officialUseOnly1},#{officialUseOnly2},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 船员适任证书申请表选项 -->
    <insert id="insertCysrzsqbOptions" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbOptions">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb_ops (
            cysrzsqb_options_id, cysrzsqb_id, option_key, option_value, 
            create_time, update_time
        ) VALUES (
            #{cysrzsqbOptionsId}, #{cysrzsqbId}, #{optionKey}, #{optionValue}, 
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入不参加航行和轮机值班海船船员适任证书 -->
    <insert id="batchInsertBcjhljzb" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_bcjhljzb (
            bcjhljzb_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, certificate_issued_date1, certificate_issued_date2, 
            certificate_holder_name, information_of_photo, capacity1, 
            capacity2, capacity3, capacity4, applivations1, 
            applivations2, applivations3, applivations4, name_of_duly_authorized_official1, 
            name_of_duly_authorized_official2, issuing_authority1, issuing_authority2, 
            official_use_only1, official_use_only2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.bcjhljzbId}, #{item.dataId}, #{item.fullNameoftheHolder1}, #{item.fullNameoftheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, 
                #{item.certificateExpiringDate2}, #{item.certificateIssuedDate1}, #{item.certificateIssuedDate2}, 
                #{item.certificateHolderName}, #{item.informationOfPhoto}, #{item.capacity1}, 
                #{item.capacity2}, #{item.capacity3}, #{item.capacity4}, #{item.applivations1}, 
                #{item.applivations2}, #{item.applivations3}, #{item.applivations4}, #{item.nameOfDulyAuthorizedOfficial1}, 
                #{item.nameOfDulyAuthorizedOfficial2}, #{item.issuingAuthority1}, #{item.issuingAuthority2}, 
                #{item.officialUseOnly1}, #{item.officialUseOnly2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入船员适任证书申请表 -->
    <insert id="batchInsertCysrzsqb" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb (
            cysrzsqb_id, data_id, sq_year, sq_month, sq_day, 
            number, name, py, sex, id_code, 
            birth_year, birth_month, birth_day, company, education, 
            school, special, cert_number, cert_date, photo, 
            gmdss_job, gmdss_no, g_year, g_month, g_day, 
            area2, level2, job2, limit_info, hgz_no, 
            year1, month1, day1, signature, seal, 
            link, tel, create_time, update_time,
            cert_no1, s_day, job1, s_year, level1, s_month, area1
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.cysrzsqbId}, #{item.dataId}, #{item.sqYear}, #{item.sqMonth}, #{item.sqDay}, 
                #{item.number}, #{item.name}, #{item.py}, #{item.sex}, #{item.idCode}, 
                #{item.birthYear}, #{item.birthMonth}, #{item.birthDay}, #{item.company}, #{item.education}, 
                #{item.school}, #{item.special}, #{item.certNumber}, #{item.certDate}, #{item.photo}, 
                #{item.gmdssJob}, #{item.gmdssNo}, #{item.gYear}, #{item.gMonth}, #{item.gDay}, 
                #{item.area2}, #{item.level2}, #{item.job2}, #{item.limitInfo}, #{item.hgzNo}, 
                #{item.year1}, #{item.month1}, #{item.day1}, #{item.signature}, #{item.seal}, 
                #{item.link}, #{item.tel}, #{item.createTime}, #{item.updateTime},
                #{item.certNo1}, #{item.sDay}, #{item.job1}, #{item.sYear}, #{item.level1}, #{item.sMonth}, #{item.area1}
            )
        </foreach>
    </insert>

    <!-- 其他批量插入方法，按照同样的模式添加 -->
    <!-- 批量插入船员适任证书申请表经历 -->
    <insert id="batchInsertCysrzsqbExperience" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb_expe (
            cysrzsqb_experience_id, cysrzsqb_id, jobs, ship_name, type, 
            class, weight, rz_date, lz_date, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.cysrzsqbExperienceId}, #{item.cysrzsqbId}, #{item.jobs}, #{item.shipName}, #{item.type}, 
                #{item.class_}, #{item.weight}, #{item.rzDate}, #{item.lzDate}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入船员适任证书申请表选项 -->
    <insert id="batchInsertCysrzsqbOptions" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_cysrzsqb_ops (
            cysrzsqb_options_id, cysrzsqb_id, option_key, option_value, 
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.cysrzsqbOptionsId}, #{item.cysrzsqbId}, #{item.optionKey}, #{item.optionValue}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入其他所有表的方法，按照单条插入的SQL语句模式，添加foreach循环 -->
    <!-- 这里只展示了几个示例，其他表的批量插入方法也需要按照同样的模式添加 -->

    <!-- 批量插入引航员船员适任证书 -->
    <insert id="batchInsertYhysrz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_yhysrz (
            yhysrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2, gender1,
            gender2, certificate_no, certificate_expiring_date1, certificate_expiring_date2,
            date_of_issue1, date_of_issue2, certificate_holder_name, information_of_photo,
            remarks,
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            issuing_authority1, issuing_authority2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.yhysrzId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, #{item.gender1},
                #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, #{item.certificateExpiringDate2},
                #{item.dateOfIssue1}, #{item.dateOfIssue2}, #{item.certificateHolderName}, #{item.informationOfPhoto},
                #{item.remarks},
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2},
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入公务船船员适任证书 -->
    <insert id="batchInsertGwccy" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_gwccy (
            gwccy_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, information_of_photo, 
            issuing_authority1, issuing_authority2, official_use_only1, official_use_only2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.gwccyId}, #{item.dataId}, #{item.fullNameoftheHolder1}, #{item.fullNameoftheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, 
                #{item.certificateExpiringDate2}, #{item.dateOfIssue1}, #{item.dateOfIssue2}, 
                #{item.certificateHolderName}, #{item.informationOfPhoto}, 
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.officialUseOnly1}, #{item.officialUseOnly2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船高级船员适任证书 -->
    <insert id="batchInsertHcgjcy" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy (
            hcgjcy_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, article_number1, article_number2, 
            article_number3, article_number4, information_of_photo, 
            name_of_duly_authorized_official1, name_of_duly_authorized_official2, 
            issuing_authority1, issuing_authority2, official_use_only1, 
            official_use_only2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcgjcyId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, 
                #{item.certificateExpiringDate2}, #{item.dateOfIssue1}, #{item.dateOfIssue2}, 
                #{item.certificateHolderName}, #{item.articleNumber1}, #{item.articleNumber2}, 
                #{item.articleNumber3}, #{item.articleNumber4}, #{item.informationOfPhoto}, 
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2}, 
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.officialUseOnly1}, 
                #{item.officialUseOnly2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船高级船员适任证书能力 -->
    <insert id="batchInsertHcgjcyCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy_cap (
            hcgjcy_capacity_id, hcgjcy_id, gradw_and_capacity1, gradw_and_capacity2, 
            alimitations_applying1, alimitations_applying2, 
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcgjcyCapacityId}, #{item.hcgjcyId}, #{item.gradwAndCapacity1}, #{item.gradwAndCapacity2}, 
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船高级船员适任证书功能 -->
    <insert id="batchInsertHcgjcyFunction" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcgjcy_func (
            hcgjcy_function_id, hcgjcy_id, function1, function2, 
            level1, level2, limitations_applying1, limitations_applying2, 
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcgjcyFunctionId}, #{item.hcgjcyId}, #{item.function1}, #{item.function2}, 
                #{item.level1}, #{item.level2}, #{item.limitationsApplying1}, #{item.limitationsApplying2}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

        <!-- 批量插入海船普通船员适任证书 -->
    <insert id="batchInsertHcptcysrz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcptcysrz (
            hcptcysrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, certificate_expiring_date1, 
            certificate_expiring_date2, date_of_issue1, date_of_issue2, 
            certificate_holder_name, capacity1, capacity2, information_of_photo, 
            name_of_duly_authorized_official1, 
            name_of_duly_authorized_official2, issuing_authority1, issuing_authority2, 
            official_use_only1, official_use_only2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcptcysrzId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, 
                #{item.certificateExpiringDate2}, #{item.dateOfIssue1}, #{item.dateOfIssue2}, 
                #{item.certificateHolderName}, #{item.capacity1}, #{item.capacity2}, #{item.informationOfPhoto}, 
                #{item.nameOfDulyAuthorizedOfficial1}, 
                #{item.nameOfDulyAuthorizedOfficial2}, #{item.issuingAuthority1}, #{item.issuingAuthority2}, 
                #{item.officialUseOnly1}, #{item.officialUseOnly2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船普通船员适任证书能力 -->
    <insert id="batchInsertHcptcysrzCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcptcysrz_cap (
            hcptcysrz_capacity_id, hcptcysrz_id, gradw_and_capacity1, gradw_and_capacity2, 
            alimitations_applying1, alimitations_applying2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcptcysrzCapacityId}, #{item.hcptcysrzId}, #{item.gradwAndCapacity1}, #{item.gradwAndCapacity2}, 
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船普通船员适任证书功能 -->
    <insert id="batchInsertHcptcysrzFunction" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcptcysrz_fun (
            hcptcysrz_function_id, hcptcysrz_id, function1, function2, 
            level1, level2, limitations_applying1, limitations_applying2, 
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcptcysrzFunctionId}, #{item.hcptcysrzId}, #{item.function1}, #{item.function2}, 
                #{item.level1}, #{item.level2}, #{item.limitationsApplying1}, #{item.limitationsApplying2}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员培训合格证书 -->
    <insert id="batchInsertHcpxhg" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxhg (
            hcpxhg_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, certificate_no, issued_on1, 
            issued_on2, certificate_holder_name, information_of_photo, 
            signature_of_duly_aut_offi1, signature_of_duly_aut_offi2, 
            name_of_duly_aut_offi1, name_of_duly_aut_offi2, 
            offical_seal1, offical_seal2, official_use_only1, 
            official_use_only2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcpxhgId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.issuedOn1}, 
                #{item.issuedOn2}, #{item.certificateHolderName}, #{item.informationOfPhoto}, 
                #{item.signatureOfDulyAutOffi1}, #{item.signatureOfDulyAutOffi2}, 
                #{item.nameOfDulyAutOffi1}, #{item.nameOfDulyAutOffi2}, 
                #{item.officalSeal1}, #{item.officalSeal2}, #{item.officialUseOnly1}, 
                #{item.officialUseOnly2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员培训合格证书培训项目 -->
    <insert id="batchInsertHcpxhgTraining" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxhg_train (
            hcpxhg_training_id, hcpxhg_id, data_id, title_of_the_certificate1, title_of_the_certificate2,
            "level", date_of_issue1, date_of_issue2, date_of_expiry1, date_of_expiry2,
            create_time, update_time, prefix
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcpxhgTrainingId}, #{item.hcpxhgId}, #{item.dataId}, #{item.titleOfTheCertificate1}, #{item.titleOfTheCertificate2},
                #{item.level}, #{item.dateOfIssue1}, #{item.dateOfIssue2}, #{item.dateOfExpiry1}, #{item.dateOfExpiry2},
                #{item.createTime}, #{item.updateTime}, #{item.prefix}
            )
        </foreach>
    </insert>

    <!-- 批量插入海上非自航船舶船员适任证书 -->
    <insert id="batchInsertHsfhcysrz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hsfcysrz (
            hsfhcysrz_id, data_id, certificate_no, full_name_of_the_holder, 
            date_of_birth, place_of_birth, date_of_expirty, date_of_issue, 
            certificate_holder_name, information_of_photo, name_of_duly_authorized_official, 
            remark, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hsfhcysrzId}, #{item.dataId}, #{item.certificateNo}, #{item.fullNameOfTheHolder}, 
                #{item.dateOfBirth}, #{item.placeOfBirth}, #{item.dateOfExpirty}, #{item.dateOfIssue}, 
                #{item.certificateHolderName}, #{item.informationOfPhoto}, #{item.nameOfDulyAuthorizedOfficial}, 
                #{item.remark}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海上非自航船舶船员适任证书船舶信息 -->
    <insert id="batchInsertHsfhcysrzShip" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hsfcysrz_ship (
            hsfhcysrz_ship_id, hsfhcysrz_id, ship_type, "level", capacity,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hsfhcysrzShipId}, #{item.hsfhcysrzId}, #{item.shipType}, #{item.level}, #{item.capacity},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海员外派机构资质证书 -->
    <insert id="batchInsertHywpjg" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hywpjg (
            hywpjg_id, data_id, permit_number1, permit_number2, 
            an_thority_name1, an_thority_name2, an_thority_name3, an_thority_name4, 
            address1, address2, address3, address4, 
            representative1, representative2, representative3, representative4, 
            expiry_date1, expiry_date2, date_of_issue1, date_of_issue3, 
            issuing_authority1, issuing_authority2, issuing_authority3, issuing_authority4, 
            remark1, remark2, annual_examination, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hywpjgId}, #{item.dataId}, #{item.permitNumber1}, #{item.permitNumber2}, 
                #{item.anThorityName1}, #{item.anThorityName2}, #{item.anThorityName3}, #{item.anThorityName4}, 
                #{item.address1}, #{item.address2}, #{item.address3}, #{item.address4}, 
                #{item.representative1}, #{item.representative2}, #{item.representative3}, #{item.representative4}, 
                #{item.expiryDate1}, #{item.expiryDate2}, #{item.dateOfIssue1}, #{item.dateOfIssue3}, 
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.issuingAuthority3}, #{item.issuingAuthority4}, 
                #{item.remark1}, #{item.remark2}, #{item.annualExamination}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员健康证明 -->
    <insert id="batchInsertJkzm" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_jkzm (
            jkzm_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2, 
            nationality1, nationality2, date_of_birth1, date_of_birth2, 
            gender1, gender2, department1, department2, 
            certificate_no, certificate_expiring_date1, certificate_expiring_date2, 
            date_of_issue1, date_of_issue2, certificate_holder_name, information_of_photo, 
            yes_or_no1, yes_or_no2, yes_or_no3, yes_or_no4, 
            yes_or_no5, yes_or_no6, yes_or_no7, yes_or_no8, 
            yes_or_no9, authorizing_authority1, authorizing_authority2, 
            doctor_name1, doctor_name2, issuing_authority1, issuing_authority2, 
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.jkzmId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2}, 
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, 
                #{item.gender1}, #{item.gender2}, #{item.department1}, #{item.department2}, 
                #{item.certificateNo}, #{item.certificateExpiringDate1}, #{item.certificateExpiringDate2}, 
                #{item.dateOfIssue1}, #{item.dateOfIssue2}, #{item.certificateHolderName}, #{item.informationOfPhoto}, 
                #{item.yesOrNo1}, #{item.yesOrNo2}, #{item.yesOrNo3}, #{item.yesOrNo4}, 
                #{item.yesOrNo5}, #{item.yesOrNo6}, #{item.yesOrNo7}, #{item.yesOrNo8}, 
                #{item.yesOrNo9}, #{item.authorizingAuthority1}, #{item.authorizingAuthority2}, 
                #{item.doctorName1}, #{item.doctorName2}, #{item.issuingAuthority1}, #{item.issuingAuthority2}, 
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入内河船舶船员适任证书 -->
    <insert id="batchInsertNhcbcy" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_nhcbsr (
            nhcbsr_id, data_id, name, sex, number, 
            type, end_date, sign_dept, print_no, scope, 
            photo, year, month, day, issue_dept, 
            sign_date, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhcbcyId}, #{item.dataId}, #{item.name}, #{item.sex}, #{item.number}, 
                #{item.type}, #{item.endDate}, #{item.signDept}, #{item.printNo}, #{item.scope}, 
                #{item.photo}, #{item.year}, #{item.month}, #{item.day}, #{item.issueDept}, 
                #{item.signDate}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员内河航线行驶资格证明 -->
    <insert id="batchInsertNhhxxs" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_nhhxxs (
            nhhxxs_id, data_id, number_of_certificate, name, gender,
            credit_code, date_of_issue, expiry_date, issuing_date,
            applivations, photo, issuing_authority, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhhxxsId}, #{item.dataId}, #{item.numberOfCertificate}, #{item.name}, #{item.gender},
                #{item.creditCode}, #{item.dateOfIssue}, #{item.expiryDate}, #{item.issuingDate},
                #{item.applivations}, #{item.photo}, #{item.issuingAuthority}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入内河船舶船员培训合格证 -->
    <insert id="batchInsertNhpxhg" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_nhpxhg (
            nhpxhg_id, data_id, name, sex, number,
            print_no, photo, year, month, day,            
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhpxhgId}, #{item.dataId}, #{item.name}, #{item.sex}, #{item.number},
                #{item.printNo}, #{item.photo}, #{item.year}, #{item.month}, #{item.day},                
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入船员培训质量管理体系证书 -->
    <insert id="batchInsertQms" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_qms (
            qms_id, data_id, number1, full_name_of_the_holder1, full_name_of_the_holder2,
            year1, month1, day1, certificate_expiring_date, name_of_duly_authorized_official1,
            evaluation_organization1, evaluation_organization2, date_of_issue1, date_of_issue2,
            number2, remarks, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.qmsId}, #{item.dataId}, #{item.number1}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.year1}, #{item.month1}, #{item.day1}, #{item.certificateExpiringDate}, #{item.nameOfDulyAuthorizedOfficial1},
                #{item.evaluationOrganization1}, #{item.evaluationOrganization2}, #{item.dateOfIssue1}, #{item.dateOfIssue2},
                #{item.number2}, #{item.remarks}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员培训许可证机构信息 -->
    <insert id="batchInsertSeamanInfo" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_seaman_info (
            seaman_info_id, data_id, number, name_cn, name_en,
            sex_cn, sex_en, country_cn, country_en, birth_cn,
            birth_en, file_no_cn, file_no_en, qualification_cn, qualification_en,
            initial_date_cn, initial_date_en, expiry_date_cn, expiry_date_en,
            sign_dept_cn, sign_dept_en, office_of_issue_cn, office_of_issue_en,
            date, photo, year, month, day, create_time, update_time            
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.seamanInfoId}, #{item.dataId}, #{item.number}, #{item.nameCn}, #{item.nameEn},
                #{item.sexCn}, #{item.sexEn}, #{item.countryCn}, #{item.countryEn}, #{item.birthCn},
                #{item.birthEn}, #{item.fileNoCn}, #{item.fileNoEn}, #{item.qualificationCn}, #{item.qualificationEn},
                #{item.initialDateCn}, #{item.initialDateEn}, #{item.expiryDateCn}, #{item.expiryDateEn},
                #{item.signDeptCn}, #{item.signDeptEn}, #{item.officeOfIssueCn}, #{item.officeOfIssueEn},
                #{item.date}, #{item.photo}, #{item.year}, #{item.month}, #{item.day}, #{item.createTime}, #{item.updateTime}                
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员培训许可证 -->
    <insert id="batchInsertSeamanPermit" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_sea_per (
            seaman_permit_id, data_id, permit_number1, an_thority_name1, training_institution_code1,
            representative1, training_program1, training_program2, registered_address1, training_location1,
            period_of_validity1, period_of_validity2, issuing_authority1, dateof_issue1, permit_number2,
            an_thority_name2, registered_address2, representative2, training_location2, period_of_validity3,
            period_of_validity4, remarks, issuing_authority2, dateof_issue2, create_time, update_time,
            create_by, update_by
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.seamanPermitId}, #{item.dataId}, #{item.permitNumber1}, #{item.anThorityName1}, #{item.trainingInstitutionCode1},
                #{item.representative1}, #{item.trainingProgram1}, #{item.trainingProgram2}, #{item.registeredAddress1}, #{item.trainingLocation1},
                #{item.periodOfValidity1}, #{item.periodOfValidity2}, #{item.issuingAuthority1}, #{item.dateofIssue1}, #{item.permitNumber2},
                #{item.anThorityName2}, #{item.registeredAddress2}, #{item.representative2}, #{item.trainingLocation2}, #{item.periodOfValidity3},
                #{item.periodOfValidity4}, #{item.remarks}, #{item.issuingAuthority2}, #{item.dateofIssue2}, #{item.createTime}, #{item.updateTime},
                #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员培训许可证培训项目 -->
    <insert id="batchInsertSeamanPermitItem" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_sea_per_item (
            seaman_permit_item_id, seaman_permit_id, number, atraining_program, 
            training_scale, create_time, update_time, create_by, update_by
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.seamanPermitItemId}, #{item.seamanPermitId}, #{item.number}, #{item.atrainingProgram},
                #{item.trainingScale}, #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 批量插入特定航线江海直达船舶船员行驶资格证明培训合格证 -->
    <insert id="batchInsertTdhxjh" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_tdhxjh (
            tdhxjh_id, data_id, number_of_certificate, name, date_of_birth,
            credit_code, gender, date_of_issue, expiry_date, applivations,
            limitations_applying, photo, issuing_authority, issuing_date,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.tdhxjhId}, #{item.dataId}, #{item.numberOfCertificate}, #{item.name}, #{item.dateOfBirth},
                #{item.creditCode}, #{item.gender}, #{item.dateOfIssue}, #{item.expiryDate}, #{item.applivations},
                #{item.limitationsApplying}, #{item.photo}, #{item.issuingAuthority}, #{item.issuingDate},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入小型海船适任证书 -->
    <insert id="batchInsertXhcsrz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_xhcsrz (
            xhcsrz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2, gender1, gender2,
            certificate_no, certificate_expiring_date1, certificate_expiring_date2,
            date_of_issue1, date_of_issue2, certificate_holder_name, article_number1,
            article_number2, article_number3, article_number4, information_of_photo,                        
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            issuing_authority1, issuing_authority2, official_use_only1,official_use_only2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.xhcsrzId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2}, #{item.gender1}, #{item.gender2},
                #{item.certificateNo}, #{item.certificateExpiringDate1}, #{item.certificateExpiringDate2},
                #{item.dateOfIssue1}, #{item.dateOfIssue2}, #{item.certificateHolderName}, #{item.articleNumber1},
                #{item.articleNumber2}, #{item.articleNumber3}, #{item.articleNumber4}, #{item.informationOfPhoto},                                
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2},
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.officialUseOnly1},#{item.officialUseOnly2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <insert id="batchInsertNhpxhgItem" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_nhpxhg_item (
            nhpxhg_item_id, nhpxhg_id, project_name, sign_dept, sign_date, end_date,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhpxhgItemId}, #{item.nhpxhgId}, #{item.project}, 
                #{item.signDept}, #{item.signDate}, #{item.endDate},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 查询需要重处理的数据 -->
    <select id="selectRedoData" resultType="com.example.certificate.entity.aggregate.OdsCertificateData">
        SELECT 
            dataid, certificateid, catalogid, catalogname, templateid,
            certificatetype, certificatetypecode, issuedept, issuedeptcode,
            certificateareacode, certificateholder, certificateholdercode,
            certificateholdertype, certificatenumber, issuedate, validbegindate,
            validenddate, surfacedata, status, creator, createtime, operator,
            updatetime, filepath, syncstatus, remarks, deptid, applynum,
            affairname, affairtype, servebusiness, affairid, affairnum,
            qztype, zztype, drafturl, isview, sortname, col1, verifydate,
            verification, creditcode, sealname, fcdc_date
        FROM dwdb_certificate_data_redo
        WHERE redo_status = '0'
        ORDER BY fcdc_date
        LIMIT 100
    </select>

    <!-- 更新重处理状态 -->
    <update id="updateRedoStatus">
        UPDATE dwdb_certificate_data_redo
        SET redo_status = #{status},
            fail_reason = #{failReason}
        WHERE dataid = #{dataId}
    </update>

    <!-- 插入错误记录 -->
    <insert id="insertErrorRecord" parameterType="com.example.certificate.entity.standard.DwdbCertificateDataError">
        INSERT INTO dwdb_certificate_data_error (
            error_id, data_id, table_type, error_time, error_message, data_content
        ) VALUES (
            #{errorId}, #{dataId}, #{tableType}, #{errorTime}, #{errorMessage}, #{dataContent}
        )
    </insert>

    <!-- 插入重处理数据 -->
    <insert id="insertRedoData">
        INSERT INTO dwdb_certificate_data_redo (
            dataid, certificateid, catalogid, catalogname, templateid,
            certificatetype, certificatetypecode, issuedept, issuedeptcode,
            certificateareacode, certificateholder, certificateholdercode,
            certificateholdertype, certificatenumber, issuedate, validbegindate,
            validenddate, surfacedata, status, creator, createtime, operator,
            updatetime, filepath, syncstatus, remarks, deptid, applynum,
            affairname, affairtype, servebusiness, affairid, affairnum,
            qztype, zztype, drafturl, isview, sortname, col1, verifydate,
            verification, creditcode, sealname, fcdc_date, redo_status, fail_reason
        ) VALUES (
            #{dataid}, #{certificateid}, #{catalogid}, #{catalogname}, #{templateid},
            #{certificatetype}, #{certificatetypecode}, #{issuedept}, #{issuedeptcode},
            #{certificateareacode}, #{certificateholder}, #{certificateholdercode},
            #{certificateholdertype}, #{certificatenumber}, #{issuedate}, #{validbegindate},
            #{validenddate}, #{surfacedata}, #{status}, #{creator}, #{createtime}, #{operator},
            #{updatetime}, #{filepath}, #{syncstatus}, #{remarks}, #{deptid}, #{applynum},
            #{affairname}, #{affairtype}, #{servebusiness}, #{affairid}, #{affairnum},
            #{qztype}, #{zztype}, #{drafturl}, #{isview}, #{sortname}, #{col1}, #{verifydate},
            #{verification}, #{creditcode}, #{sealname}, #{fcdc_date}, #{redo_status}, #{fail_reason}
        )
    </insert>

    <select id="existsInRedoTable" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM dwdb_certificate_data_redo
        WHERE dataid = #{dataId}
    </select>

    <!-- 主表删除 -->
    <delete id="deleteByDataId">
        DELETE FROM dwdb_certificate_data WHERE data_id = #{dataId}
    </delete>

    <!-- 明细表删除 -->
    <delete id="deleteBcjhljzbByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_bcjhljzb WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteGwccyByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_gwccy WHERE data_id = #{dataId}
    </delete>

    <!-- 明细表删除 -->
    <delete id="deleteHcptcysrzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcptcysrz WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteHcptcysrzCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcptcysrz_cap 
        WHERE hcptcysrz_id IN (
            SELECT hcptcysrz_id 
            FROM dwdb_ctf_cert_dtl_hcptcysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHcpxhgByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcpxhg WHERE data_id = #{dataId}
    </delete>

    <!-- 海船船员培训合格证书相关删除 -->
    <delete id="deleteHcpxhgTrainingByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcpxhg_train 
        WHERE hcpxhg_id IN (
            SELECT hcpxhg_id 
            FROM dwdb_ctf_cert_dtl_hcpxhg 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHsfhcysrzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hsfcysrz WHERE data_id = #{dataId}
    </delete>

    <!-- 海上非自航船舶船员适任证书相关删除 -->
    <delete id="deleteHsfhcysrzShipByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hsfcysrz_ship 
        WHERE hsfhcysrz_id IN (
            SELECT hsfhcysrz_id 
            FROM dwdb_ctf_cert_dtl_hsfcysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHywpjgByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hywpjg WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteJkzmByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_jkzm WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteNhcbcyByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_nhcbsr WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteNhhxxsByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_nhhxxs WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteNhpxhgByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_nhpxhg WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteNhpxhgItemByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_nhpxhg_item 
        WHERE nhpxhg_id IN (
            SELECT nhpxhg_id 
            FROM dwdb_ctf_cert_dtl_nhpxhg 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteQmsByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_qms WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteSeamanInfoByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_seaman_info WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteSeamanPermitByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_sea_per WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteSeamanPermitItemByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_sea_per_item 
        WHERE seaman_permit_id IN (
            SELECT seaman_permit_id 
            FROM dwdb_ctf_cert_dtl_sea_per 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteTdhxjhByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_tdhxjh WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteXhcsrzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_xhcsrz WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteYhysrzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_yhysrz WHERE data_id = #{dataId}
    </delete>

    <!-- 船员适任证书申请表相关删除 -->
    <delete id="deleteCysrzsqbByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_cysrzsqb WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteCysrzsqbExperienceByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_cysrzsqb_expe 
        WHERE cysrzsqb_id IN (
            SELECT cysrzsqb_id 
            FROM dwdb_ctf_cert_dtl_cysrzsqb 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteCysrzsqbOptionsByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_cysrzsqb_ops 
        WHERE cysrzsqb_id IN (
            SELECT cysrzsqb_id 
            FROM dwdb_ctf_cert_dtl_cysrzsqb 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 海船高级船员适任证书相关删除 -->
    <delete id="deleteHcgjcyByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcgjcy WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteHcgjcyCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcgjcy_cap 
        WHERE hcgjcy_id IN (
            SELECT hcgjcy_id 
            FROM dwdb_ctf_cert_dtl_hcgjcy 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHcgjcyFunctionByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcgjcy_func 
        WHERE hcgjcy_id IN (
            SELECT hcgjcy_id 
            FROM dwdb_ctf_cert_dtl_hcgjcy 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHcptcysrzFunctionByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcptcysrz_fun 
        WHERE hcptcysrz_id IN (
            SELECT hcptcysrz_id 
            FROM dwdb_ctf_cert_dtl_hcptcysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <insert id="batchInsertAttributes" parameterType="java.util.List">
        INSERT INTO dwdb_certificate_data_attribute (
            certificate_attribute_id, data_id, attribute_column_name,
            attribute_value, rec_create_date, rec_modify_date
        ) VALUES 
        <foreach collection="attributes" item="item" separator=",">
            (
                #{item.certificateAttributeId}, #{item.dataId}, #{item.attributeColumnName},
                #{item.attributeValue}, #{item.recCreateDate}, #{item.recModifyDate}
            )
        </foreach>
    </insert>

    <!-- 批量插入小型海船适任证书职务等级 -->
    <insert id="batchInsertXhcsrzCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_xhcsrz_cap (
            xhcsrz_capacity_id, xhcsrz_id, gradw_and_capacity1, gradw_and_capacity2,
            alimitations_applying1, alimitations_applying2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.xhcsrzCapacityId}, #{item.xhcsrzId}, #{item.gradwAndCapacity1}, #{item.gradwAndCapacity2},
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入小型海船适任证书职能 -->
    <insert id="batchInsertXhcsrzFunction" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_xhcsrz_func (
            xhcsrz_function_id, xhcsrz_id, function1, function2, level1, level2,
            limitations_applying1, limitations_applying2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.xhcsrzFunctionId}, #{item.xhcsrzId}, #{item.function1}, #{item.function2},
                #{item.level1}, #{item.level2}, #{item.limitationsApplying1}, #{item.limitationsApplying2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除小型海船适任证书职务等级 -->
    <delete id="deleteXhcsrzCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_xhcsrz_cap 
        WHERE xhcsrz_id IN (
            SELECT xhcsrz_id 
            FROM dwdb_ctf_cert_dtl_xhcsrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 删除小型海船适任证书职能 -->
    <delete id="deleteXhcsrzFunctionByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_xhcsrz_func 
        WHERE xhcsrz_id IN (
            SELECT xhcsrz_id 
            FROM dwdb_ctf_cert_dtl_xhcsrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <insert id="batchInsertYhysrzRange" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_yhysrz_range (
            yhysrz_range_id, yhysrz_id, type1, type2, level1, level2,
            pilotage_area1, pilotage_area2, limitation_of_polotage1, limitation_of_polotage2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.yhysrzRangeId}, #{item.yhysrzId}, #{item.type1}, #{item.type2},
                #{item.level1}, #{item.level2}, #{item.pilotageArea1}, #{item.pilotageArea2},
                #{item.limitationOfPolotage1}, #{item.limitationOfPolotage2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteYhysrzRangeByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_yhysrz_range 
        WHERE yhysrz_id IN (
            SELECT yhysrz_id 
            FROM dwdb_ctf_cert_dtl_yhysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteYhysrzRangeByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_yhysrz_range 
        WHERE yhysrz_id IN (
            SELECT yhysrz_id 
            FROM dwdb_ctf_cert_dtl_yhysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteYhysrzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_yhysrz 
        WHERE yhysrz_id IN (
            SELECT yhysrz_id 
            FROM dwdb_ctf_cert_dtl_yhysrz 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 删除游艇驾驶证书 -->
    <delete id="deleteYtjszByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_ytjsz WHERE data_id = #{dataId}
    </delete>

    <!-- 批量插入游艇驾驶证书 -->
    <insert id="batchInsertYtjsz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_ytjsz (
            ytjsz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_expiry1, date_of_expiry2,
            issued_on1, issued_on2, information_of_photo, file_no_cn, file_no_en,
            qualification_cn, qualification_en, initial_date_cn, initial_date_en,
            sign_dept_cn, sign_dept_en, office_of_issue_cn, office_of_issue_en,
            date, year, month, day, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ytjszId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfExpiry1}, #{item.dateOfExpiry2},
                #{item.issuedOn1}, #{item.issuedOn2}, #{item.informationOfPhoto}, #{item.fileNoCn}, #{item.fileNoEn},
                #{item.qualificationCn}, #{item.qualificationEn}, #{item.initialDateCn}, #{item.initialDateEn},
                #{item.signDeptCn}, #{item.signDeptEn}, #{item.officeOfIssueCn}, #{item.officeOfIssueEn},
                #{item.date}, #{item.year}, #{item.month}, #{item.day}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除船上厨师培训合格证 -->
    <delete id="deleteCscspxByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_cscspx WHERE data_id = #{dataId}
    </delete>

    <!-- 批量插入船上厨师培训合格证 -->
    <insert id="batchInsertCscspx" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_cscspx (
            cscspx_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_issue1, date_of_issue2,
            name_of_the_traing_manager1, name_of_the_traing_manager2,
            issuing_body1, issuing_body2, information_of_photo,
            create_time, update_time, issuing_body,certificate_holder_name
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.cscspxId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfIssue1}, #{item.dateOfIssue2},
                #{item.nameOfTheTraingManager1}, #{item.nameOfTheTraingManager2},
                #{item.issuingBody1}, #{item.issuingBody2}, #{item.informationOfPhoto},
                #{item.createTime}, #{item.updateTime},#{item.issuingBody},#{item.certificateHolderName}
            )
        </foreach>
    </insert>

    <!-- 删除船上膳食服务辅助人员培训证明 -->
    <delete id="deleteCsssfzpxByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_csssfzpx WHERE data_id = #{dataId}
    </delete>

    <!-- 批量插入船上膳食服务辅助人员培训证明 -->
    <insert id="batchInsertCsssfzpx" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_csssfzpx (
            csssfzpx_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_issue1, date_of_issue2,
            name_of_the_traing_manager1, name_of_the_traing_manager2,
            issuing_body1, issuing_body2, information_of_photo,
            create_time, update_time,issuing_body,certificate_holder_name
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.csssfzpxId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfIssue1}, #{item.dateOfIssue2},
                #{item.nameOfTheTraingManager1}, #{item.nameOfTheTraingManager2},
                #{item.issuingBody1}, #{item.issuingBody2}, #{item.informationOfPhoto},
                #{item.createTime}, #{item.updateTime},#{item.issuingBody},#{item.certificateHolderName}
            )
        </foreach>
    </insert>

    <!-- 删除海船船员适任证书承认签证 -->
    <delete id="deleteHccycrByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccycr WHERE data_id = #{dataId}
    </delete>

    <!-- 删除海船船员适任证书承认签证职务等级 -->
    <delete id="deleteHccycrCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccycr_cap 
        WHERE hccycr_id IN (
            SELECT hccycr_id 
            FROM dwdb_ctf_cert_dtl_hccycr 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 删除海船船员适任证书承认签证职能 -->
    <delete id="deleteHccycrFunctionByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccycr_func 
        WHERE hccycr_id IN (
            SELECT hccycr_id 
            FROM dwdb_ctf_cert_dtl_hccycr 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 批量插入海船船员适任证书承认签证 -->
    <insert id="batchInsertHccycr" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccycr (
            hccycr_id, data_id, holder_name1, holder_name2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_expiry1, date_of_expiry2,
            issued_on1, issued_on2, certificate_holder_name,
            article_number1, article_number2, article_number3, article_number4,
            information_of_photo, issuing_aminstration1, issuing_aminstration2,
            official_use_only1, official_use_only2,
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccycrId}, #{item.dataId}, #{item.holderName1}, #{item.holderName2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfExpiry1}, #{item.dateOfExpiry2},
                #{item.issuedOn1}, #{item.issuedOn2}, #{item.certificateHolderName},
                #{item.articleNumber1}, #{item.articleNumber2}, #{item.articleNumber3}, #{item.articleNumber4},
                #{item.informationOfPhoto}, #{item.issuingAminstration1}, #{item.issuingAminstration2},
                #{item.officialUseOnly1}, #{item.officialUseOnly2},
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员适任证书承认签证职务等级 -->
    <insert id="batchInsertHccycrCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccycr_cap (
            hccycr_capacity_id, hccycr_id, capacity1, capacity2,
            alimitations_applying1, alimitations_applying2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccycrCapacityId}, #{item.hccycrId}, #{item.capacity1}, #{item.capacity2},
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船船员适任证书承认签证职能 -->
    <insert id="batchInsertHccycrFunction" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccycr_func (
            hccycr_function_id, hccycr_id, function1, function2,
            level1, level2, limitations_applying1, limitations_applying2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccycrFunctionId}, #{item.hccycrId}, #{item.function1}, #{item.function2},
                #{item.level1}, #{item.level2}, #{item.limitationsApplying1}, #{item.limitationsApplying2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除海上设施工作人员海上技能安全培训合格证明 -->
    <delete id="deleteHsssjnByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hsssjn WHERE data_id = #{dataId}
    </delete>

    <!-- 批量插入海上设施工作人员海上技能安全培训合格证明 -->
    <insert id="batchInsertHsssjn" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hsssjn (
            hsssjn_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            edit_box1, nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, an_thority_name1, an_thority_name2,
            evaluation_organization1, information_of_photo,
            year1, month1, day1, year2, month2, day2,id_number,passport_number,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hsssjnId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.editBox1}, #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.anThorityName1}, #{item.anThorityName2},
                #{item.evaluationOrganization1}, #{item.informationOfPhoto},
                #{item.year1}, #{item.month1}, #{item.day1}, #{item.year2}, #{item.month2}, #{item.day2}, #{item.IDNumber}, #{item.passportNumber},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除海船不参加船员适任证书 -->
    <delete id="deleteHcbcjcyByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcbcjcy WHERE data_id = #{dataId}
    </delete>

    <!-- 删除海船不参加船员适任证书职务等级 -->
    <delete id="deleteHcbcjcyCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcbcjcy_cap 
        WHERE hcbcjcy_id IN (
            SELECT hcbcjcy_id 
            FROM dwdb_ctf_cert_dtl_hcbcjcy 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 批量插入海船不参加船员适任证书 -->
    <insert id="batchInsertHcbcjcy" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcbcjcy (
            hcbcjcy_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, certificate_expiring_date1, certificate_expiring_date2,
            date_of_issue1, date_of_issue2, information_of_photo,
            issuing_authority1, issuing_authority2, official_use_only1, official_use_only2,
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcbcjcyId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.certificateExpiringDate1}, #{item.certificateExpiringDate2},
                #{item.dateOfIssue1}, #{item.dateOfIssue2}, #{item.informationOfPhoto},
                #{item.issuingAuthority1}, #{item.issuingAuthority2}, #{item.officialUseOnly1}, #{item.officialUseOnly2},
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入海船不参加船员适任证书职务等级 -->
    <insert id="batchInsertHcbcjcyCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcbcjcy_cap (
            hcbcjcy_capacity_id, hcbcjcy_id, gradw_and_capacity1, gradw_and_capacity2,
            alimitations_applying1, alimitations_applying2,
            create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcbcjcyCapacityId}, #{item.hcbcjcyId}, #{item.gradwAndCapacity1}, #{item.gradwAndCapacity2},
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>    

    <!-- 批量插入不参加航行职务等级 -->
    <insert id="batchInsertBcjhljzbCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_bcjhljzb_cap (
            bcjhljzb_capacity_id, bcjhljzb_id, capacity1, capacity2,
            applivations1, applivations2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.bcjhljzbCapacityId}, #{item.bcjhljzbId}, #{item.capacity1}, #{item.capacity2},
                #{item.applivations1}, #{item.applivations2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除不参加航行职务等级 -->
    <delete id="deleteBcjhljzbCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_bcjhljzb_cap 
        WHERE bcjhljzb_id IN (
            SELECT bcjhljzb_id 
            FROM dwdb_ctf_cert_dtl_bcjhljzb 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 批量插入公务船船员适任证书职务等级 -->
    <insert id="batchInsertGwccyCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_gwccy_cap (
            gwccy_capacity_id, gwccy_id, gradw_and_capacity1, gradw_and_capacity2,
            alimitations_applying1, alimitations_applying2, create_time, update_time
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.gwccyCapacityId}, #{item.gwccyId}, #{item.gradwAndCapacity1}, #{item.gradwAndCapacity2},
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 删除公务船船员适任证书职务等级 -->
    <delete id="deleteGwccyCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_gwccy_cap WHERE gwccy_id IN (
            SELECT gwccy_id FROM dwdb_ctf_cert_dtl_gwccy WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 删除公务船船员适任证书主表 -->
    <delete id="deleteGwccyByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_gwccy WHERE data_id = #{dataId}
    </delete>

    <!-- 批量插入内河船员培训许可证主表数据 -->
    <insert id="batchInsertNhcyxkz" parameterType="java.util.List">
        insert into dwdb_ctf_cert_dtl_nhcyxkz (
            nhcyxkz_id, data_id, permit_number1, an_thority_name1, training_institution_code1,
            representative1, training_program1, training_program2, registered_address1, training_location1,
            period_of_validity1, period_of_validity2, issuing_authority1, dateof_issue1, permit_number2,
            an_thority_name2, registered_address2, representative2, training_location2, period_of_validity3,
            period_of_validity4, remarks, issuing_authority2, dateof_issue2, create_time, update_time
        ) values 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhcyxkzId}, #{item.dataId}, #{item.permitNumber1}, #{item.anThorityName1}, #{item.trainingInstitutionCode1},
                #{item.representative1}, #{item.trainingProgram1}, #{item.trainingProgram2}, #{item.registeredAddress1}, #{item.trainingLocation1},
                #{item.periodOfValidity1}, #{item.periodOfValidity2}, #{item.issuingAuthority1}, #{item.dateofIssue1}, #{item.permitNumber2},
                #{item.anThorityName2}, #{item.registeredAddress2}, #{item.representative2}, #{item.trainingLocation2}, #{item.periodOfValidity3},
                #{item.periodOfValidity4}, #{item.remarks}, #{item.issuingAuthority2}, #{item.dateofIssue2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 单条插入内河船员培训许可证主表数据 -->
    <insert id="insertNhcyxkz" parameterType="com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkz">
        insert into dwdb_ctf_cert_dtl_nhcyxkz (
            nhcyxkz_id, data_id, permit_number1, an_thority_name1, training_institution_code1,
            representative1, training_program1, training_program2, registered_address1, training_location1,
            period_of_validity1, period_of_validity2, issuing_authority1, dateof_issue1, permit_number2,
            an_thority_name2, registered_address2, representative2, training_location2, period_of_validity3,
            period_of_validity4, remarks, issuing_authority2, dateof_issue2, create_time, update_time
        ) values (
            #{nhcyxkzId}, #{dataId}, #{permitNumber1}, #{anThorityName1}, #{trainingInstitutionCode1},
            #{representative1}, #{trainingProgram1}, #{trainingProgram2}, #{registeredAddress1}, #{trainingLocation1},
            #{periodOfValidity1}, #{periodOfValidity2}, #{issuingAuthority1}, #{dateofIssue1}, #{permitNumber2},
            #{anThorityName2}, #{registeredAddress2}, #{representative2}, #{trainingLocation2}, #{periodOfValidity3},
            #{periodOfValidity4}, #{remarks}, #{issuingAuthority2}, #{dateofIssue2}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入内河船员培训许可证子表数据 -->
    <insert id="batchInsertNhcyxkzItems" parameterType="java.util.List">
        insert into dwdb_ctf_cert_dtl_nhcyxkz_item (
            nhcyxkz_item_id, nhcyxkz_id, number, atraining_program, training_scale,
            create_time, update_time, create_by, update_by
        ) values 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.nhcyxkzItemId}, #{item.nhcyxkzId}, #{item.number}, #{item.atrainingProgram}, #{item.trainingScale},
                #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <!-- 删除内河船员培训许可证主表数据 -->
    <delete id="deleteNhcyxkzByDataId">
        delete from dwdb_ctf_cert_dtl_nhcyxkz where data_id = #{dataId}
    </delete>

    <!-- 删除内河船员培训许可证子表数据 -->
    <delete id="deleteNhcyxkzItemsByDataId">
        delete from dwdb_ctf_cert_dtl_nhcyxkz_item 
        where nhcyxkz_id in (
            select nhcyxkz_id 
            from dwdb_ctf_cert_dtl_nhcyxkz 
            where data_id = #{dataId}
        )
    </delete>

    <!-- 根据证照编号检查记录是否已存在 -->
    <select id="existsByCertificateNumber" resultType="java.lang.Boolean">
        SELECT 
            CASE WHEN COUNT(1) > 0 THEN true ELSE false END
        FROM 
            dwdb_certificate_data
        WHERE 
            certificate_number = #{certificateNumber}
        LIMIT 1
    </select>

    <!-- 根据数据ID检查记录是否已存在 -->
    <select id="existsByDataId" resultType="java.lang.Boolean">
        SELECT 
            CASE WHEN COUNT(1) > 0 THEN true ELSE false END
        FROM 
            dwdb_certificate_data
        WHERE 
            data_id = #{dataId}
        LIMIT 1
    </select>

    <!-- 根据证书ID检查记录是否已存在 -->
    <select id="existsByCertificateId" resultType="java.lang.Boolean">
        SELECT 
            CASE WHEN COUNT(1) > 0 THEN true ELSE false END
        FROM 
            dwdb_certificate_data
        WHERE 
            certificate_id = #{certificateId}
        LIMIT 1
    </select>

    <!-- 根据证书ID查找数据ID列表 -->
    <select id="findDataIdsByCertificateId" resultType="java.lang.String">
        SELECT 
            data_id
        FROM 
            dwdb_certificate_data
        WHERE 
            certificate_id = #{certificateId}
    </select>

    <!-- 根据数据ID获取证书类型 -->
    <select id="getCertificateTypeByDataId" resultType="java.lang.String">
        SELECT 
            certificate_type_name
        FROM 
            dwdb_certificate_data
        WHERE 
            data_id = #{dataId}
        LIMIT 1
    </select>

    <!-- 海船船员特免证明相关SQL -->
    <insert id="batchInsertHccytm" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccytm (
            hccytm_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_expiry1,
            date_of_expiry2, issued_on1, issued_on2,
            article_number1, article_number2, article_number3, article_number4,
            photo, issuing_aminstration1, issuing_aminstration2,
            official_use_only1, official_use_only2, name_of_duly_authorized_official1,
            name_of_duly_authorized_official2, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccytmId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfExpiry1},
                #{item.dateOfExpiry2}, #{item.issuedOn1}, #{item.issuedOn2},
                #{item.articleNumber1}, #{item.articleNumber2}, #{item.articleNumber3}, #{item.articleNumber4},
                #{item.photo}, #{item.issuingAminstration1}, #{item.issuingAminstration2},
                #{item.officialUseOnly1}, #{item.officialUseOnly2}, #{item.nameOfDulyAuthorizedOfficial1},
                #{item.nameOfDulyAuthorizedOfficial2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <insert id="batchInsertHccytmCapacity" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccytm_cap (
            hccytm_capacity_id, hccytm_id, capacity1, capacity2,
            alimitations_applying1, alimitations_applying2, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccytmCapacityId}, #{item.hccytmId}, #{item.capacity1}, #{item.capacity2},
                #{item.alimitationsApplying1}, #{item.alimitationsApplying2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <insert id="batchInsertHccytmFunction" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hccytm_func (
            hccytm_function_id, hccytm_id, function1, function2,
            level1, level2, limitations_applying1, limitations_applying2,
            create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hccytmFunctionId}, #{item.hccytmId}, #{item.function1}, #{item.function2},
                #{item.level1}, #{item.level2}, #{item.limitationsApplying1}, #{item.limitationsApplying2},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteHccytmByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccytm WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteHccytmCapacityByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccytm_cap 
        WHERE hccytm_id IN (
            SELECT hccytm_id 
            FROM dwdb_ctf_cert_dtl_hccytm 
            WHERE data_id = #{dataId}
        )
    </delete>

    <delete id="deleteHccytmFunctionByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hccytm_func 
        WHERE hccytm_id IN (
            SELECT hccytm_id 
            FROM dwdb_ctf_cert_dtl_hccytm 
            WHERE data_id = #{dataId}
        )
    </delete>

    <!-- 海船船员培训合格证承认签证相关SQL -->
    <insert id="batchInsertHcpxhgqz" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxhgqz (
            hcpxhgqz_id, data_id, full_name_of_the_holder1, full_name_of_the_holder2,
            nationality1, nationality2, date_of_birth1, date_of_birth2,
            gender1, gender2, certificate_no, date_of_expiry1,
            date_of_expiry2, issued_on1, issued_on2, article_number1,
            article_number2, article_number3, article_number4, photo,
            name_of_duly_authorized_official1, name_of_duly_authorized_official2,
            issuing_aminstration1, issuing_aminstration2, official_use_only1,
            official_use_only2, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcpxhgqzId}, #{item.dataId}, #{item.fullNameOfTheHolder1}, #{item.fullNameOfTheHolder2},
                #{item.nationality1}, #{item.nationality2}, #{item.dateOfBirth1}, #{item.dateOfBirth2},
                #{item.gender1}, #{item.gender2}, #{item.certificateNo}, #{item.dateOfExpiry1},
                #{item.dateOfExpiry2}, #{item.issuedOn1}, #{item.issuedOn2}, #{item.articleNumber1},
                #{item.articleNumber2}, #{item.articleNumber3}, #{item.articleNumber4}, #{item.photo},
                #{item.nameOfDulyAuthorizedOfficial1}, #{item.nameOfDulyAuthorizedOfficial2},
                #{item.issuingAminstration1}, #{item.issuingAminstration2}, #{item.officialUseOnly1},
                #{item.officialUseOnly2}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <insert id="batchInsertHcpxqzTrain" parameterType="java.util.List">
        INSERT INTO dwdb_ctf_cert_dtl_hcpxqz_train (
            hcpxhgqz_training_id, hcpxhgqz_id, title_of_the_certificate1, title_of_the_certificate2,
            clause, certificate_no1, certificate_no2, date_of_expiry3, date_of_expiry4,
            create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.hcpxhgqzTrainingId}, #{item.hcpxhgqzId}, #{item.titleOfTheCertificate1}, #{item.titleOfTheCertificate2},
                #{item.clause}, #{item.certificateNo1}, #{item.certificateNo2}, #{item.dateOfExpiry3}, #{item.dateOfExpiry4},
                #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteHcpxhgqzByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcpxhgqz WHERE data_id = #{dataId}
    </delete>

    <delete id="deleteHcpxqzTrainByDataId">
        DELETE FROM dwdb_ctf_cert_dtl_hcpxqz_train 
        WHERE hcpxhgqz_id IN (
            SELECT hcpxhgqz_id 
            FROM dwdb_ctf_cert_dtl_hcpxhgqz 
            WHERE data_id = #{dataId}
        )
    </delete>

</mapper> 