<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.config</a> &gt; <span class="el_source">CacheConfig.java</span></div><h1>CacheConfig.java</h1><pre class="source lang-java linenums">package com.example.certificate.config;

import com.example.certificate.entity.standard.CertTypeDirectory;
import com.example.certificate.entity.standard.CtfSysDept;
import com.example.certificate.entity.standard.DictYthOrgMapping;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
<span class="fc" id="L13">public class CacheConfig {</span>

    @Bean(&quot;certTypeCache&quot;)
    public Map&lt;String, CertTypeDirectory&gt; certTypeCache() {
<span class="fc" id="L17">        return new HashMap&lt;&gt;();</span>
    }
    
    @Bean(&quot;orgMappingCache&quot;)
    public Map&lt;String, DictYthOrgMapping&gt; orgMappingCache() {
<span class="fc" id="L22">        return new HashMap&lt;&gt;();</span>
    }
    
    @Bean(&quot;deptInfoCache&quot;)
    public Map&lt;String, CtfSysDept&gt; deptInfoCache() {
<span class="fc" id="L27">        return new HashMap&lt;&gt;();</span>
    }

    @Bean(&quot;capMappingCache&quot;)
    public Map&lt;String, String&gt; capMappingCache() {
<span class="fc" id="L32">        return new HashMap&lt;&gt;();</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>