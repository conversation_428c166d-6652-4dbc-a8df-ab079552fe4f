<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataSourceConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.config</a> &gt; <span class="el_source">DataSourceConfig.java</span></div><h1>DataSourceConfig.java</h1><pre class="source lang-java linenums">package com.example.certificate.config;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = &quot;com.example.certificate.mapper.aggregate&quot;, sqlSessionFactoryRef = &quot;aggregateSqlSessionFactory&quot;)
@MapperScan(basePackages = &quot;com.example.certificate.mapper.standard&quot;, sqlSessionFactoryRef = &quot;standardSqlSessionFactory&quot;)
<span class="fc" id="L19">public class DataSourceConfig {</span>

    @Primary
    @Bean(name = &quot;aggregateDataSource&quot;)
    @ConfigurationProperties(prefix = &quot;spring.datasource.aggregate&quot;)
    public DataSource aggregateDataSource() {
<span class="fc" id="L25">        return new HikariDataSource();</span>
    }

    @Bean(name = &quot;standardDataSource&quot;)
    @ConfigurationProperties(prefix = &quot;spring.datasource.standard&quot;)
    public DataSource standardDataSource() {
<span class="fc" id="L31">        return new HikariDataSource();</span>
    }

    @Bean(name = &quot;aggregateSqlSessionFactory&quot;)
    @Primary
    public SqlSessionFactory aggregateSqlSessionFactory(@Qualifier(&quot;aggregateDataSource&quot;) DataSource dataSource) throws Exception {
<span class="fc" id="L37">        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();</span>
<span class="fc" id="L38">        sqlSessionFactory.setDataSource(dataSource);</span>
<span class="fc" id="L39">        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()</span>
<span class="fc" id="L40">                .getResources(&quot;classpath:mapper/aggregate/*.xml&quot;));</span>
<span class="fc" id="L41">        return sqlSessionFactory.getObject();</span>
    }

    @Bean(name = &quot;standardSqlSessionFactory&quot;)
    public SqlSessionFactory standardSqlSessionFactory(@Qualifier(&quot;standardDataSource&quot;) DataSource dataSource) throws Exception {
<span class="fc" id="L46">        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();</span>
<span class="fc" id="L47">        sqlSessionFactory.setDataSource(dataSource);</span>
<span class="fc" id="L48">        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()</span>
<span class="fc" id="L49">                .getResources(&quot;classpath:mapper/standard/*.xml&quot;));</span>
<span class="fc" id="L50">        return sqlSessionFactory.getObject();</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>