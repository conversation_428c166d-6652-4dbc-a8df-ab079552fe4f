<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TraceIdConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.config</a> &gt; <span class="el_source">TraceIdConfig.java</span></div><h1>TraceIdConfig.java</h1><pre class="source lang-java linenums">package com.example.certificate.config;

import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

@Configuration
<span class="fc" id="L14">public class TraceIdConfig implements WebMvcConfigurer {</span>

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
<span class="fc" id="L18">        registry.addInterceptor(new HandlerInterceptor() {</span>
            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
<span class="nc" id="L21">                String traceId = UUID.randomUUID().toString().replace(&quot;-&quot;, &quot;&quot;);</span>
<span class="nc" id="L22">                MDC.put(&quot;traceId&quot;, traceId);</span>
<span class="nc" id="L23">                return true;</span>
            }

            @Override
            public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
<span class="nc" id="L28">                MDC.remove(&quot;traceId&quot;);</span>
<span class="nc" id="L29">            }</span>
        });
<span class="fc" id="L31">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.************</span></div></body></html>