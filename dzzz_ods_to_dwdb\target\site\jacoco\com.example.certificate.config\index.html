<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.example.certificate.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <span class="el_package">com.example.certificate.config</span></div><h1>com.example.certificate.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">129 of 221</td><td class="ctr2">41%</td><td class="bar">22 of 22</td><td class="ctr2">0%</td><td class="ctr1">19</td><td class="ctr2">35</td><td class="ctr1">7</td><td class="ctr2">29</td><td class="ctr1">8</td><td class="ctr2">24</td><td class="ctr1">0</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a2"><a href="TaskConfig.html" class="el_class">TaskConfig</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="115" alt="115"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">8%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">20</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j0">6</td><td class="ctr2" id="k0">9</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a4"><a href="TraceIdConfig$1.html" class="el_class">TraceIdConfig.new HandlerInterceptor() {...}</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c3">30%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j1">2</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="DataSourceConfig.html" class="el_class">DataSourceConfig</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="45" alt="45"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">5</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="CacheConfig.html" class="el_class">CacheConfig</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="19" alt="19"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="TraceIdConfig.html" class="el_class">TraceIdConfig</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">2</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>