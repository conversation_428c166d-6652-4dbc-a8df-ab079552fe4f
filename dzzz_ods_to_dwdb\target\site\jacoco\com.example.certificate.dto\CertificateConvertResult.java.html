<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateConvertResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.dto</a> &gt; <span class="el_source">CertificateConvertResult.java</span></div><h1>CertificateConvertResult.java</h1><pre class="source lang-java linenums">package com.example.certificate.dto;

import lombok.Data;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.example.certificate.entity.standard.DwdbCertificateDataAttribute;

/**
 * 证书转换结果类
 * 用于存储证书转换的结果，包括主表数据和子表数据
 */
<span class="nc bnc" id="L14" title="All 58 branches missed.">@Data</span>
public class CertificateConvertResult {
    
    /**
     * 数据ID
     */
<span class="nc" id="L20">    private String dataId;</span>
    
    /**
     * 证书类型
     */
<span class="nc" id="L25">    private String certificateType;</span>
    
    /**
     * 主表数据
     */
<span class="nc" id="L30">    private Object mainTableData;</span>
    
    /**
     * 子表数据列表，key为子表名称，value为子表数据列表
     */
<span class="nc" id="L35">    private Map&lt;String, List&lt;?&gt;&gt; subTableDataMap = new HashMap&lt;&gt;();</span>
    
    /**
     * 是否有错误
     */
<span class="nc" id="L40">    private boolean hasError;</span>
    
    /**
     * 错误信息
     */
<span class="nc" id="L45">    private String errorMessage;</span>
    
    /**
     * 未被使用的属性列表
     */
<span class="nc" id="L50">    private List&lt;DwdbCertificateDataAttribute&gt; unusedAttributes = new ArrayList&lt;&gt;();</span>
    
    /**
     * 设置子表数据列表
     * @param key 子表名称
     * @param dataList 子表数据列表
     */
    public void setSubTableDataList(String key, List&lt;?&gt; dataList) {
<span class="nc" id="L58">        this.subTableDataMap.put(key, dataList);</span>
<span class="nc" id="L59">    }</span>
    
    /**
     * 设置子表数据列表
     * @param key 子表名称
     * @param data 子表数据对象
     */
    public void setSubTableDataList(String key, Object data) {
<span class="nc bnc" id="L67" title="All 2 branches missed.">        if (data instanceof List) {</span>
<span class="nc" id="L68">            this.subTableDataMap.put(key, (List&lt;?&gt;) data);</span>
        }
<span class="nc" id="L70">    }</span>
    
    /**
     * 获取指定类型的子表数据列表
     * @param key 子表类型标识
     * @return 子表数据列表
     */
    public List&lt;?&gt; getSubTableDataList(String key) {
<span class="nc" id="L78">        return subTableDataMap.getOrDefault(key, new ArrayList&lt;&gt;());</span>
    }
    
    /**
     * 判断是否包含指定类型的子表数据
     * @param key 子表类型标识
     * @return 是否包含
     */
    public boolean hasSubTableData(String key) {
<span class="nc bnc" id="L87" title="All 2 branches missed.">        return subTableDataMap.containsKey(key) &amp;&amp; </span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">               subTableDataMap.get(key) != null &amp;&amp; </span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">               !subTableDataMap.get(key).isEmpty();</span>
    }
    
    /**
     * 设置未使用的属性列表
     * @param attributes 未使用的属性列表
     */
    public void setUnusedAttributes(List&lt;DwdbCertificateDataAttribute&gt; attributes) {
<span class="nc" id="L97">        this.unusedAttributes = attributes;</span>
<span class="nc" id="L98">    }</span>
    
    /**
     * 获取未使用的属性列表
     * @return 未使用的属性列表
     */
    public List&lt;DwdbCertificateDataAttribute&gt; getUnusedAttributes() {
<span class="nc" id="L105">        return this.unusedAttributes;</span>
    }
    
    /**
     * 判断是否有未使用的属性
     * @return 是否有未使用的属性
     */
    public boolean hasUnusedAttributes() {
<span class="nc bnc" id="L113" title="All 4 branches missed.">        return unusedAttributes != null &amp;&amp; !unusedAttributes.isEmpty();</span>
    }
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>