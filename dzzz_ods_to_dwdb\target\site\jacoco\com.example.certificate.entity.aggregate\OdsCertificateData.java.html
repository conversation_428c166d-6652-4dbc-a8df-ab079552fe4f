<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OdsCertificateData.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.aggregate</a> &gt; <span class="el_source">OdsCertificateData.java</span></div><h1>OdsCertificateData.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.aggregate;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import java.util.Date;
import java.time.LocalDateTime;

<span class="nc bnc" id="L9" title="All 358 branches missed.">@Data</span>
@TableName(&quot;ods_certificate_data&quot;)
//电子证照信息汇聚表
public class OdsCertificateData {
    //证照数据主键
    @TableId
<span class="nc" id="L15">    private String dataid;</span>
    
    //电子证照标识码
<span class="nc" id="L18">    private String certificateid;</span>
    
    //目录ID
<span class="nc" id="L21">    private String catalogid;</span>
    
    //目录名称
<span class="nc" id="L24">    private String catalogname;</span>
    
    //模板ID
<span class="nc" id="L27">    private String templateid;</span>
    
    //证照类型名称
<span class="nc" id="L30">    private String certificatetype;</span>
    
    //证照类型代码
<span class="nc" id="L33">    private String certificatetypecode;</span>
    
    //证照颁发机构
<span class="nc" id="L36">    private String issuedept;</span>
    
    //证照颁发机构代码
<span class="nc" id="L39">    private String issuedeptcode;</span>
    
    //证照所属地区编码
<span class="nc" id="L42">    private String certificateareacode;</span>
    
    //持证者名称
<span class="nc" id="L45">    private String certificateholder;</span>
    
    //持证者代码
<span class="nc" id="L48">    private String certificateholdercode;</span>
    
    //持证者类型
<span class="nc" id="L51">    private String certificateholdertype;</span>
    
    //证照编号
<span class="nc" id="L54">    private String certificatenumber;</span>
    
    //颁证日期
<span class="nc" id="L57">    private String issuedate;</span>
    
    //有效期起始日
<span class="nc" id="L60">    private String validbegindate;</span>
    
    //有效期截止日
<span class="nc" id="L63">    private String validenddate;</span>
    
    //照面拓展信息
<span class="nc" id="L66">    private String surfacedata;</span>
    
    //证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
<span class="nc" id="L69">    private String status;</span>
    
    //登记人员ID
<span class="nc" id="L72">    private String creator;</span>
    
    //创建时间
<span class="nc" id="L75">    private String createtime;</span>
    
    //最后操作人
<span class="nc" id="L78">    private String operator;</span>
    
    //修改时间
<span class="nc" id="L81">    private String updatetime;</span>
    
    //文件保存位置
<span class="nc" id="L84">    private String filepath;</span>
    
    //同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
<span class="nc" id="L87">    private String syncstatus;</span>
    
    //备注
<span class="nc" id="L90">    private String remarks;</span>
    
    //登记部门
<span class="nc" id="L93">    private String deptid;</span>
    
    //申请编号
<span class="nc" id="L96">    private String applynum;</span>
    
    //事项名称
<span class="nc" id="L99">    private String affairname;</span>
    
    //事项类型
<span class="nc" id="L102">    private String affairtype;</span>
    
    //服务对象
<span class="nc" id="L105">    private String servebusiness;</span>
    
    //事项ID
<span class="nc" id="L108">    private String affairid;</span>
    
    //事项编号
<span class="nc" id="L111">    private String affairnum;</span>
    
    //签章类型
<span class="nc" id="L114">    private String qztype;</span>
    
    //证书类别
<span class="nc" id="L117">    private String zztype;</span>
    
    //加签文件备用地址
<span class="nc" id="L120">    private String drafturl;</span>
    
    //是否预览
<span class="nc" id="L123">    private String isview;</span>
    
    //归档编号
<span class="nc" id="L126">    private String sortname;</span>
    
    //备用字段
<span class="nc" id="L129">    private String col1;</span>
    
    //验证日期
<span class="nc" id="L132">    private String verifydate;</span>
    
    //验证
<span class="nc" id="L135">    private String verification;</span>
    
    //统一信用代码
<span class="nc" id="L138">    private String creditcode;</span>
    
    //印章名称
<span class="nc" id="L141">    private String sealname;</span>
    
    //数据同步时间
<span class="nc" id="L144">    private LocalDateTime fcdcDate;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>