<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertCapMapping.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">CertCapMapping.java</span></div><h1>CertCapMapping.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.util.Date;

/**
 * 职务映射表实体类
 */
<span class="pc bnc" id="L12" title="All 86 branches missed.">@Data</span>
@TableName(&quot;dwdb_ctf_cert_cap_mapping&quot;)
public class CertCapMapping {
    
    @TableId(&quot;CAP_MAPPING_ID&quot;)
<span class="nc" id="L17">    private String capMappingId;      // 主键</span>
    
    @TableField(&quot;SOURCE_CAP_NAME&quot;)
<span class="fc" id="L20">    private String sourceCapName;     // 源职务名称</span>
    
    @TableField(&quot;DEST_CAP_NAME&quot;)
<span class="fc" id="L23">    private String destCapName;       // 目标职务名称</span>
    
    @TableField(&quot;CREATE_OPER_ID&quot;)
<span class="nc" id="L26">    private String createOperId;      // 创建人</span>
    
    @TableField(&quot;CREATE_DATE&quot;)
<span class="nc" id="L29">    private Date createDate;          // 创建时间</span>
    
    @TableField(&quot;MODIFY_OPER_ID&quot;)
<span class="nc" id="L32">    private String modifyOperId;      // 修改人</span>
    
    @TableField(&quot;MODIFY_DATE&quot;)
<span class="nc" id="L35">    private Date modifyDate;          // 修改时间</span>
    
    @TableField(&quot;DEL_FLAG&quot;)
<span class="nc" id="L38">    private String delFlag;           // 逻辑删除标记(0--正常 1--删除)</span>
    
    @TableField(&quot;rec_create_date&quot;)
<span class="nc" id="L41">    private Date recCreateDate;       // 记录创建日期</span>
    
    @TableField(&quot;rec_modify_date&quot;)
<span class="nc" id="L44">    private Date recModifyDate;       // 记录修改日期</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>