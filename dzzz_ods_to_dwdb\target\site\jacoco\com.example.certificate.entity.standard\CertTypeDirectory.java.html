<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertTypeDirectory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">CertTypeDirectory.java</span></div><h1>CertTypeDirectory.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

<span class="pc bnc" id="L8" title="All 214 branches missed.">@Data</span>
@TableName(&quot;cert_type_directory&quot;)
public class CertTypeDirectory {
    
    @TableId
<span class="nc" id="L13">    private String certTypeDirId;</span>
    
<span class="nc" id="L15">    private String certificateTypeCode;</span>
    
<span class="nc" id="L17">    private String certificateTypeName;</span>
    
<span class="nc" id="L19">    private String parentId;</span>
    
<span class="nc" id="L21">    private Integer certLevel;</span>
    
<span class="nc" id="L23">    private Integer sort;</span>
    
<span class="nc" id="L25">    private String certificateDefineAuthorityName;</span>
    
<span class="nc" id="L27">    private String certificateDefineAuthorityCode;</span>
    
<span class="nc" id="L29">    private String certificateDefineAuthorityLevel;</span>
    
<span class="nc" id="L31">    private String relatedItemName;</span>
    
<span class="nc" id="L33">    private String relatedItemId;</span>
    
<span class="nc" id="L35">    private String relatedItemCode;</span>
    
<span class="nc" id="L37">    private String certificateHolderCategory;</span>
    
<span class="nc" id="L39">    private String validityRange;</span>
    
<span class="nc" id="L41">    private String certificateIssuingAuthorityLevel;</span>
    
<span class="nc" id="L43">    private String createOrgCode;</span>
    
<span class="nc" id="L45">    private String createOrgName;</span>
    
<span class="nc" id="L47">    private String approvalStatus;</span>
    
<span class="nc" id="L49">    private String issueStatus;</span>
    
<span class="nc" id="L51">    private Date issueDate;</span>

<span class="fc" id="L53">    private String catalogId;</span>
    
<span class="nc" id="L55">    private Date createTime;</span>
    
<span class="nc" id="L57">    private String createBy;</span>
    
<span class="nc" id="L59">    private Date updateTime;</span>
    
<span class="nc" id="L61">    private String updateBy;</span>
    
<span class="nc" id="L63">    private String delFlag;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>