<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CtfSysDept.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">CtfSysDept.java</span></div><h1>CtfSysDept.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;

<span class="pc bnc" id="L9" title="All 262 branches missed.">@Data</span>
@TableName(&quot;ctf_sys_dept&quot;)
public class CtfSysDept {
    
    @TableId
<span class="nc" id="L14">    private String deptId;</span>
    
<span class="nc" id="L16">    private String name;</span>
    
<span class="nc" id="L18">    private BigDecimal sort;</span>
    
<span class="nc" id="L20">    private Date createTime;</span>
    
<span class="nc" id="L22">    private Date updateTime;</span>
    
<span class="nc" id="L24">    private String delFlag;</span>
    
<span class="nc" id="L26">    private String parentId;</span>
    
<span class="nc" id="L28">    private String govLevel;</span>
    
<span class="fc" id="L30">    private String code;</span>
    
<span class="nc" id="L32">    private String isShow;</span>
    
<span class="nc" id="L34">    private String hallShow;</span>
    
<span class="nc" id="L36">    private String creditCode;</span>
    
<span class="nc" id="L38">    private String handleDate;</span>
    
<span class="nc" id="L40">    private String approveAddress;</span>
    
<span class="nc" id="L42">    private String consultType;</span>
    
<span class="nc" id="L44">    private String superviseType;</span>
    
<span class="nc" id="L46">    private String isCenter;</span>
    
<span class="nc" id="L48">    private String deptPhone;</span>
    
<span class="nc" id="L50">    private String taskCode;</span>
    
<span class="nc" id="L52">    private String parentCode;</span>
    
<span class="nc" id="L54">    private String deptType;</span>
    
<span class="nc" id="L56">    private String nameAbbr;</span>
    
<span class="nc" id="L58">    private String codesetId;</span>
    
<span class="nc" id="L60">    private String orgTag;</span>
    
<span class="nc" id="L62">    private String orgCcname;</span>
    
<span class="nc" id="L64">    private String orgDesc;</span>
    
<span class="nc" id="L66">    private String orgCertifit;</span>
    
<span class="nc" id="L68">    private String zsOrgType;</span>
    
<span class="nc" id="L70">    private String oldDeptId;</span>
    
<span class="nc" id="L72">    private String areaId;</span>
    
<span class="nc" id="L74">    private String grade;</span>
    
<span class="nc" id="L76">    private String uscc;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>