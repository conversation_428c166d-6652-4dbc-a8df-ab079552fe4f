<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DictYthOrgMapping.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DictYthOrgMapping.java</span></div><h1>DictYthOrgMapping.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

<span class="pc bnc" id="L8" title="All 86 branches missed.">@Data</span>
@TableName(&quot;dict_yth_org_mapping&quot;)
public class DictYthOrgMapping {
    
    @TableId
<span class="nc" id="L13">    private String id;</span>
    
<span class="fc" id="L15">    private String srcOrgCode;</span>
    
<span class="nc" id="L17">    private String srcOrgName;</span>
    
<span class="nc" id="L19">    private String orgCode;</span>
    
<span class="nc" id="L21">    private String orgName;</span>
    
<span class="nc" id="L23">    private String remark;</span>
    
<span class="nc" id="L25">    private String sourceTypeCode;</span>
    
<span class="nc" id="L27">    private String sourceCode;</span>
    
<span class="nc" id="L29">    private Date recCreateDate;</span>
    
<span class="nc" id="L31">    private Date recModifyDate;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>