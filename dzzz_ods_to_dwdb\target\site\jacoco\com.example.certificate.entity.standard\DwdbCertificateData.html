<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCertificateData</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_class">DwdbCertificateData</span></div><h1>DwdbCertificateData</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,480 of 3,480</td><td class="ctr2">0%</td><td class="bar">646 of 646</td><td class="ctr2">0%</td><td class="ctr1">488</td><td class="ctr2">488</td><td class="ctr1">81</td><td class="ctr2">81</td><td class="ctr1">165</td><td class="ctr2">165</td></tr></tfoot><tbody><tr><td id="a2"><a href="DwdbCertificateData.java.html#L8" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="1,381" alt="1,381"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="486" alt="486"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">244</td><td class="ctr2" id="g0">244</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a83"><a href="DwdbCertificateData.java.html#L8" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="1,126" alt="1,126"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="160" alt="160"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">81</td><td class="ctr2" id="g1">81</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a164"><a href="DwdbCertificateData.java.html#L8" class="el_method">toString()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="407" alt="407"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a124"><a href="DwdbCertificateData.java.html#L8" class="el_method">setDataId(String)</a></td><td class="bar" id="b3"/><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a106"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateId(String)</a></td><td class="bar" id="b4"/><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a151"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTemplateId(String)</a></td><td class="bar" id="b5"/><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a116"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateTypeName(String)</a></td><td class="bar" id="b6"/><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a115"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateTypeCode(String)</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a96"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateDefineAuthorityName(String)</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a95"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateDefineAuthorityCode(String)</a></td><td class="bar" id="b9"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a140"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRelatedItemName(String)</a></td><td class="bar" id="b10"/><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a139"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRelatedItemCode(String)</a></td><td class="bar" id="b11"/><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a101"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateHolderCategory(String)</a></td><td class="bar" id="b12"/><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a102"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateHolderCategoryName(String)</a></td><td class="bar" id="b13"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a163"><a href="DwdbCertificateData.java.html#L8" class="el_method">setValidityRange(String)</a></td><td class="bar" id="b14"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a107"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateIdentifier(String)</a></td><td class="bar" id="b15"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a112"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateName(String)</a></td><td class="bar" id="b16"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a113"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateNumber(String)</a></td><td class="bar" id="b17"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a111"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateIssuingAuthorityName(String)</a></td><td class="bar" id="b18"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a110"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateIssuingAuthorityCode(String)</a></td><td class="bar" id="b19"/><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a108"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateIssuedDate(String)</a></td><td class="bar" id="b20"/><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a104"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateHolderName(String)</a></td><td class="bar" id="b21"/><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a103"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateHolderCode(String)</a></td><td class="bar" id="b22"/><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a105"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateHolderTypeName(String)</a></td><td class="bar" id="b23"/><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a97"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateEffectiveDate(String)</a></td><td class="bar" id="b24"/><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a99"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateExpiringDate(String)</a></td><td class="bar" id="b25"/><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a130"><a href="DwdbCertificateData.java.html#L8" class="el_method">setIssueDeptCode2(String)</a></td><td class="bar" id="b26"/><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a131"><a href="DwdbCertificateData.java.html#L8" class="el_method">setIssueDeptCode3(String)</a></td><td class="bar" id="b27"/><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a94"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateAreaCode(String)</a></td><td class="bar" id="b28"/><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a149"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSurfaceData(String)</a></td><td class="bar" id="b29"/><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a114"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateStatus(String)</a></td><td class="bar" id="b30"/><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a121"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCreatorId(String)</a></td><td class="bar" id="b31"/><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a120"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCreateTime(String)</a></td><td class="bar" id="b32"/><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a134"><a href="DwdbCertificateData.java.html#L8" class="el_method">setOperatorId(String)</a></td><td class="bar" id="b33"/><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a162"><a href="DwdbCertificateData.java.html#L8" class="el_method">setUpdateTime(String)</a></td><td class="bar" id="b34"/><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a129"><a href="DwdbCertificateData.java.html#L8" class="el_method">setFilePath(String)</a></td><td class="bar" id="b35"/><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a150"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSyncStatus(String)</a></td><td class="bar" id="b36"/><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a141"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRemarks(String)</a></td><td class="bar" id="b37"/><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a125"><a href="DwdbCertificateData.java.html#L8" class="el_method">setDeptId(String)</a></td><td class="bar" id="b38"/><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a89"><a href="DwdbCertificateData.java.html#L8" class="el_method">setApplyNum(String)</a></td><td class="bar" id="b39"/><td class="ctr2" id="c39">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a86"><a href="DwdbCertificateData.java.html#L8" class="el_method">setAffairType(String)</a></td><td class="bar" id="b40"/><td class="ctr2" id="c40">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a145"><a href="DwdbCertificateData.java.html#L8" class="el_method">setServeBusiness(String)</a></td><td class="bar" id="b41"/><td class="ctr2" id="c41">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a84"><a href="DwdbCertificateData.java.html#L8" class="el_method">setAffairId(String)</a></td><td class="bar" id="b42"/><td class="ctr2" id="c42">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a85"><a href="DwdbCertificateData.java.html#L8" class="el_method">setAffairNum(String)</a></td><td class="bar" id="b43"/><td class="ctr2" id="c43">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a136"><a href="DwdbCertificateData.java.html#L8" class="el_method">setQzType(String)</a></td><td class="bar" id="b44"/><td class="ctr2" id="c44">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a126"><a href="DwdbCertificateData.java.html#L8" class="el_method">setDraftUrl(String)</a></td><td class="bar" id="b45"/><td class="ctr2" id="c45">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a147"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSortName(String)</a></td><td class="bar" id="b46"/><td class="ctr2" id="c46">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a144"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSealname(String)</a></td><td class="bar" id="b47"/><td class="ctr2" id="c47">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a148"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSourceCode(String)</a></td><td class="bar" id="b48"/><td class="ctr2" id="c48">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a137"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRecCreateDate(Date)</a></td><td class="bar" id="b49"/><td class="ctr2" id="c49">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a138"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRecModifyDate(Date)</a></td><td class="bar" id="b50"/><td class="ctr2" id="c50">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a132"><a href="DwdbCertificateData.java.html#L8" class="el_method">setMsaOrgCode(String)</a></td><td class="bar" id="b51"/><td class="ctr2" id="c51">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a92"><a href="DwdbCertificateData.java.html#L8" class="el_method">setBirth(String)</a></td><td class="bar" id="b52"/><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a133"><a href="DwdbCertificateData.java.html#L8" class="el_method">setNameEn(String)</a></td><td class="bar" id="b53"/><td class="ctr2" id="c53">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a118"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCountryCn(String)</a></td><td class="bar" id="b54"/><td class="ctr2" id="c54">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a119"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCountryEn(String)</a></td><td class="bar" id="b55"/><td class="ctr2" id="c55">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a146"><a href="DwdbCertificateData.java.html#L8" class="el_method">setSignDeptEn(String)</a></td><td class="bar" id="b56"/><td class="ctr2" id="c56">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a87"><a href="DwdbCertificateData.java.html#L8" class="el_method">setApplivationsCn(String)</a></td><td class="bar" id="b57"/><td class="ctr2" id="c57">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a122"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCrewType(String)</a></td><td class="bar" id="b58"/><td class="ctr2" id="c58">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a135"><a href="DwdbCertificateData.java.html#L8" class="el_method">setQualificationCn(String)</a></td><td class="bar" id="b59"/><td class="ctr2" id="c59">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a93"><a href="DwdbCertificateData.java.html#L8" class="el_method">setBirthEn(String)</a></td><td class="bar" id="b60"/><td class="ctr2" id="c60">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h60">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a117"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertPrintNo(String)</a></td><td class="bar" id="b61"/><td class="ctr2" id="c61">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h61">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a98"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateEffectiveDateEn(String)</a></td><td class="bar" id="b62"/><td class="ctr2" id="c62">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h62">1</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a100"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateExpiringDateEn(String)</a></td><td class="bar" id="b63"/><td class="ctr2" id="c63">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a109"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCertificateIssuedDateEn(String)</a></td><td class="bar" id="b64"/><td class="ctr2" id="c64">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a123"><a href="DwdbCertificateData.java.html#L8" class="el_method">setCrewTypeEn(String)</a></td><td class="bar" id="b65"/><td class="ctr2" id="c65">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a88"><a href="DwdbCertificateData.java.html#L8" class="el_method">setApplivationsEn(String)</a></td><td class="bar" id="b66"/><td class="ctr2" id="c66">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a90"><a href="DwdbCertificateData.java.html#L8" class="el_method">setAuthAuthorityCn(String)</a></td><td class="bar" id="b67"/><td class="ctr2" id="c67">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a91"><a href="DwdbCertificateData.java.html#L8" class="el_method">setAuthAuthorityEn(String)</a></td><td class="bar" id="b68"/><td class="ctr2" id="c68">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a127"><a href="DwdbCertificateData.java.html#L8" class="el_method">setEvaOrgCn(String)</a></td><td class="bar" id="b69"/><td class="ctr2" id="c69">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a128"><a href="DwdbCertificateData.java.html#L8" class="el_method">setEvaOrgEn(String)</a></td><td class="bar" id="b70"/><td class="ctr2" id="c70">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a160"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainManagerNameCn(String)</a></td><td class="bar" id="b71"/><td class="ctr2" id="c71">0%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f71">1</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h71">1</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a161"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainManagerNameEn(String)</a></td><td class="bar" id="b72"/><td class="ctr2" id="c72">0%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f72">1</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h72">1</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a142"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRepresentativeCn(String)</a></td><td class="bar" id="b73"/><td class="ctr2" id="c73">0%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f73">1</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h73">1</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a143"><a href="DwdbCertificateData.java.html#L8" class="el_method">setRepresentativeEn(String)</a></td><td class="bar" id="b74"/><td class="ctr2" id="c74">0%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f74">1</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h74">1</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k74">1</td></tr><tr><td id="a158"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingNamesCn(String)</a></td><td class="bar" id="b75"/><td class="ctr2" id="c75">0%</td><td class="bar" id="d75"/><td class="ctr2" id="e75">n/a</td><td class="ctr1" id="f75">1</td><td class="ctr2" id="g75">1</td><td class="ctr1" id="h75">1</td><td class="ctr2" id="i75">1</td><td class="ctr1" id="j75">1</td><td class="ctr2" id="k75">1</td></tr><tr><td id="a159"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingNamesEn(String)</a></td><td class="bar" id="b76"/><td class="ctr2" id="c76">0%</td><td class="bar" id="d76"/><td class="ctr2" id="e76">n/a</td><td class="ctr1" id="f76">1</td><td class="ctr2" id="g76">1</td><td class="ctr1" id="h76">1</td><td class="ctr2" id="i76">1</td><td class="ctr1" id="j76">1</td><td class="ctr2" id="k76">1</td></tr><tr><td id="a155"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingIssueDatesCn(String)</a></td><td class="bar" id="b77"/><td class="ctr2" id="c77">0%</td><td class="bar" id="d77"/><td class="ctr2" id="e77">n/a</td><td class="ctr1" id="f77">1</td><td class="ctr2" id="g77">1</td><td class="ctr1" id="h77">1</td><td class="ctr2" id="i77">1</td><td class="ctr1" id="j77">1</td><td class="ctr2" id="k77">1</td></tr><tr><td id="a156"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingIssueDatesEn(String)</a></td><td class="bar" id="b78"/><td class="ctr2" id="c78">0%</td><td class="bar" id="d78"/><td class="ctr2" id="e78">n/a</td><td class="ctr1" id="f78">1</td><td class="ctr2" id="g78">1</td><td class="ctr1" id="h78">1</td><td class="ctr2" id="i78">1</td><td class="ctr1" id="j78">1</td><td class="ctr2" id="k78">1</td></tr><tr><td id="a152"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingEffectiveDatesCn(String)</a></td><td class="bar" id="b79"/><td class="ctr2" id="c79">0%</td><td class="bar" id="d79"/><td class="ctr2" id="e79">n/a</td><td class="ctr1" id="f79">1</td><td class="ctr2" id="g79">1</td><td class="ctr1" id="h79">1</td><td class="ctr2" id="i79">1</td><td class="ctr1" id="j79">1</td><td class="ctr2" id="k79">1</td></tr><tr><td id="a153"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingEffectiveDatesEn(String)</a></td><td class="bar" id="b80"/><td class="ctr2" id="c80">0%</td><td class="bar" id="d80"/><td class="ctr2" id="e80">n/a</td><td class="ctr1" id="f80">1</td><td class="ctr2" id="g80">1</td><td class="ctr1" id="h80">1</td><td class="ctr2" id="i80">1</td><td class="ctr1" id="j80">1</td><td class="ctr2" id="k80">1</td></tr><tr><td id="a154"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingInstitutionCode(String)</a></td><td class="bar" id="b81"/><td class="ctr2" id="c81">0%</td><td class="bar" id="d81"/><td class="ctr2" id="e81">n/a</td><td class="ctr1" id="f81">1</td><td class="ctr2" id="g81">1</td><td class="ctr1" id="h81">1</td><td class="ctr2" id="i81">1</td><td class="ctr1" id="j81">1</td><td class="ctr2" id="k81">1</td></tr><tr><td id="a157"><a href="DwdbCertificateData.java.html#L8" class="el_method">setTrainingLocation(String)</a></td><td class="bar" id="b82"/><td class="ctr2" id="c82">0%</td><td class="bar" id="d82"/><td class="ctr2" id="e82">n/a</td><td class="ctr1" id="f82">1</td><td class="ctr2" id="g82">1</td><td class="ctr1" id="h82">1</td><td class="ctr2" id="i82">1</td><td class="ctr1" id="j82">1</td><td class="ctr2" id="k82">1</td></tr><tr><td id="a1"><a href="DwdbCertificateData.java.html#L8" class="el_method">DwdbCertificateData()</a></td><td class="bar" id="b83"/><td class="ctr2" id="c83">0%</td><td class="bar" id="d83"/><td class="ctr2" id="e83">n/a</td><td class="ctr1" id="f83">1</td><td class="ctr2" id="g83">1</td><td class="ctr1" id="h83">1</td><td class="ctr2" id="i83">1</td><td class="ctr1" id="j83">1</td><td class="ctr2" id="k83">1</td></tr><tr><td id="a43"><a href="DwdbCertificateData.java.html#L14" class="el_method">getDataId()</a></td><td class="bar" id="b84"/><td class="ctr2" id="c84">0%</td><td class="bar" id="d84"/><td class="ctr2" id="e84">n/a</td><td class="ctr1" id="f84">1</td><td class="ctr2" id="g84">1</td><td class="ctr1" id="h84">1</td><td class="ctr2" id="i84">1</td><td class="ctr1" id="j84">1</td><td class="ctr2" id="k84">1</td></tr><tr><td id="a25"><a href="DwdbCertificateData.java.html#L17" class="el_method">getCertificateId()</a></td><td class="bar" id="b85"/><td class="ctr2" id="c85">0%</td><td class="bar" id="d85"/><td class="ctr2" id="e85">n/a</td><td class="ctr1" id="f85">1</td><td class="ctr2" id="g85">1</td><td class="ctr1" id="h85">1</td><td class="ctr2" id="i85">1</td><td class="ctr1" id="j85">1</td><td class="ctr2" id="k85">1</td></tr><tr><td id="a70"><a href="DwdbCertificateData.java.html#L20" class="el_method">getTemplateId()</a></td><td class="bar" id="b86"/><td class="ctr2" id="c86">0%</td><td class="bar" id="d86"/><td class="ctr2" id="e86">n/a</td><td class="ctr1" id="f86">1</td><td class="ctr2" id="g86">1</td><td class="ctr1" id="h86">1</td><td class="ctr2" id="i86">1</td><td class="ctr1" id="j86">1</td><td class="ctr2" id="k86">1</td></tr><tr><td id="a35"><a href="DwdbCertificateData.java.html#L23" class="el_method">getCertificateTypeName()</a></td><td class="bar" id="b87"/><td class="ctr2" id="c87">0%</td><td class="bar" id="d87"/><td class="ctr2" id="e87">n/a</td><td class="ctr1" id="f87">1</td><td class="ctr2" id="g87">1</td><td class="ctr1" id="h87">1</td><td class="ctr2" id="i87">1</td><td class="ctr1" id="j87">1</td><td class="ctr2" id="k87">1</td></tr><tr><td id="a34"><a href="DwdbCertificateData.java.html#L26" class="el_method">getCertificateTypeCode()</a></td><td class="bar" id="b88"/><td class="ctr2" id="c88">0%</td><td class="bar" id="d88"/><td class="ctr2" id="e88">n/a</td><td class="ctr1" id="f88">1</td><td class="ctr2" id="g88">1</td><td class="ctr1" id="h88">1</td><td class="ctr2" id="i88">1</td><td class="ctr1" id="j88">1</td><td class="ctr2" id="k88">1</td></tr><tr><td id="a15"><a href="DwdbCertificateData.java.html#L29" class="el_method">getCertificateDefineAuthorityName()</a></td><td class="bar" id="b89"/><td class="ctr2" id="c89">0%</td><td class="bar" id="d89"/><td class="ctr2" id="e89">n/a</td><td class="ctr1" id="f89">1</td><td class="ctr2" id="g89">1</td><td class="ctr1" id="h89">1</td><td class="ctr2" id="i89">1</td><td class="ctr1" id="j89">1</td><td class="ctr2" id="k89">1</td></tr><tr><td id="a14"><a href="DwdbCertificateData.java.html#L32" class="el_method">getCertificateDefineAuthorityCode()</a></td><td class="bar" id="b90"/><td class="ctr2" id="c90">0%</td><td class="bar" id="d90"/><td class="ctr2" id="e90">n/a</td><td class="ctr1" id="f90">1</td><td class="ctr2" id="g90">1</td><td class="ctr1" id="h90">1</td><td class="ctr2" id="i90">1</td><td class="ctr1" id="j90">1</td><td class="ctr2" id="k90">1</td></tr><tr><td id="a59"><a href="DwdbCertificateData.java.html#L35" class="el_method">getRelatedItemName()</a></td><td class="bar" id="b91"/><td class="ctr2" id="c91">0%</td><td class="bar" id="d91"/><td class="ctr2" id="e91">n/a</td><td class="ctr1" id="f91">1</td><td class="ctr2" id="g91">1</td><td class="ctr1" id="h91">1</td><td class="ctr2" id="i91">1</td><td class="ctr1" id="j91">1</td><td class="ctr2" id="k91">1</td></tr><tr><td id="a58"><a href="DwdbCertificateData.java.html#L38" class="el_method">getRelatedItemCode()</a></td><td class="bar" id="b92"/><td class="ctr2" id="c92">0%</td><td class="bar" id="d92"/><td class="ctr2" id="e92">n/a</td><td class="ctr1" id="f92">1</td><td class="ctr2" id="g92">1</td><td class="ctr1" id="h92">1</td><td class="ctr2" id="i92">1</td><td class="ctr1" id="j92">1</td><td class="ctr2" id="k92">1</td></tr><tr><td id="a20"><a href="DwdbCertificateData.java.html#L41" class="el_method">getCertificateHolderCategory()</a></td><td class="bar" id="b93"/><td class="ctr2" id="c93">0%</td><td class="bar" id="d93"/><td class="ctr2" id="e93">n/a</td><td class="ctr1" id="f93">1</td><td class="ctr2" id="g93">1</td><td class="ctr1" id="h93">1</td><td class="ctr2" id="i93">1</td><td class="ctr1" id="j93">1</td><td class="ctr2" id="k93">1</td></tr><tr><td id="a21"><a href="DwdbCertificateData.java.html#L44" class="el_method">getCertificateHolderCategoryName()</a></td><td class="bar" id="b94"/><td class="ctr2" id="c94">0%</td><td class="bar" id="d94"/><td class="ctr2" id="e94">n/a</td><td class="ctr1" id="f94">1</td><td class="ctr2" id="g94">1</td><td class="ctr1" id="h94">1</td><td class="ctr2" id="i94">1</td><td class="ctr1" id="j94">1</td><td class="ctr2" id="k94">1</td></tr><tr><td id="a82"><a href="DwdbCertificateData.java.html#L47" class="el_method">getValidityRange()</a></td><td class="bar" id="b95"/><td class="ctr2" id="c95">0%</td><td class="bar" id="d95"/><td class="ctr2" id="e95">n/a</td><td class="ctr1" id="f95">1</td><td class="ctr2" id="g95">1</td><td class="ctr1" id="h95">1</td><td class="ctr2" id="i95">1</td><td class="ctr1" id="j95">1</td><td class="ctr2" id="k95">1</td></tr><tr><td id="a26"><a href="DwdbCertificateData.java.html#L50" class="el_method">getCertificateIdentifier()</a></td><td class="bar" id="b96"/><td class="ctr2" id="c96">0%</td><td class="bar" id="d96"/><td class="ctr2" id="e96">n/a</td><td class="ctr1" id="f96">1</td><td class="ctr2" id="g96">1</td><td class="ctr1" id="h96">1</td><td class="ctr2" id="i96">1</td><td class="ctr1" id="j96">1</td><td class="ctr2" id="k96">1</td></tr><tr><td id="a31"><a href="DwdbCertificateData.java.html#L53" class="el_method">getCertificateName()</a></td><td class="bar" id="b97"/><td class="ctr2" id="c97">0%</td><td class="bar" id="d97"/><td class="ctr2" id="e97">n/a</td><td class="ctr1" id="f97">1</td><td class="ctr2" id="g97">1</td><td class="ctr1" id="h97">1</td><td class="ctr2" id="i97">1</td><td class="ctr1" id="j97">1</td><td class="ctr2" id="k97">1</td></tr><tr><td id="a32"><a href="DwdbCertificateData.java.html#L56" class="el_method">getCertificateNumber()</a></td><td class="bar" id="b98"/><td class="ctr2" id="c98">0%</td><td class="bar" id="d98"/><td class="ctr2" id="e98">n/a</td><td class="ctr1" id="f98">1</td><td class="ctr2" id="g98">1</td><td class="ctr1" id="h98">1</td><td class="ctr2" id="i98">1</td><td class="ctr1" id="j98">1</td><td class="ctr2" id="k98">1</td></tr><tr><td id="a30"><a href="DwdbCertificateData.java.html#L59" class="el_method">getCertificateIssuingAuthorityName()</a></td><td class="bar" id="b99"/><td class="ctr2" id="c99">0%</td><td class="bar" id="d99"/><td class="ctr2" id="e99">n/a</td><td class="ctr1" id="f99">1</td><td class="ctr2" id="g99">1</td><td class="ctr1" id="h99">1</td><td class="ctr2" id="i99">1</td><td class="ctr1" id="j99">1</td><td class="ctr2" id="k99">1</td></tr><tr><td id="a29"><a href="DwdbCertificateData.java.html#L62" class="el_method">getCertificateIssuingAuthorityCode()</a></td><td class="bar" id="b100"/><td class="ctr2" id="c100">0%</td><td class="bar" id="d100"/><td class="ctr2" id="e100">n/a</td><td class="ctr1" id="f100">1</td><td class="ctr2" id="g100">1</td><td class="ctr1" id="h100">1</td><td class="ctr2" id="i100">1</td><td class="ctr1" id="j100">1</td><td class="ctr2" id="k100">1</td></tr><tr><td id="a27"><a href="DwdbCertificateData.java.html#L65" class="el_method">getCertificateIssuedDate()</a></td><td class="bar" id="b101"/><td class="ctr2" id="c101">0%</td><td class="bar" id="d101"/><td class="ctr2" id="e101">n/a</td><td class="ctr1" id="f101">1</td><td class="ctr2" id="g101">1</td><td class="ctr1" id="h101">1</td><td class="ctr2" id="i101">1</td><td class="ctr1" id="j101">1</td><td class="ctr2" id="k101">1</td></tr><tr><td id="a23"><a href="DwdbCertificateData.java.html#L68" class="el_method">getCertificateHolderName()</a></td><td class="bar" id="b102"/><td class="ctr2" id="c102">0%</td><td class="bar" id="d102"/><td class="ctr2" id="e102">n/a</td><td class="ctr1" id="f102">1</td><td class="ctr2" id="g102">1</td><td class="ctr1" id="h102">1</td><td class="ctr2" id="i102">1</td><td class="ctr1" id="j102">1</td><td class="ctr2" id="k102">1</td></tr><tr><td id="a22"><a href="DwdbCertificateData.java.html#L71" class="el_method">getCertificateHolderCode()</a></td><td class="bar" id="b103"/><td class="ctr2" id="c103">0%</td><td class="bar" id="d103"/><td class="ctr2" id="e103">n/a</td><td class="ctr1" id="f103">1</td><td class="ctr2" id="g103">1</td><td class="ctr1" id="h103">1</td><td class="ctr2" id="i103">1</td><td class="ctr1" id="j103">1</td><td class="ctr2" id="k103">1</td></tr><tr><td id="a24"><a href="DwdbCertificateData.java.html#L74" class="el_method">getCertificateHolderTypeName()</a></td><td class="bar" id="b104"/><td class="ctr2" id="c104">0%</td><td class="bar" id="d104"/><td class="ctr2" id="e104">n/a</td><td class="ctr1" id="f104">1</td><td class="ctr2" id="g104">1</td><td class="ctr1" id="h104">1</td><td class="ctr2" id="i104">1</td><td class="ctr1" id="j104">1</td><td class="ctr2" id="k104">1</td></tr><tr><td id="a16"><a href="DwdbCertificateData.java.html#L77" class="el_method">getCertificateEffectiveDate()</a></td><td class="bar" id="b105"/><td class="ctr2" id="c105">0%</td><td class="bar" id="d105"/><td class="ctr2" id="e105">n/a</td><td class="ctr1" id="f105">1</td><td class="ctr2" id="g105">1</td><td class="ctr1" id="h105">1</td><td class="ctr2" id="i105">1</td><td class="ctr1" id="j105">1</td><td class="ctr2" id="k105">1</td></tr><tr><td id="a18"><a href="DwdbCertificateData.java.html#L80" class="el_method">getCertificateExpiringDate()</a></td><td class="bar" id="b106"/><td class="ctr2" id="c106">0%</td><td class="bar" id="d106"/><td class="ctr2" id="e106">n/a</td><td class="ctr1" id="f106">1</td><td class="ctr2" id="g106">1</td><td class="ctr1" id="h106">1</td><td class="ctr2" id="i106">1</td><td class="ctr1" id="j106">1</td><td class="ctr2" id="k106">1</td></tr><tr><td id="a49"><a href="DwdbCertificateData.java.html#L83" class="el_method">getIssueDeptCode2()</a></td><td class="bar" id="b107"/><td class="ctr2" id="c107">0%</td><td class="bar" id="d107"/><td class="ctr2" id="e107">n/a</td><td class="ctr1" id="f107">1</td><td class="ctr2" id="g107">1</td><td class="ctr1" id="h107">1</td><td class="ctr2" id="i107">1</td><td class="ctr1" id="j107">1</td><td class="ctr2" id="k107">1</td></tr><tr><td id="a50"><a href="DwdbCertificateData.java.html#L86" class="el_method">getIssueDeptCode3()</a></td><td class="bar" id="b108"/><td class="ctr2" id="c108">0%</td><td class="bar" id="d108"/><td class="ctr2" id="e108">n/a</td><td class="ctr1" id="f108">1</td><td class="ctr2" id="g108">1</td><td class="ctr1" id="h108">1</td><td class="ctr2" id="i108">1</td><td class="ctr1" id="j108">1</td><td class="ctr2" id="k108">1</td></tr><tr><td id="a13"><a href="DwdbCertificateData.java.html#L89" class="el_method">getCertificateAreaCode()</a></td><td class="bar" id="b109"/><td class="ctr2" id="c109">0%</td><td class="bar" id="d109"/><td class="ctr2" id="e109">n/a</td><td class="ctr1" id="f109">1</td><td class="ctr2" id="g109">1</td><td class="ctr1" id="h109">1</td><td class="ctr2" id="i109">1</td><td class="ctr1" id="j109">1</td><td class="ctr2" id="k109">1</td></tr><tr><td id="a68"><a href="DwdbCertificateData.java.html#L92" class="el_method">getSurfaceData()</a></td><td class="bar" id="b110"/><td class="ctr2" id="c110">0%</td><td class="bar" id="d110"/><td class="ctr2" id="e110">n/a</td><td class="ctr1" id="f110">1</td><td class="ctr2" id="g110">1</td><td class="ctr1" id="h110">1</td><td class="ctr2" id="i110">1</td><td class="ctr1" id="j110">1</td><td class="ctr2" id="k110">1</td></tr><tr><td id="a33"><a href="DwdbCertificateData.java.html#L95" class="el_method">getCertificateStatus()</a></td><td class="bar" id="b111"/><td class="ctr2" id="c111">0%</td><td class="bar" id="d111"/><td class="ctr2" id="e111">n/a</td><td class="ctr1" id="f111">1</td><td class="ctr2" id="g111">1</td><td class="ctr1" id="h111">1</td><td class="ctr2" id="i111">1</td><td class="ctr1" id="j111">1</td><td class="ctr2" id="k111">1</td></tr><tr><td id="a40"><a href="DwdbCertificateData.java.html#L98" class="el_method">getCreatorId()</a></td><td class="bar" id="b112"/><td class="ctr2" id="c112">0%</td><td class="bar" id="d112"/><td class="ctr2" id="e112">n/a</td><td class="ctr1" id="f112">1</td><td class="ctr2" id="g112">1</td><td class="ctr1" id="h112">1</td><td class="ctr2" id="i112">1</td><td class="ctr1" id="j112">1</td><td class="ctr2" id="k112">1</td></tr><tr><td id="a39"><a href="DwdbCertificateData.java.html#L101" class="el_method">getCreateTime()</a></td><td class="bar" id="b113"/><td class="ctr2" id="c113">0%</td><td class="bar" id="d113"/><td class="ctr2" id="e113">n/a</td><td class="ctr1" id="f113">1</td><td class="ctr2" id="g113">1</td><td class="ctr1" id="h113">1</td><td class="ctr2" id="i113">1</td><td class="ctr1" id="j113">1</td><td class="ctr2" id="k113">1</td></tr><tr><td id="a53"><a href="DwdbCertificateData.java.html#L104" class="el_method">getOperatorId()</a></td><td class="bar" id="b114"/><td class="ctr2" id="c114">0%</td><td class="bar" id="d114"/><td class="ctr2" id="e114">n/a</td><td class="ctr1" id="f114">1</td><td class="ctr2" id="g114">1</td><td class="ctr1" id="h114">1</td><td class="ctr2" id="i114">1</td><td class="ctr1" id="j114">1</td><td class="ctr2" id="k114">1</td></tr><tr><td id="a81"><a href="DwdbCertificateData.java.html#L107" class="el_method">getUpdateTime()</a></td><td class="bar" id="b115"/><td class="ctr2" id="c115">0%</td><td class="bar" id="d115"/><td class="ctr2" id="e115">n/a</td><td class="ctr1" id="f115">1</td><td class="ctr2" id="g115">1</td><td class="ctr1" id="h115">1</td><td class="ctr2" id="i115">1</td><td class="ctr1" id="j115">1</td><td class="ctr2" id="k115">1</td></tr><tr><td id="a48"><a href="DwdbCertificateData.java.html#L110" class="el_method">getFilePath()</a></td><td class="bar" id="b116"/><td class="ctr2" id="c116">0%</td><td class="bar" id="d116"/><td class="ctr2" id="e116">n/a</td><td class="ctr1" id="f116">1</td><td class="ctr2" id="g116">1</td><td class="ctr1" id="h116">1</td><td class="ctr2" id="i116">1</td><td class="ctr1" id="j116">1</td><td class="ctr2" id="k116">1</td></tr><tr><td id="a69"><a href="DwdbCertificateData.java.html#L113" class="el_method">getSyncStatus()</a></td><td class="bar" id="b117"/><td class="ctr2" id="c117">0%</td><td class="bar" id="d117"/><td class="ctr2" id="e117">n/a</td><td class="ctr1" id="f117">1</td><td class="ctr2" id="g117">1</td><td class="ctr1" id="h117">1</td><td class="ctr2" id="i117">1</td><td class="ctr1" id="j117">1</td><td class="ctr2" id="k117">1</td></tr><tr><td id="a60"><a href="DwdbCertificateData.java.html#L116" class="el_method">getRemarks()</a></td><td class="bar" id="b118"/><td class="ctr2" id="c118">0%</td><td class="bar" id="d118"/><td class="ctr2" id="e118">n/a</td><td class="ctr1" id="f118">1</td><td class="ctr2" id="g118">1</td><td class="ctr1" id="h118">1</td><td class="ctr2" id="i118">1</td><td class="ctr1" id="j118">1</td><td class="ctr2" id="k118">1</td></tr><tr><td id="a44"><a href="DwdbCertificateData.java.html#L119" class="el_method">getDeptId()</a></td><td class="bar" id="b119"/><td class="ctr2" id="c119">0%</td><td class="bar" id="d119"/><td class="ctr2" id="e119">n/a</td><td class="ctr1" id="f119">1</td><td class="ctr2" id="g119">1</td><td class="ctr1" id="h119">1</td><td class="ctr2" id="i119">1</td><td class="ctr1" id="j119">1</td><td class="ctr2" id="k119">1</td></tr><tr><td id="a8"><a href="DwdbCertificateData.java.html#L122" class="el_method">getApplyNum()</a></td><td class="bar" id="b120"/><td class="ctr2" id="c120">0%</td><td class="bar" id="d120"/><td class="ctr2" id="e120">n/a</td><td class="ctr1" id="f120">1</td><td class="ctr2" id="g120">1</td><td class="ctr1" id="h120">1</td><td class="ctr2" id="i120">1</td><td class="ctr1" id="j120">1</td><td class="ctr2" id="k120">1</td></tr><tr><td id="a5"><a href="DwdbCertificateData.java.html#L125" class="el_method">getAffairType()</a></td><td class="bar" id="b121"/><td class="ctr2" id="c121">0%</td><td class="bar" id="d121"/><td class="ctr2" id="e121">n/a</td><td class="ctr1" id="f121">1</td><td class="ctr2" id="g121">1</td><td class="ctr1" id="h121">1</td><td class="ctr2" id="i121">1</td><td class="ctr1" id="j121">1</td><td class="ctr2" id="k121">1</td></tr><tr><td id="a64"><a href="DwdbCertificateData.java.html#L128" class="el_method">getServeBusiness()</a></td><td class="bar" id="b122"/><td class="ctr2" id="c122">0%</td><td class="bar" id="d122"/><td class="ctr2" id="e122">n/a</td><td class="ctr1" id="f122">1</td><td class="ctr2" id="g122">1</td><td class="ctr1" id="h122">1</td><td class="ctr2" id="i122">1</td><td class="ctr1" id="j122">1</td><td class="ctr2" id="k122">1</td></tr><tr><td id="a3"><a href="DwdbCertificateData.java.html#L131" class="el_method">getAffairId()</a></td><td class="bar" id="b123"/><td class="ctr2" id="c123">0%</td><td class="bar" id="d123"/><td class="ctr2" id="e123">n/a</td><td class="ctr1" id="f123">1</td><td class="ctr2" id="g123">1</td><td class="ctr1" id="h123">1</td><td class="ctr2" id="i123">1</td><td class="ctr1" id="j123">1</td><td class="ctr2" id="k123">1</td></tr><tr><td id="a4"><a href="DwdbCertificateData.java.html#L134" class="el_method">getAffairNum()</a></td><td class="bar" id="b124"/><td class="ctr2" id="c124">0%</td><td class="bar" id="d124"/><td class="ctr2" id="e124">n/a</td><td class="ctr1" id="f124">1</td><td class="ctr2" id="g124">1</td><td class="ctr1" id="h124">1</td><td class="ctr2" id="i124">1</td><td class="ctr1" id="j124">1</td><td class="ctr2" id="k124">1</td></tr><tr><td id="a55"><a href="DwdbCertificateData.java.html#L137" class="el_method">getQzType()</a></td><td class="bar" id="b125"/><td class="ctr2" id="c125">0%</td><td class="bar" id="d125"/><td class="ctr2" id="e125">n/a</td><td class="ctr1" id="f125">1</td><td class="ctr2" id="g125">1</td><td class="ctr1" id="h125">1</td><td class="ctr2" id="i125">1</td><td class="ctr1" id="j125">1</td><td class="ctr2" id="k125">1</td></tr><tr><td id="a45"><a href="DwdbCertificateData.java.html#L140" class="el_method">getDraftUrl()</a></td><td class="bar" id="b126"/><td class="ctr2" id="c126">0%</td><td class="bar" id="d126"/><td class="ctr2" id="e126">n/a</td><td class="ctr1" id="f126">1</td><td class="ctr2" id="g126">1</td><td class="ctr1" id="h126">1</td><td class="ctr2" id="i126">1</td><td class="ctr1" id="j126">1</td><td class="ctr2" id="k126">1</td></tr><tr><td id="a66"><a href="DwdbCertificateData.java.html#L143" class="el_method">getSortName()</a></td><td class="bar" id="b127"/><td class="ctr2" id="c127">0%</td><td class="bar" id="d127"/><td class="ctr2" id="e127">n/a</td><td class="ctr1" id="f127">1</td><td class="ctr2" id="g127">1</td><td class="ctr1" id="h127">1</td><td class="ctr2" id="i127">1</td><td class="ctr1" id="j127">1</td><td class="ctr2" id="k127">1</td></tr><tr><td id="a63"><a href="DwdbCertificateData.java.html#L146" class="el_method">getSealname()</a></td><td class="bar" id="b128"/><td class="ctr2" id="c128">0%</td><td class="bar" id="d128"/><td class="ctr2" id="e128">n/a</td><td class="ctr1" id="f128">1</td><td class="ctr2" id="g128">1</td><td class="ctr1" id="h128">1</td><td class="ctr2" id="i128">1</td><td class="ctr1" id="j128">1</td><td class="ctr2" id="k128">1</td></tr><tr><td id="a67"><a href="DwdbCertificateData.java.html#L149" class="el_method">getSourceCode()</a></td><td class="bar" id="b129"/><td class="ctr2" id="c129">0%</td><td class="bar" id="d129"/><td class="ctr2" id="e129">n/a</td><td class="ctr1" id="f129">1</td><td class="ctr2" id="g129">1</td><td class="ctr1" id="h129">1</td><td class="ctr2" id="i129">1</td><td class="ctr1" id="j129">1</td><td class="ctr2" id="k129">1</td></tr><tr><td id="a56"><a href="DwdbCertificateData.java.html#L152" class="el_method">getRecCreateDate()</a></td><td class="bar" id="b130"/><td class="ctr2" id="c130">0%</td><td class="bar" id="d130"/><td class="ctr2" id="e130">n/a</td><td class="ctr1" id="f130">1</td><td class="ctr2" id="g130">1</td><td class="ctr1" id="h130">1</td><td class="ctr2" id="i130">1</td><td class="ctr1" id="j130">1</td><td class="ctr2" id="k130">1</td></tr><tr><td id="a57"><a href="DwdbCertificateData.java.html#L155" class="el_method">getRecModifyDate()</a></td><td class="bar" id="b131"/><td class="ctr2" id="c131">0%</td><td class="bar" id="d131"/><td class="ctr2" id="e131">n/a</td><td class="ctr1" id="f131">1</td><td class="ctr2" id="g131">1</td><td class="ctr1" id="h131">1</td><td class="ctr2" id="i131">1</td><td class="ctr1" id="j131">1</td><td class="ctr2" id="k131">1</td></tr><tr><td id="a51"><a href="DwdbCertificateData.java.html#L158" class="el_method">getMsaOrgCode()</a></td><td class="bar" id="b132"/><td class="ctr2" id="c132">0%</td><td class="bar" id="d132"/><td class="ctr2" id="e132">n/a</td><td class="ctr1" id="f132">1</td><td class="ctr2" id="g132">1</td><td class="ctr1" id="h132">1</td><td class="ctr2" id="i132">1</td><td class="ctr1" id="j132">1</td><td class="ctr2" id="k132">1</td></tr><tr><td id="a11"><a href="DwdbCertificateData.java.html#L161" class="el_method">getBirth()</a></td><td class="bar" id="b133"/><td class="ctr2" id="c133">0%</td><td class="bar" id="d133"/><td class="ctr2" id="e133">n/a</td><td class="ctr1" id="f133">1</td><td class="ctr2" id="g133">1</td><td class="ctr1" id="h133">1</td><td class="ctr2" id="i133">1</td><td class="ctr1" id="j133">1</td><td class="ctr2" id="k133">1</td></tr><tr><td id="a52"><a href="DwdbCertificateData.java.html#L164" class="el_method">getNameEn()</a></td><td class="bar" id="b134"/><td class="ctr2" id="c134">0%</td><td class="bar" id="d134"/><td class="ctr2" id="e134">n/a</td><td class="ctr1" id="f134">1</td><td class="ctr2" id="g134">1</td><td class="ctr1" id="h134">1</td><td class="ctr2" id="i134">1</td><td class="ctr1" id="j134">1</td><td class="ctr2" id="k134">1</td></tr><tr><td id="a37"><a href="DwdbCertificateData.java.html#L167" class="el_method">getCountryCn()</a></td><td class="bar" id="b135"/><td class="ctr2" id="c135">0%</td><td class="bar" id="d135"/><td class="ctr2" id="e135">n/a</td><td class="ctr1" id="f135">1</td><td class="ctr2" id="g135">1</td><td class="ctr1" id="h135">1</td><td class="ctr2" id="i135">1</td><td class="ctr1" id="j135">1</td><td class="ctr2" id="k135">1</td></tr><tr><td id="a38"><a href="DwdbCertificateData.java.html#L170" class="el_method">getCountryEn()</a></td><td class="bar" id="b136"/><td class="ctr2" id="c136">0%</td><td class="bar" id="d136"/><td class="ctr2" id="e136">n/a</td><td class="ctr1" id="f136">1</td><td class="ctr2" id="g136">1</td><td class="ctr1" id="h136">1</td><td class="ctr2" id="i136">1</td><td class="ctr1" id="j136">1</td><td class="ctr2" id="k136">1</td></tr><tr><td id="a65"><a href="DwdbCertificateData.java.html#L173" class="el_method">getSignDeptEn()</a></td><td class="bar" id="b137"/><td class="ctr2" id="c137">0%</td><td class="bar" id="d137"/><td class="ctr2" id="e137">n/a</td><td class="ctr1" id="f137">1</td><td class="ctr2" id="g137">1</td><td class="ctr1" id="h137">1</td><td class="ctr2" id="i137">1</td><td class="ctr1" id="j137">1</td><td class="ctr2" id="k137">1</td></tr><tr><td id="a6"><a href="DwdbCertificateData.java.html#L176" class="el_method">getApplivationsCn()</a></td><td class="bar" id="b138"/><td class="ctr2" id="c138">0%</td><td class="bar" id="d138"/><td class="ctr2" id="e138">n/a</td><td class="ctr1" id="f138">1</td><td class="ctr2" id="g138">1</td><td class="ctr1" id="h138">1</td><td class="ctr2" id="i138">1</td><td class="ctr1" id="j138">1</td><td class="ctr2" id="k138">1</td></tr><tr><td id="a41"><a href="DwdbCertificateData.java.html#L179" class="el_method">getCrewType()</a></td><td class="bar" id="b139"/><td class="ctr2" id="c139">0%</td><td class="bar" id="d139"/><td class="ctr2" id="e139">n/a</td><td class="ctr1" id="f139">1</td><td class="ctr2" id="g139">1</td><td class="ctr1" id="h139">1</td><td class="ctr2" id="i139">1</td><td class="ctr1" id="j139">1</td><td class="ctr2" id="k139">1</td></tr><tr><td id="a54"><a href="DwdbCertificateData.java.html#L182" class="el_method">getQualificationCn()</a></td><td class="bar" id="b140"/><td class="ctr2" id="c140">0%</td><td class="bar" id="d140"/><td class="ctr2" id="e140">n/a</td><td class="ctr1" id="f140">1</td><td class="ctr2" id="g140">1</td><td class="ctr1" id="h140">1</td><td class="ctr2" id="i140">1</td><td class="ctr1" id="j140">1</td><td class="ctr2" id="k140">1</td></tr><tr><td id="a12"><a href="DwdbCertificateData.java.html#L185" class="el_method">getBirthEn()</a></td><td class="bar" id="b141"/><td class="ctr2" id="c141">0%</td><td class="bar" id="d141"/><td class="ctr2" id="e141">n/a</td><td class="ctr1" id="f141">1</td><td class="ctr2" id="g141">1</td><td class="ctr1" id="h141">1</td><td class="ctr2" id="i141">1</td><td class="ctr1" id="j141">1</td><td class="ctr2" id="k141">1</td></tr><tr><td id="a36"><a href="DwdbCertificateData.java.html#L188" class="el_method">getCertPrintNo()</a></td><td class="bar" id="b142"/><td class="ctr2" id="c142">0%</td><td class="bar" id="d142"/><td class="ctr2" id="e142">n/a</td><td class="ctr1" id="f142">1</td><td class="ctr2" id="g142">1</td><td class="ctr1" id="h142">1</td><td class="ctr2" id="i142">1</td><td class="ctr1" id="j142">1</td><td class="ctr2" id="k142">1</td></tr><tr><td id="a17"><a href="DwdbCertificateData.java.html#L191" class="el_method">getCertificateEffectiveDateEn()</a></td><td class="bar" id="b143"/><td class="ctr2" id="c143">0%</td><td class="bar" id="d143"/><td class="ctr2" id="e143">n/a</td><td class="ctr1" id="f143">1</td><td class="ctr2" id="g143">1</td><td class="ctr1" id="h143">1</td><td class="ctr2" id="i143">1</td><td class="ctr1" id="j143">1</td><td class="ctr2" id="k143">1</td></tr><tr><td id="a19"><a href="DwdbCertificateData.java.html#L194" class="el_method">getCertificateExpiringDateEn()</a></td><td class="bar" id="b144"/><td class="ctr2" id="c144">0%</td><td class="bar" id="d144"/><td class="ctr2" id="e144">n/a</td><td class="ctr1" id="f144">1</td><td class="ctr2" id="g144">1</td><td class="ctr1" id="h144">1</td><td class="ctr2" id="i144">1</td><td class="ctr1" id="j144">1</td><td class="ctr2" id="k144">1</td></tr><tr><td id="a28"><a href="DwdbCertificateData.java.html#L197" class="el_method">getCertificateIssuedDateEn()</a></td><td class="bar" id="b145"/><td class="ctr2" id="c145">0%</td><td class="bar" id="d145"/><td class="ctr2" id="e145">n/a</td><td class="ctr1" id="f145">1</td><td class="ctr2" id="g145">1</td><td class="ctr1" id="h145">1</td><td class="ctr2" id="i145">1</td><td class="ctr1" id="j145">1</td><td class="ctr2" id="k145">1</td></tr><tr><td id="a42"><a href="DwdbCertificateData.java.html#L200" class="el_method">getCrewTypeEn()</a></td><td class="bar" id="b146"/><td class="ctr2" id="c146">0%</td><td class="bar" id="d146"/><td class="ctr2" id="e146">n/a</td><td class="ctr1" id="f146">1</td><td class="ctr2" id="g146">1</td><td class="ctr1" id="h146">1</td><td class="ctr2" id="i146">1</td><td class="ctr1" id="j146">1</td><td class="ctr2" id="k146">1</td></tr><tr><td id="a7"><a href="DwdbCertificateData.java.html#L203" class="el_method">getApplivationsEn()</a></td><td class="bar" id="b147"/><td class="ctr2" id="c147">0%</td><td class="bar" id="d147"/><td class="ctr2" id="e147">n/a</td><td class="ctr1" id="f147">1</td><td class="ctr2" id="g147">1</td><td class="ctr1" id="h147">1</td><td class="ctr2" id="i147">1</td><td class="ctr1" id="j147">1</td><td class="ctr2" id="k147">1</td></tr><tr><td id="a9"><a href="DwdbCertificateData.java.html#L206" class="el_method">getAuthAuthorityCn()</a></td><td class="bar" id="b148"/><td class="ctr2" id="c148">0%</td><td class="bar" id="d148"/><td class="ctr2" id="e148">n/a</td><td class="ctr1" id="f148">1</td><td class="ctr2" id="g148">1</td><td class="ctr1" id="h148">1</td><td class="ctr2" id="i148">1</td><td class="ctr1" id="j148">1</td><td class="ctr2" id="k148">1</td></tr><tr><td id="a10"><a href="DwdbCertificateData.java.html#L209" class="el_method">getAuthAuthorityEn()</a></td><td class="bar" id="b149"/><td class="ctr2" id="c149">0%</td><td class="bar" id="d149"/><td class="ctr2" id="e149">n/a</td><td class="ctr1" id="f149">1</td><td class="ctr2" id="g149">1</td><td class="ctr1" id="h149">1</td><td class="ctr2" id="i149">1</td><td class="ctr1" id="j149">1</td><td class="ctr2" id="k149">1</td></tr><tr><td id="a46"><a href="DwdbCertificateData.java.html#L212" class="el_method">getEvaOrgCn()</a></td><td class="bar" id="b150"/><td class="ctr2" id="c150">0%</td><td class="bar" id="d150"/><td class="ctr2" id="e150">n/a</td><td class="ctr1" id="f150">1</td><td class="ctr2" id="g150">1</td><td class="ctr1" id="h150">1</td><td class="ctr2" id="i150">1</td><td class="ctr1" id="j150">1</td><td class="ctr2" id="k150">1</td></tr><tr><td id="a47"><a href="DwdbCertificateData.java.html#L215" class="el_method">getEvaOrgEn()</a></td><td class="bar" id="b151"/><td class="ctr2" id="c151">0%</td><td class="bar" id="d151"/><td class="ctr2" id="e151">n/a</td><td class="ctr1" id="f151">1</td><td class="ctr2" id="g151">1</td><td class="ctr1" id="h151">1</td><td class="ctr2" id="i151">1</td><td class="ctr1" id="j151">1</td><td class="ctr2" id="k151">1</td></tr><tr><td id="a79"><a href="DwdbCertificateData.java.html#L218" class="el_method">getTrainManagerNameCn()</a></td><td class="bar" id="b152"/><td class="ctr2" id="c152">0%</td><td class="bar" id="d152"/><td class="ctr2" id="e152">n/a</td><td class="ctr1" id="f152">1</td><td class="ctr2" id="g152">1</td><td class="ctr1" id="h152">1</td><td class="ctr2" id="i152">1</td><td class="ctr1" id="j152">1</td><td class="ctr2" id="k152">1</td></tr><tr><td id="a80"><a href="DwdbCertificateData.java.html#L221" class="el_method">getTrainManagerNameEn()</a></td><td class="bar" id="b153"/><td class="ctr2" id="c153">0%</td><td class="bar" id="d153"/><td class="ctr2" id="e153">n/a</td><td class="ctr1" id="f153">1</td><td class="ctr2" id="g153">1</td><td class="ctr1" id="h153">1</td><td class="ctr2" id="i153">1</td><td class="ctr1" id="j153">1</td><td class="ctr2" id="k153">1</td></tr><tr><td id="a61"><a href="DwdbCertificateData.java.html#L224" class="el_method">getRepresentativeCn()</a></td><td class="bar" id="b154"/><td class="ctr2" id="c154">0%</td><td class="bar" id="d154"/><td class="ctr2" id="e154">n/a</td><td class="ctr1" id="f154">1</td><td class="ctr2" id="g154">1</td><td class="ctr1" id="h154">1</td><td class="ctr2" id="i154">1</td><td class="ctr1" id="j154">1</td><td class="ctr2" id="k154">1</td></tr><tr><td id="a62"><a href="DwdbCertificateData.java.html#L227" class="el_method">getRepresentativeEn()</a></td><td class="bar" id="b155"/><td class="ctr2" id="c155">0%</td><td class="bar" id="d155"/><td class="ctr2" id="e155">n/a</td><td class="ctr1" id="f155">1</td><td class="ctr2" id="g155">1</td><td class="ctr1" id="h155">1</td><td class="ctr2" id="i155">1</td><td class="ctr1" id="j155">1</td><td class="ctr2" id="k155">1</td></tr><tr><td id="a77"><a href="DwdbCertificateData.java.html#L230" class="el_method">getTrainingNamesCn()</a></td><td class="bar" id="b156"/><td class="ctr2" id="c156">0%</td><td class="bar" id="d156"/><td class="ctr2" id="e156">n/a</td><td class="ctr1" id="f156">1</td><td class="ctr2" id="g156">1</td><td class="ctr1" id="h156">1</td><td class="ctr2" id="i156">1</td><td class="ctr1" id="j156">1</td><td class="ctr2" id="k156">1</td></tr><tr><td id="a78"><a href="DwdbCertificateData.java.html#L233" class="el_method">getTrainingNamesEn()</a></td><td class="bar" id="b157"/><td class="ctr2" id="c157">0%</td><td class="bar" id="d157"/><td class="ctr2" id="e157">n/a</td><td class="ctr1" id="f157">1</td><td class="ctr2" id="g157">1</td><td class="ctr1" id="h157">1</td><td class="ctr2" id="i157">1</td><td class="ctr1" id="j157">1</td><td class="ctr2" id="k157">1</td></tr><tr><td id="a74"><a href="DwdbCertificateData.java.html#L236" class="el_method">getTrainingIssueDatesCn()</a></td><td class="bar" id="b158"/><td class="ctr2" id="c158">0%</td><td class="bar" id="d158"/><td class="ctr2" id="e158">n/a</td><td class="ctr1" id="f158">1</td><td class="ctr2" id="g158">1</td><td class="ctr1" id="h158">1</td><td class="ctr2" id="i158">1</td><td class="ctr1" id="j158">1</td><td class="ctr2" id="k158">1</td></tr><tr><td id="a75"><a href="DwdbCertificateData.java.html#L239" class="el_method">getTrainingIssueDatesEn()</a></td><td class="bar" id="b159"/><td class="ctr2" id="c159">0%</td><td class="bar" id="d159"/><td class="ctr2" id="e159">n/a</td><td class="ctr1" id="f159">1</td><td class="ctr2" id="g159">1</td><td class="ctr1" id="h159">1</td><td class="ctr2" id="i159">1</td><td class="ctr1" id="j159">1</td><td class="ctr2" id="k159">1</td></tr><tr><td id="a71"><a href="DwdbCertificateData.java.html#L242" class="el_method">getTrainingEffectiveDatesCn()</a></td><td class="bar" id="b160"/><td class="ctr2" id="c160">0%</td><td class="bar" id="d160"/><td class="ctr2" id="e160">n/a</td><td class="ctr1" id="f160">1</td><td class="ctr2" id="g160">1</td><td class="ctr1" id="h160">1</td><td class="ctr2" id="i160">1</td><td class="ctr1" id="j160">1</td><td class="ctr2" id="k160">1</td></tr><tr><td id="a72"><a href="DwdbCertificateData.java.html#L245" class="el_method">getTrainingEffectiveDatesEn()</a></td><td class="bar" id="b161"/><td class="ctr2" id="c161">0%</td><td class="bar" id="d161"/><td class="ctr2" id="e161">n/a</td><td class="ctr1" id="f161">1</td><td class="ctr2" id="g161">1</td><td class="ctr1" id="h161">1</td><td class="ctr2" id="i161">1</td><td class="ctr1" id="j161">1</td><td class="ctr2" id="k161">1</td></tr><tr><td id="a73"><a href="DwdbCertificateData.java.html#L248" class="el_method">getTrainingInstitutionCode()</a></td><td class="bar" id="b162"/><td class="ctr2" id="c162">0%</td><td class="bar" id="d162"/><td class="ctr2" id="e162">n/a</td><td class="ctr1" id="f162">1</td><td class="ctr2" id="g162">1</td><td class="ctr1" id="h162">1</td><td class="ctr2" id="i162">1</td><td class="ctr1" id="j162">1</td><td class="ctr2" id="k162">1</td></tr><tr><td id="a76"><a href="DwdbCertificateData.java.html#L251" class="el_method">getTrainingLocation()</a></td><td class="bar" id="b163"/><td class="ctr2" id="c163">0%</td><td class="bar" id="d163"/><td class="ctr2" id="e163">n/a</td><td class="ctr1" id="f163">1</td><td class="ctr2" id="g163">1</td><td class="ctr1" id="h163">1</td><td class="ctr2" id="i163">1</td><td class="ctr1" id="j163">1</td><td class="ctr2" id="k163">1</td></tr><tr><td id="a0"><a href="DwdbCertificateData.java.html#L8" class="el_method">canEqual(Object)</a></td><td class="bar" id="b164"/><td class="ctr2" id="c164">0%</td><td class="bar" id="d164"/><td class="ctr2" id="e164">n/a</td><td class="ctr1" id="f164">1</td><td class="ctr2" id="g164">1</td><td class="ctr1" id="h164">1</td><td class="ctr2" id="i164">1</td><td class="ctr1" id="j164">1</td><td class="ctr2" id="k164">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>