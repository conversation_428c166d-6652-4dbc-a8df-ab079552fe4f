<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCertificateData.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCertificateData.java</span></div><h1>DwdbCertificateData.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import java.util.Date;

<span class="nc bnc" id="L8" title="All 646 branches missed.">@Data</span>
@TableName(&quot;dwdb_certificate_data&quot;)
//标准库电子证照信息表
public class DwdbCertificateData {
    //证照数据主键
    @TableId
<span class="nc" id="L14">    private String dataId;</span>
    
    //电子证照业务标识码
<span class="nc" id="L17">    private String certificateId;</span>
    
    //证照模板ID
<span class="nc" id="L20">    private String templateId;</span>
    
    //证照类型名称
<span class="nc" id="L23">    private String certificateTypeName;</span>
    
    //证照类型代码，21位编码
<span class="nc" id="L26">    private String certificateTypeCode;</span>
    
    //证照定义机构
<span class="nc" id="L29">    private String certificateDefineAuthorityName;</span>
    
    //证照定义机构代码（统一社会信用代码）
<span class="nc" id="L32">    private String certificateDefineAuthorityCode;</span>
    
    //关联事项名称
<span class="nc" id="L35">    private String relatedItemName;</span>
    
    //关联事项编码
<span class="nc" id="L38">    private String relatedItemCode;</span>
    
    //持证主体类别：1-自然人 2-法人或其他组织 3-混合 4-其他
<span class="nc" id="L41">    private String certificateHolderCategory;</span>
    
    //持证主体类别名称：自然人/法人或其他组织/混合/其他
<span class="nc" id="L44">    private String certificateHolderCategoryName;</span>
    
    //有效期限范围，多个用^分隔不同期限
<span class="nc" id="L47">    private String validityRange;</span>
    
    //电子证照唯一标识码
<span class="nc" id="L50">    private String certificateIdentifier;</span>
    
    //证照名称
<span class="nc" id="L53">    private String certificateName;</span>
    
    //证照编号
<span class="nc" id="L56">    private String certificateNumber;</span>
    
    //证照颁发机构
<span class="nc" id="L59">    private String certificateIssuingAuthorityName;</span>
    
    //证照颁发机构代码（统一社会信用代码）
<span class="nc" id="L62">    private String certificateIssuingAuthorityCode;</span>
    
    //证照颁发日期（yyyy-mm-dd）
<span class="nc" id="L65">    private String certificateIssuedDate;</span>
    
    //持证主体名称（自然人姓名/法人全称）
<span class="nc" id="L68">    private String certificateHolderName;</span>
    
    //持证主体代码（信用代码/身份证号等）
<span class="nc" id="L71">    private String certificateHolderCode;</span>
    
    //持证主体代码类型：统一社会信用代码/公民身份号码/护照号/其他
<span class="nc" id="L74">    private String certificateHolderTypeName;</span>
    
    //证照有效期开始日期（yyyy-mm-dd）
<span class="nc" id="L77">    private String certificateEffectiveDate;</span>
    
    //证照有效期截止日期（yyyy-mm-dd或长期）
<span class="nc" id="L80">    private String certificateExpiringDate;</span>
    
    //证照颁发机构代码（海事内部统一编码2.0版本）
<span class="nc" id="L83">    private String issueDeptCode2;</span>
    
    //证照颁发机构代码（海事内部统一编码3.0版本）
<span class="nc" id="L86">    private String issueDeptCode3;</span>
    
    //证照所属地区编码
<span class="nc" id="L89">    private String certificateAreaCode;</span>
    
    //照面拓展信息
<span class="nc" id="L92">    private String surfaceData;</span>
    
    //证照状态（-5 异常,-4 撤回,-3 撤销,-2 注销,-1 作废,0 首次生成,1 生成已同步,2 修改未同步,3 过期,4 修改已同步,5 预览）
<span class="nc" id="L95">    private String certificateStatus;</span>
    
    //登记人员ID
<span class="nc" id="L98">    private String creatorId;</span>
    
    //创建时间
<span class="nc" id="L101">    private String createTime;</span>
    
    //最后操作人
<span class="nc" id="L104">    private String operatorId;</span>
    
    //修改时间
<span class="nc" id="L107">    private String updateTime;</span>
    
    //文件保存位置
<span class="nc" id="L110">    private String filePath;</span>
    
    //同步状态（0-新增未上传,1-已上传，2-更新未上传，3-更新已上传,4-删除未上传,5-删除未上传）
<span class="nc" id="L113">    private String syncStatus;</span>
    
    //备注
<span class="nc" id="L116">    private String remarks;</span>
    
    //登记部门
<span class="nc" id="L119">    private String deptId;</span>
    
    //申请编号
<span class="nc" id="L122">    private String applyNum;</span>
    
    //事项类型
<span class="nc" id="L125">    private String affairType;</span>
    
    //服务对象
<span class="nc" id="L128">    private String serveBusiness;</span>
    
    //事项ID
<span class="nc" id="L131">    private String affairId;</span>
    
    //事项编号
<span class="nc" id="L134">    private String affairNum;</span>
    
    //签章类型
<span class="nc" id="L137">    private String qzType;</span>
    
    //加签文件备用地址
<span class="nc" id="L140">    private String draftUrl;</span>
    
    //归档编号
<span class="nc" id="L143">    private String sortName;</span>
    
    //印章名称
<span class="nc" id="L146">    private String sealname;</span>
    
    //源系统代码
<span class="nc" id="L149">    private String sourceCode;</span>
    
    //记录创建日期
<span class="nc" id="L152">    private Date recCreateDate;</span>
    
    //记录修改日期
<span class="nc" id="L155">    private Date recModifyDate;</span>
    
    //数据归属机构代码
<span class="nc" id="L158">    private String msaOrgCode;</span>
    
    //出生年月日-yyyymmdd
<span class="nc" id="L161">    private String birth;</span>
    
    //持证人姓名-英文
<span class="nc" id="L164">    private String nameEn;</span>
    
    //国籍-中文
<span class="nc" id="L167">    private String countryCn;</span>
    
    //国籍-英文
<span class="nc" id="L170">    private String countryEn;</span>
    
    //发证机关(英文）
<span class="nc" id="L173">    private String signDeptEn;</span>
    
    //适用航线-中文
<span class="nc" id="L176">    private String applivationsCn;</span>
    
    //船员类型
<span class="nc" id="L179">    private String crewType;</span>

    //游艇驾驶证资格
<span class="nc" id="L182">    private String qualificationCn;</span>

    //出生日期-英文
<span class="nc" id="L185">    private String birthEn;</span>

    //证书印刷号
<span class="nc" id="L188">    private String certPrintNo;</span>

    //证照有效期开始日期（英文）
<span class="nc" id="L191">    private String certificateEffectiveDateEn;</span>
    
    //证照有效期截止日期（英文）
<span class="nc" id="L194">    private String certificateExpiringDateEn;</span>
    
    //证照颁发日期（英文）
<span class="nc" id="L197">    private String certificateIssuedDateEn;</span>
    
    //职务(英文)
<span class="nc" id="L200">    private String crewTypeEn;</span>
    
    //适用航线-英文
<span class="nc" id="L203">    private String applivationsEn;</span>
    
    //授权机关（中文）
<span class="nc" id="L206">    private String authAuthorityCn;</span>
    
    //授权机关（英文）
<span class="nc" id="L209">    private String authAuthorityEn;</span>
    
    //审核机构（中文）
<span class="nc" id="L212">    private String evaOrgCn;</span>
    
    //审核机构（英文）
<span class="nc" id="L215">    private String evaOrgEn;</span>
    
    //培训主管姓名（中文）
<span class="nc" id="L218">    private String trainManagerNameCn;</span>
    
    //培训主管姓名（英文）
<span class="nc" id="L221">    private String trainManagerNameEn;</span>
    
    //法定代表人姓名（中文）
<span class="nc" id="L224">    private String representativeCn;</span>
    
    //法定代表人姓名（英文）
<span class="nc" id="L227">    private String representativeEn;</span>
    
    //培训项目（中文）
<span class="nc" id="L230">    private String trainingNamesCn;</span>
    
    //培训项目（英文）
<span class="nc" id="L233">    private String trainingNamesEn;</span>
    
    //培训项目签发日期（中文）多个项目用#隔开
<span class="nc" id="L236">    private String trainingIssueDatesCn;</span>
    
    //培训项目签发日期（英文）多个项目用#隔开
<span class="nc" id="L239">    private String trainingIssueDatesEn;</span>
    
    //培训项目有效期至（中文）多个项目用#隔开
<span class="nc" id="L242">    private String trainingEffectiveDatesCn;</span>
    
    //培训项目有效期至（英文）多个项目用#隔开    
<span class="nc" id="L245">    private String trainingEffectiveDatesEn;</span>
    
    //机构代码
<span class="nc" id="L248">    private String trainingInstitutionCode;</span>
    
    //培训地点
<span class="nc" id="L251">    private String trainingLocation;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>