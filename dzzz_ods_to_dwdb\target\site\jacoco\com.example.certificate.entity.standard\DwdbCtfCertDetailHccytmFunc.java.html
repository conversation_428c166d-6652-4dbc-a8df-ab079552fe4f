<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertDetailHccytmFunc.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertDetailHccytmFunc.java</span></div><h1>DwdbCtfCertDetailHccytmFunc.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员特免证明职能信息表实体类
 */
<span class="nc bnc" id="L9" title="All 86 branches missed.">@Data</span>
public class DwdbCtfCertDetailHccytmFunc {
    /**
     * 主键
     */
<span class="nc" id="L14">    private String hccytmFunctionId;</span>

    /**
     * 证书主表ID
     */
<span class="nc" id="L19">    private String hccytmId;</span>

    /**
     * 职能(中文)
     */
<span class="nc" id="L24">    private String function1;</span>

    /**
     * 职能(英文)
     */
<span class="nc" id="L29">    private String function2;</span>

    /**
     * 等级(中文)
     */
<span class="nc" id="L34">    private String level1;</span>

    /**
     * 等级(英文)
     */
<span class="nc" id="L39">    private String level2;</span>

    /**
     * 限制(中文)
     */
<span class="nc" id="L44">    private String limitationsApplying1;</span>

    /**
     * 限制(英文)
     */
<span class="nc" id="L49">    private String limitationsApplying2;</span>

    /**
     * 创建时间
     */
<span class="nc" id="L54">    private Date createTime;</span>

    /**
     * 更新时间
     */
<span class="nc" id="L59">    private Date updateTime;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>