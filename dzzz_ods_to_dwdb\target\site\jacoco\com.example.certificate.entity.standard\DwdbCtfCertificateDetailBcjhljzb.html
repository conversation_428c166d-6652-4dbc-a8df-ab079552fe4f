<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailBcjhljzb</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_class">DwdbCtfCertificateDetailBcjhljzb</span></div><h1>DwdbCtfCertificateDetailBcjhljzb</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,459 of 1,459</td><td class="ctr2">0%</td><td class="bar">270 of 270</td><td class="ctr2">0%</td><td class="ctr1">206</td><td class="ctr2">206</td><td class="ctr1">34</td><td class="ctr2">34</td><td class="ctr1">71</td><td class="ctr2">71</td></tr></tfoot><tbody><tr><td id="a2"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="582" alt="582"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="204" alt="204"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">103</td><td class="ctr2" id="g0">103</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a36"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="468" alt="468"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="66" alt="66"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">34</td><td class="ctr2" id="g1">34</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a70"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">toString()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="172" alt="172"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a41"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setBcjhljzbId(String)</a></td><td class="bar" id="b3"/><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a53"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setDataId(String)</a></td><td class="bar" id="b4"/><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a56"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setFullNameoftheHolder1(String)</a></td><td class="bar" id="b5"/><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a57"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setFullNameoftheHolder2(String)</a></td><td class="bar" id="b6"/><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a65"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setNationality1(String)</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a66"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setNationality2(String)</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a54"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setDateOfBirth1(String)</a></td><td class="bar" id="b9"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a55"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setDateOfBirth2(String)</a></td><td class="bar" id="b10"/><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a58"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setGender1(String)</a></td><td class="bar" id="b11"/><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a59"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setGender2(String)</a></td><td class="bar" id="b12"/><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a51"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateNo(String)</a></td><td class="bar" id="b13"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a46"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateExpiringDate1(String)</a></td><td class="bar" id="b14"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a47"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateExpiringDate2(String)</a></td><td class="bar" id="b15"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a49"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateIssuedDate1(String)</a></td><td class="bar" id="b16"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a50"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateIssuedDate2(String)</a></td><td class="bar" id="b17"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a48"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCertificateHolderName(String)</a></td><td class="bar" id="b18"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a60"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setInformationOfPhoto(String)</a></td><td class="bar" id="b19"/><td class="ctr2" id="c19">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a42"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCapacity1(String)</a></td><td class="bar" id="b20"/><td class="ctr2" id="c20">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a43"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCapacity2(String)</a></td><td class="bar" id="b21"/><td class="ctr2" id="c21">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a44"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCapacity3(String)</a></td><td class="bar" id="b22"/><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a45"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCapacity4(String)</a></td><td class="bar" id="b23"/><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a37"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setApplivations1(String)</a></td><td class="bar" id="b24"/><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a38"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setApplivations2(String)</a></td><td class="bar" id="b25"/><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a39"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setApplivations3(String)</a></td><td class="bar" id="b26"/><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a40"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setApplivations4(String)</a></td><td class="bar" id="b27"/><td class="ctr2" id="c27">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a63"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setNameOfDulyAuthorizedOfficial1(String)</a></td><td class="bar" id="b28"/><td class="ctr2" id="c28">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a64"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setNameOfDulyAuthorizedOfficial2(String)</a></td><td class="bar" id="b29"/><td class="ctr2" id="c29">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a61"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setIssuingAuthority1(String)</a></td><td class="bar" id="b30"/><td class="ctr2" id="c30">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a62"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setIssuingAuthority2(String)</a></td><td class="bar" id="b31"/><td class="ctr2" id="c31">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a67"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setOfficialUseOnly1(String)</a></td><td class="bar" id="b32"/><td class="ctr2" id="c32">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a68"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setOfficialUseOnly2(String)</a></td><td class="bar" id="b33"/><td class="ctr2" id="c33">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a52"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setCreateTime(Date)</a></td><td class="bar" id="b34"/><td class="ctr2" id="c34">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a69"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">setUpdateTime(Date)</a></td><td class="bar" id="b35"/><td class="ctr2" id="c35">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a1"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">DwdbCtfCertificateDetailBcjhljzb()</a></td><td class="bar" id="b36"/><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a7"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L11" class="el_method">getBcjhljzbId()</a></td><td class="bar" id="b37"/><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a19"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L12" class="el_method">getDataId()</a></td><td class="bar" id="b38"/><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a22"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L13" class="el_method">getFullNameoftheHolder1()</a></td><td class="bar" id="b39"/><td class="ctr2" id="c39">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a23"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L14" class="el_method">getFullNameoftheHolder2()</a></td><td class="bar" id="b40"/><td class="ctr2" id="c40">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a31"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L15" class="el_method">getNationality1()</a></td><td class="bar" id="b41"/><td class="ctr2" id="c41">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a32"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L16" class="el_method">getNationality2()</a></td><td class="bar" id="b42"/><td class="ctr2" id="c42">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a20"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L17" class="el_method">getDateOfBirth1()</a></td><td class="bar" id="b43"/><td class="ctr2" id="c43">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a21"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L18" class="el_method">getDateOfBirth2()</a></td><td class="bar" id="b44"/><td class="ctr2" id="c44">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a24"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L19" class="el_method">getGender1()</a></td><td class="bar" id="b45"/><td class="ctr2" id="c45">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a25"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L20" class="el_method">getGender2()</a></td><td class="bar" id="b46"/><td class="ctr2" id="c46">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a17"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L21" class="el_method">getCertificateNo()</a></td><td class="bar" id="b47"/><td class="ctr2" id="c47">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a12"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L22" class="el_method">getCertificateExpiringDate1()</a></td><td class="bar" id="b48"/><td class="ctr2" id="c48">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a13"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L23" class="el_method">getCertificateExpiringDate2()</a></td><td class="bar" id="b49"/><td class="ctr2" id="c49">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a15"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L24" class="el_method">getCertificateIssuedDate1()</a></td><td class="bar" id="b50"/><td class="ctr2" id="c50">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a16"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L25" class="el_method">getCertificateIssuedDate2()</a></td><td class="bar" id="b51"/><td class="ctr2" id="c51">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a14"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L26" class="el_method">getCertificateHolderName()</a></td><td class="bar" id="b52"/><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a26"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L27" class="el_method">getInformationOfPhoto()</a></td><td class="bar" id="b53"/><td class="ctr2" id="c53">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a8"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L28" class="el_method">getCapacity1()</a></td><td class="bar" id="b54"/><td class="ctr2" id="c54">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a9"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L29" class="el_method">getCapacity2()</a></td><td class="bar" id="b55"/><td class="ctr2" id="c55">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a10"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L30" class="el_method">getCapacity3()</a></td><td class="bar" id="b56"/><td class="ctr2" id="c56">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a11"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L31" class="el_method">getCapacity4()</a></td><td class="bar" id="b57"/><td class="ctr2" id="c57">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a3"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L32" class="el_method">getApplivations1()</a></td><td class="bar" id="b58"/><td class="ctr2" id="c58">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a4"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L33" class="el_method">getApplivations2()</a></td><td class="bar" id="b59"/><td class="ctr2" id="c59">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a5"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L34" class="el_method">getApplivations3()</a></td><td class="bar" id="b60"/><td class="ctr2" id="c60">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h60">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a6"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L35" class="el_method">getApplivations4()</a></td><td class="bar" id="b61"/><td class="ctr2" id="c61">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h61">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a29"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L36" class="el_method">getNameOfDulyAuthorizedOfficial1()</a></td><td class="bar" id="b62"/><td class="ctr2" id="c62">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h62">1</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a30"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L37" class="el_method">getNameOfDulyAuthorizedOfficial2()</a></td><td class="bar" id="b63"/><td class="ctr2" id="c63">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a27"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L38" class="el_method">getIssuingAuthority1()</a></td><td class="bar" id="b64"/><td class="ctr2" id="c64">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a28"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L39" class="el_method">getIssuingAuthority2()</a></td><td class="bar" id="b65"/><td class="ctr2" id="c65">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a33"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L40" class="el_method">getOfficialUseOnly1()</a></td><td class="bar" id="b66"/><td class="ctr2" id="c66">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a34"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L41" class="el_method">getOfficialUseOnly2()</a></td><td class="bar" id="b67"/><td class="ctr2" id="c67">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a18"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L42" class="el_method">getCreateTime()</a></td><td class="bar" id="b68"/><td class="ctr2" id="c68">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a35"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L43" class="el_method">getUpdateTime()</a></td><td class="bar" id="b69"/><td class="ctr2" id="c69">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a0"><a href="DwdbCtfCertificateDetailBcjhljzb.java.html#L9" class="el_method">canEqual(Object)</a></td><td class="bar" id="b70"/><td class="ctr2" id="c70">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k70">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>