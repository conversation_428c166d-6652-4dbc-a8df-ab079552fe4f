<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailCsssfzpx.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailCsssfzpx.java</span></div><h1>DwdbCtfCertificateDetailCsssfzpx.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

<span class="nc bnc" id="L6" title="All 182 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailCsssfzpx {
<span class="nc" id="L8">    private String csssfzpxId;               // 主键ID</span>
<span class="nc" id="L9">    private String dataId;                   // 证照数据主键</span>
<span class="nc" id="L10">    private String fullNameOfTheHolder1;     // 持证人姓名(中文)</span>
<span class="nc" id="L11">    private String fullNameOfTheHolder2;     // 持证人姓名(英文)</span>
<span class="nc" id="L12">    private String nationality1;             // 国籍(中文)</span>
<span class="nc" id="L13">    private String nationality2;             // 国籍(英文)</span>
<span class="nc" id="L14">    private String dateOfBirth1;             // 出生日期(中文格式)</span>
<span class="nc" id="L15">    private String dateOfBirth2;             // 出生日期(英文格式)</span>
<span class="nc" id="L16">    private String gender1;                  // 性别(中文)</span>
<span class="nc" id="L17">    private String gender2;                  // 性别(英文)</span>
<span class="nc" id="L18">    private String certificateNo;            // 证书编号</span>
<span class="nc" id="L19">    private String dateOfIssue1;             // 签发日期(中文格式)</span>
<span class="nc" id="L20">    private String dateOfIssue2;             // 签发日期(英文格式)</span>
<span class="nc" id="L21">    private String nameOfTheTraingManager1;  // 培训主管姓名(中文)</span>
<span class="nc" id="L22">    private String nameOfTheTraingManager2;  // 培训主管姓名(英文)</span>
<span class="nc" id="L23">    private String issuingBody1;             // 培训机构(中文)</span>
<span class="nc" id="L24">    private String issuingBody2;             // 培训机构(英文)</span>
<span class="nc" id="L25">    private String informationOfPhoto;       // 照片信息</span>
<span class="nc" id="L26">    private Date createTime;                 // 创建时间</span>
<span class="nc" id="L27">    private Date updateTime;                 // 更新时间</span>
<span class="nc" id="L28">    private String issuingBody;             // 培训机构</span>
<span class="nc" id="L29">    private String certificateHolderName;   // 持证人姓名</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>