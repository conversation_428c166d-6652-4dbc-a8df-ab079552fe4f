<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailCysrzsqb.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailCysrzsqb.java</span></div><h1>DwdbCtfCertificateDetailCysrzsqb.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * 船员适任证书申请表实体类
 */
<span class="nc bnc" id="L11" title="All 390 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailCysrzsqb {
<span class="nc" id="L13">    private String cysrzsqbId;    // 主键ID</span>
<span class="nc" id="L14">    private String dataId;        // 数据ID</span>
<span class="nc" id="L15">    private String sqYear;        // 申请年</span>
<span class="nc" id="L16">    private String sqMonth;       // 申请月</span>
<span class="nc" id="L17">    private String sqDay;         // 申请日</span>
<span class="nc" id="L18">    private String number;        // 编号</span>
<span class="nc" id="L19">    private String name;          // 姓名</span>
<span class="nc" id="L20">    private String py;            // 拼音</span>
<span class="nc" id="L21">    private String sex;           // 性别</span>
<span class="nc" id="L22">    private String idCode;        // 身份证号</span>
<span class="nc" id="L23">    private String birthYear;     // 出生年</span>
<span class="nc" id="L24">    private String birthMonth;    // 出生月</span>
<span class="nc" id="L25">    private String birthDay;      // 出生日</span>
<span class="nc" id="L26">    private String company;       // 公司</span>
<span class="nc" id="L27">    private String education;     // 学历</span>
<span class="nc" id="L28">    private String school;        // 学校</span>
<span class="nc" id="L29">    private String special;       // 专业</span>
<span class="nc" id="L30">    private String certNumber;    // 证书编号</span>
<span class="nc" id="L31">    private String certDate;      // 证书日期</span>
<span class="nc" id="L32">    private String photo;         // 照片</span>
<span class="nc" id="L33">    private String gmdssJob;      // GMDSS职务</span>
<span class="nc" id="L34">    private String gmdssNo;       // GMDSS编号</span>
<span class="nc" id="L35">    private String gYear;         // GMDSS年</span>
<span class="nc" id="L36">    private String gMonth;        // GMDSS月</span>
<span class="nc" id="L37">    private String gDay;          // GMDSS日</span>
<span class="nc" id="L38">    private String area2;         // 航区</span>
<span class="nc" id="L39">    private String level2;        // 等级</span>
<span class="nc" id="L40">    private String job2;          // 职务</span>
<span class="nc" id="L41">    private String limitInfo;     // 限制信息</span>
<span class="nc" id="L42">    private String hgzNo;         // 合格证编号</span>
<span class="nc" id="L43">    private String year1;         // 年份</span>
<span class="nc" id="L44">    private String month1;        // 月份</span>
<span class="nc" id="L45">    private String day1;          // 日期</span>
<span class="nc" id="L46">    private String signature;     // 签名</span>
<span class="nc" id="L47">    private String seal;          // 印章</span>
<span class="nc" id="L48">    private String link;          // 联系人</span>
<span class="nc" id="L49">    private String tel;           // 电话</span>
    
<span class="nc" id="L51">    private String certNo1;           // </span>
<span class="nc" id="L52">    private String sDay;           // </span>
<span class="nc" id="L53">    private String job1;           // </span>
<span class="nc" id="L54">    private String sYear;           // </span>
<span class="nc" id="L55">    private String level1;           // </span>
<span class="nc" id="L56">    private String sMonth;           // </span>
<span class="nc" id="L57">    private String area1;           //  </span>
    
<span class="nc" id="L59">    private Date createTime;      // 创建时间</span>
<span class="nc" id="L60">    private Date updateTime;      // 更新时间</span>

    @TableField(exist = false)
    private List&lt;DwdbCtfCertificateDetailCysrzsqbExperience&gt; experienceList;

    @TableField(exist = false)
    private List&lt;DwdbCtfCertificateDetailCysrzsqbOptions&gt; optionsList;

    public List&lt;DwdbCtfCertificateDetailCysrzsqbExperience&gt; getExperienceList() {
<span class="nc" id="L69">        return experienceList;</span>
    }

    public void setExperienceList(List&lt;DwdbCtfCertificateDetailCysrzsqbExperience&gt; experienceList) {
<span class="nc" id="L73">        this.experienceList = experienceList;</span>
<span class="nc" id="L74">    }</span>

    public List&lt;DwdbCtfCertificateDetailCysrzsqbOptions&gt; getOptionsList() {
<span class="nc" id="L77">        return optionsList;</span>
    }

    public void setOptionsList(List&lt;DwdbCtfCertificateDetailCysrzsqbOptions&gt; optionsList) {
<span class="nc" id="L81">        this.optionsList = optionsList;</span>
<span class="nc" id="L82">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>