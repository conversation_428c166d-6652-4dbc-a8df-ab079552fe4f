<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailHcptcysrzFunction.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailHcptcysrzFunction.java</span></div><h1>DwdbCtfCertificateDetailHcptcysrzFunction.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船普通船员适任证书职能信息实体类
 */
<span class="nc bnc" id="L9" title="All 94 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailHcptcysrzFunction {
<span class="nc" id="L11">    private String hcptcysrzFunctionId;  // 主键ID</span>
<span class="nc" id="L12">    private String dataId;            // 关联的源数据ID</span>
<span class="nc" id="L13">    private String hcptcysrzId;          // 关联的证书ID</span>
<span class="nc" id="L14">    private String function1;         // 职能(中文)</span>
<span class="nc" id="L15">    private String function2;         // 职能(英文)</span>
<span class="nc" id="L16">    private String level1;            // 等级(中文)</span>
<span class="nc" id="L17">    private String level2;            // 等级(英文)</span>
<span class="nc" id="L18">    private String limitationsApplying1;  // 适用限制(中文)</span>
<span class="nc" id="L19">    private String limitationsApplying2;  // 适用限制(英文)</span>
<span class="nc" id="L20">    private Date createTime;          // 创建时间</span>
<span class="nc" id="L21">    private Date updateTime;          // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>