<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailHcpxhg.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailHcpxhg.java</span></div><h1>DwdbCtfCertificateDetailHcpxhg.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证书主表实体类
 */
<span class="nc bnc" id="L9" title="All 206 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailHcpxhg {
    /**
     * 主键ID
     */
<span class="nc" id="L14">    private String hcpxhgId;</span>
    
    /**
     * 关联的证书数据ID
     */
<span class="nc" id="L19">    private String dataId;</span>
    
    /**
     * 持证人姓名(中文)
     */
<span class="nc" id="L24">    private String fullNameOfTheHolder1;</span>
    
    /**
     * 持证人姓名(英文)
     */
<span class="nc" id="L29">    private String fullNameOfTheHolder2;</span>
    
    /**
     * 国籍(中文)
     */
<span class="nc" id="L34">    private String nationality1;</span>
    
    /**
     * 国籍(英文)
     */
<span class="nc" id="L39">    private String nationality2;</span>
    
    /**
     * 出生日期(中文格式)
     */
<span class="nc" id="L44">    private String dateOfBirth1;</span>
    
    /**
     * 出生日期(英文格式)
     */
<span class="nc" id="L49">    private String dateOfBirth2;</span>
    
    /**
     * 性别(中文)
     */
<span class="nc" id="L54">    private String gender1;</span>
    
    /**
     * 性别(英文)
     */
<span class="nc" id="L59">    private String gender2;</span>
    
    /**
     * 证书编号
     */
<span class="nc" id="L64">    private String certificateNo;</span>
    
    /**
     * 签发时间(中文格式)
     */
<span class="nc" id="L69">    private String issuedOn1;</span>
    
    /**
     * 签发时间(英文格式)
     */
<span class="nc" id="L74">    private String issuedOn2;</span>
    
    /**
     * 持证人姓名
     */
<span class="nc" id="L79">    private String certificateHolderName;</span>
    
    /**
     * 照片信息
     */
<span class="nc" id="L84">    private String informationOfPhoto;</span>
    
    /**
     * 授权官员签名(中文)
     */
<span class="nc" id="L89">    private String signatureOfDulyAutOffi1;</span>
    
    /**
     * 授权官员签名(英文)
     */
<span class="nc" id="L94">    private String signatureOfDulyAutOffi2;</span>
    
    /**
     * 授权官员姓名(中文)
     */
<span class="nc" id="L99">    private String nameOfDulyAutOffi1;</span>
    
    /**
     * 授权官员姓名(英文)
     */
<span class="nc" id="L104">    private String nameOfDulyAutOffi2;</span>
    
    /**
     * 发证机关(中文)
     */
<span class="nc" id="L109">    private String officalSeal1;</span>
    
    /**
     * 发证机关(英文)
     */
<span class="nc" id="L114">    private String officalSeal2;</span>
    
    /**
     * 官方使用(中文)
     */
<span class="nc" id="L119">    private String officialUseOnly1;</span>
    
    /**
     * 官方使用(英文)
     */
<span class="nc" id="L124">    private String officialUseOnly2;</span>
    
    /**
     * 创建时间
     */
<span class="nc" id="L129">    private Date createTime;</span>
    
    /**
     * 更新时间
     */
<span class="nc" id="L134">    private Date updateTime;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>