<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailHcpxhgTraining.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailHcpxhgTraining.java</span></div><h1>DwdbCtfCertificateDetailHcpxhgTraining.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训合格证书培训项目信息表实体类
 */
<span class="nc bnc" id="L9" title="All 110 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailHcpxhgTraining {
    /**
     * 主键ID
     */
<span class="nc" id="L14">    private String hcpxhgTrainingId;</span>
    
    /**
     * 关联的源数据ID
     */
<span class="nc" id="L19">    private String dataId;</span>
    
    /**
     * 关联的证书主表ID
     */
<span class="nc" id="L24">    private String hcpxhgId;</span>
    
    /**
     * 培训项目代码
     */
<span class="nc" id="L29">    private String prefix;</span>
    
    /**
     * 培训项目名称(中文)
     */
<span class="nc" id="L34">    private String titleOfTheCertificate1;</span>
    
    /**
     * 培训项目名称(英文)
     */
<span class="nc" id="L39">    private String titleOfTheCertificate2;</span>
    
    /**
     * 培训等级代码
     */
<span class="nc" id="L44">    private String level;</span>
    
    /**
     * 培训发证日期(中文格式)
     */
<span class="nc" id="L49">    private String dateOfIssue1;</span>
    
    /**
     * 培训发证日期(英文格式)
     */
<span class="nc" id="L54">    private String dateOfIssue2;</span>
    
    /**
     * 培训到期日期(中文格式)
     */
<span class="nc" id="L59">    private String dateOfExpiry1;</span>
    
    /**
     * 培训到期日期(英文格式)
     */
<span class="nc" id="L64">    private String dateOfExpiry2;</span>
    
    /**
     * 创建时间
     */
<span class="nc" id="L69">    private Date createTime;</span>
    
    /**
     * 更新时间
     */
<span class="nc" id="L74">    private Date updateTime;</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>