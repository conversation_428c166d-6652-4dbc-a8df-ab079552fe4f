<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailHsfhcysrz.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailHsfhcysrz.java</span></div><h1>DwdbCtfCertificateDetailHsfhcysrz.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海上非自航船舶船员适任证书实体类
 */
<span class="nc bnc" id="L9" title="All 118 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailHsfhcysrz {
<span class="nc" id="L11">    private String hsfhcysrzId;        // 主键ID</span>
<span class="nc" id="L12">    private String dataId;             // 数据ID</span>
<span class="nc" id="L13">    private String certificateNo;      // 证书编号</span>
<span class="nc" id="L14">    private String fullNameOfTheHolder;  // 持证人姓名</span>
<span class="nc" id="L15">    private String dateOfBirth;        // 出生日期</span>
<span class="nc" id="L16">    private String placeOfBirth;       // 出生地</span>
<span class="nc" id="L17">    private String dateOfExpirty;      // 有效期至</span>
<span class="nc" id="L18">    private String dateOfIssue;        // 签发日期</span>
<span class="nc" id="L19">    private String certificateHolderName;  // 持证人姓名</span>
<span class="nc" id="L20">    private String informationOfPhoto;  // 照片信息</span>
<span class="nc" id="L21">    private String nameOfDulyAuthorizedOfficial;  // 授权官员姓名</span>
<span class="nc" id="L22">    private String remark;             // 备注</span>
<span class="nc" id="L23">    private Date createTime;           // 创建时间</span>
<span class="nc" id="L24">    private Date updateTime;           // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>