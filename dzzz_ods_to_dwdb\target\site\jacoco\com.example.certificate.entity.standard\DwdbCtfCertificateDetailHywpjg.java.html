<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailHywpjg.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailHywpjg.java</span></div><h1>DwdbCtfCertificateDetailHywpjg.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海员外派机构资质证书实体类
 */
<span class="nc bnc" id="L9" title="All 238 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailHywpjg {
<span class="nc" id="L11">    private String hywpjgId;           // 主键ID</span>
<span class="nc" id="L12">    private String dataId;             // 数据ID</span>
<span class="nc" id="L13">    private String permitNumber1;       // 许可证号</span>
<span class="nc" id="L14">    private String permitNumber2;       // 许可证号(重复)</span>
<span class="nc" id="L15">    private String anThorityName1;     // 机构名称(中文)</span>
<span class="nc" id="L16">    private String anThorityName2;     // 机构名称(英文)</span>
<span class="nc" id="L17">    private String anThorityName3;     // 机构名称(中文重复)</span>
<span class="nc" id="L18">    private String anThorityName4;     // 机构名称(英文重复)</span>
<span class="nc" id="L19">    private String address1;           // 地址(中文)</span>
<span class="nc" id="L20">    private String address2;           // 地址(英文)</span>
<span class="nc" id="L21">    private String address3;           // 地址(中文重复)</span>
<span class="nc" id="L22">    private String address4;           // 地址(英文重复)</span>
<span class="nc" id="L23">    private String representative1;    // 法定代表人(中文)</span>
<span class="nc" id="L24">    private String representative2;    // 法定代表人(英文)</span>
<span class="nc" id="L25">    private String representative3;    // 法定代表人(中文重复)</span>
<span class="nc" id="L26">    private String representative4;    // 法定代表人(英文重复)</span>
<span class="nc" id="L27">    private String expiryDate1;        // 到期日期(中文格式)</span>
<span class="nc" id="L28">    private String expiryDate2;        // 到期日期(英文格式)</span>
<span class="nc" id="L29">    private String dateOfIssue1;       // 签发日期(中文格式)</span>
<span class="nc" id="L30">    private String dateOfIssue3;       // 签发日期(重复)</span>
<span class="nc" id="L31">    private String issuingAuthority1;  // 发证机关(中文)</span>
<span class="nc" id="L32">    private String issuingAuthority2;  // 发证机关(英文)</span>
<span class="nc" id="L33">    private String issuingAuthority3;  // 发证机关(中文重复)</span>
<span class="nc" id="L34">    private String issuingAuthority4;  // 发证机关(英文重复)</span>
<span class="nc" id="L35">    private String remark1;            // 备注(中文)</span>
<span class="nc" id="L36">    private String remark2;            // 备注(英文)</span>
<span class="nc" id="L37">    private String annualExamination;  // 年审信息</span>
<span class="nc" id="L38">    private Date createTime;           // 创建时间</span>
<span class="nc" id="L39">    private Date updateTime;           // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>