<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailJkzm.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailJkzm.java</span></div><h1>DwdbCtfCertificateDetailJkzm.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员健康证明实体类
 */
<span class="nc bnc" id="L9" title="All 294 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailJkzm {
<span class="nc" id="L11">    private String jkzmId;              // 主键ID</span>
<span class="nc" id="L12">    private String dataId;              // 数据ID</span>
<span class="nc" id="L13">    private String fullNameOfTheHolder1;  // 持证人姓名(中文)</span>
<span class="nc" id="L14">    private String fullNameOfTheHolder2;  // 持证人姓名(英文)</span>
<span class="nc" id="L15">    private String nationality1;        // 国籍(中文)</span>
<span class="nc" id="L16">    private String nationality2;        // 国籍(英文)</span>
<span class="nc" id="L17">    private String dateOfBirth1;        // 出生日期(中文格式)</span>
<span class="nc" id="L18">    private String dateOfBirth2;        // 出生日期(英文格式)</span>
<span class="nc" id="L19">    private String gender1;             // 性别(中文)</span>
<span class="nc" id="L20">    private String gender2;             // 性别(英文)</span>
<span class="nc" id="L21">    private String department1;         // 部门(中文)</span>
<span class="nc" id="L22">    private String department2;         // 部门(英文)</span>
<span class="nc" id="L23">    private String certificateNo;       // 证书编号</span>
<span class="nc" id="L24">    private String certificateExpiringDate1;  // 证书到期日期(中文格式)</span>
<span class="nc" id="L25">    private String certificateExpiringDate2;  // 证书到期日期(英文格式)</span>
<span class="nc" id="L26">    private String dateOfIssue1;        // 签发日期(中文格式)</span>
<span class="nc" id="L27">    private String dateOfIssue2;        // 签发日期(英文格式)</span>
<span class="nc" id="L28">    private String certificateHolderName;  // 持证人姓名</span>
<span class="nc" id="L29">    private String informationOfPhoto;  // 照片信息</span>
<span class="nc" id="L30">    private String yesOrNo1;           // 体检项目1结果</span>
<span class="nc" id="L31">    private String yesOrNo2;           // 体检项目2结果</span>
<span class="nc" id="L32">    private String yesOrNo3;           // 体检项目3结果</span>
<span class="nc" id="L33">    private String yesOrNo4;           // 体检项目4结果</span>
<span class="nc" id="L34">    private String yesOrNo5;           // 体检项目5结果</span>
<span class="nc" id="L35">    private String yesOrNo6;           // 体检项目6结果</span>
<span class="nc" id="L36">    private String yesOrNo7;           // 体检项目7结果</span>
<span class="nc" id="L37">    private String yesOrNo8;           // 体检项目8结果</span>
<span class="nc" id="L38">    private String yesOrNo9;           // 体检项目9结果</span>
<span class="nc" id="L39">    private String authorizingAuthority1;  // 授权机构(中文)</span>
<span class="nc" id="L40">    private String authorizingAuthority2;  // 授权机构(英文)</span>
<span class="nc" id="L41">    private String doctorName1;        // 医生姓名(中文)</span>
<span class="nc" id="L42">    private String doctorName2;        // 医生姓名(英文)</span>
<span class="nc" id="L43">    private String issuingAuthority1;  // 发证机关(中文)</span>
<span class="nc" id="L44">    private String issuingAuthority2;  // 发证机关(英文)</span>
<span class="nc" id="L45">    private Date createTime;           // 创建时间</span>
<span class="nc" id="L46">    private Date updateTime;           // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>