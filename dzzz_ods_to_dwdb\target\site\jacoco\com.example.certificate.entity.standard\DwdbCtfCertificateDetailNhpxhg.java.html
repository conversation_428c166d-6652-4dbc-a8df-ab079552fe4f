<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailNhpxhg.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailNhpxhg.java</span></div><h1>DwdbCtfCertificateDetailNhpxhg.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 内河船舶船员培训合格证实体类
 */
<span class="nc bnc" id="L10" title="All 110 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailNhpxhg {
<span class="nc" id="L12">    private String nhpxhgId;           // 主键ID</span>
<span class="nc" id="L13">    private String dataId;             // 数据ID</span>
<span class="nc" id="L14">    private String name;               // 姓名</span>
<span class="nc" id="L15">    private String sex;                // 性别</span>
<span class="nc" id="L16">    private String number;             // 证件号码</span>
<span class="nc" id="L17">    private String printNo;            // 印刷号码</span>
<span class="nc" id="L18">    private String photo;              // 照片信息</span>
<span class="nc" id="L19">    private String year;               // 签发年份</span>
<span class="nc" id="L20">    private String month;              // 签发月份</span>
<span class="nc" id="L21">    private String day;                // 签发日期    </span>
<span class="nc" id="L22">    private Date createTime;           // 创建时间</span>
<span class="nc" id="L23">    private Date updateTime;           // 更新时间</span>
<span class="nc" id="L24">    private List&lt;DwdbCtfCertificateDetailNhpxhgItem&gt; items; // 培训项目子表数据</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>