<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailSeamanInfo.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailSeamanInfo.java</span></div><h1>DwdbCtfCertificateDetailSeamanInfo.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证个人信息实体类
 */
<span class="nc bnc" id="L9" title="All 246 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailSeamanInfo {
<span class="nc" id="L11">    private String seamanInfoId;      // 主键ID</span>
<span class="nc" id="L12">    private String dataId;            // 数据ID</span>
<span class="nc" id="L13">    private String number;            // 证书编号</span>
<span class="nc" id="L14">    private String nameCn;            // 姓名（中文）</span>
<span class="nc" id="L15">    private String nameEn;            // 姓名（英文）</span>
<span class="nc" id="L16">    private String sexCn;             // 性别（中文）</span>
<span class="nc" id="L17">    private String sexEn;             // 性别（英文）</span>
<span class="nc" id="L18">    private String countryCn;         // 国籍（中文）</span>
<span class="nc" id="L19">    private String countryEn;         // 国籍（英文）</span>
<span class="nc" id="L20">    private String birthCn;           // 出生日期（中文）</span>
<span class="nc" id="L21">    private String birthEn;           // 出生日期（英文）</span>
<span class="nc" id="L22">    private String fileNoCn;          // 档案编号（中文）</span>
<span class="nc" id="L23">    private String fileNoEn;          // 档案编号（英文）</span>
<span class="nc" id="L24">    private String qualificationCn;    // 资格等级（中文）</span>
<span class="nc" id="L25">    private String qualificationEn;    // 资格等级（英文）</span>
<span class="nc" id="L26">    private String initialDateCn;      // 初次发证日期（中文）</span>
<span class="nc" id="L27">    private String initialDateEn;      // 初次发证日期（英文）</span>
<span class="nc" id="L28">    private String expiryDateCn;       // 有效期至（中文）</span>
<span class="nc" id="L29">    private String expiryDateEn;       // 有效期至（英文）</span>
<span class="nc" id="L30">    private String signDeptCn;         // 签发机关（中文）</span>
<span class="nc" id="L31">    private String signDeptEn;         // 签发机关（英文）</span>
<span class="nc" id="L32">    private String officeOfIssueCn;    // 发证机关（中文）</span>
<span class="nc" id="L33">    private String officeOfIssueEn;    // 发证机关（英文）</span>
<span class="nc" id="L34">    private String date;               // 日期</span>
<span class="nc" id="L35">    private String photo;              // 照片路径</span>
<span class="nc" id="L36">    private String year;               // 发证年份</span>
<span class="nc" id="L37">    private String month;              // 发证月份</span>
<span class="nc" id="L38">    private String day;                // 发证日期</span>
<span class="nc" id="L39">    private Date createTime;           // 创建时间</span>
<span class="nc" id="L40">    private Date updateTime;           // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>