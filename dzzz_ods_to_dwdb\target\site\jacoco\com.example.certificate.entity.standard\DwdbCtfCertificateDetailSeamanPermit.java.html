<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailSeamanPermit.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailSeamanPermit.java</span></div><h1>DwdbCtfCertificateDetailSeamanPermit.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证照面信息主表实体类
 */
<span class="nc bnc" id="L9" title="All 230 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailSeamanPermit {
<span class="nc" id="L11">    private String seamanPermitId;          // 主键ID</span>
<span class="nc" id="L12">    private String dataId;                // 证照数据主键</span>
<span class="nc" id="L13">    private String permitNumber1;         // 许可证编号1</span>
<span class="nc" id="L14">    private String anThorityName1;        // 机构名称1</span>
<span class="nc" id="L15">    private String trainingInstitutionCode1; // 培训机构代码1</span>
<span class="nc" id="L16">    private String representative1;       // 法定代表人1</span>
<span class="nc" id="L17">    private String trainingProgram1;      // 培训项目1</span>
<span class="nc" id="L18">    private String trainingProgram2;      // 培训项目2</span>
<span class="nc" id="L19">    private String registeredAddress1;    // 注册地址1</span>
<span class="nc" id="L20">    private String trainingLocation1;     // 培训地点1</span>
<span class="nc" id="L21">    private String periodOfValidity1;     // 有效期起始1</span>
<span class="nc" id="L22">    private String periodOfValidity2;     // 有效期结束1</span>
<span class="nc" id="L23">    private String issuingAuthority1;     // 发证机关1</span>
<span class="nc" id="L24">    private String dateofIssue1;          // 发证日期1</span>
<span class="nc" id="L25">    private String permitNumber2;         // 许可证编号2</span>
<span class="nc" id="L26">    private String anThorityName2;        // 机构名称2</span>
<span class="nc" id="L27">    private String registeredAddress2;    // 注册地址2</span>
<span class="nc" id="L28">    private String representative2;       // 法定代表人2</span>
<span class="nc" id="L29">    private String trainingLocation2;     // 培训地点2</span>
<span class="nc" id="L30">    private String periodOfValidity3;     // 有效期起始2</span>
<span class="nc" id="L31">    private String periodOfValidity4;     // 有效期结束2</span>
<span class="nc" id="L32">    private String remarks;               // 备注</span>
<span class="nc" id="L33">    private String issuingAuthority2;     // 发证机关2</span>
<span class="nc" id="L34">    private String dateofIssue2;          // 发证日期2</span>
<span class="nc" id="L35">    private Date createTime;</span>
<span class="nc" id="L36">    private Date updateTime;</span>
<span class="nc" id="L37">    private String createBy;              // 创建人</span>
<span class="nc" id="L38">    private String updateBy;              // 更新人</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>