<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailSeamanPermitItem.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailSeamanPermitItem.java</span></div><h1>DwdbCtfCertificateDetailSeamanPermitItem.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 海船船员培训许可证培训项目子表实体类
 */
<span class="nc bnc" id="L9" title="All 86 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailSeamanPermitItem {
<span class="nc" id="L11">    private String seamanPermitItemId;    // 主键ID</span>
<span class="nc" id="L12">    private String dataId;                // 关联的源数据ID</span>
<span class="nc" id="L13">    private String seamanPermitId;        // 关联主表ID</span>
<span class="nc" id="L14">    private String number;              // 培训项目序号</span>
<span class="nc" id="L15">    private String atrainingProgram;    // 培训项目名称</span>
<span class="nc" id="L16">    private String trainingScale;       // 培训规模</span>
<span class="nc" id="L17">    private Date createTime;            // 创建时间</span>
<span class="nc" id="L18">    private Date updateTime;            // 更新时间</span>
<span class="nc" id="L19">    private String createBy;            // 创建人</span>
<span class="nc" id="L20">    private String updateBy;            // 更新人</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>