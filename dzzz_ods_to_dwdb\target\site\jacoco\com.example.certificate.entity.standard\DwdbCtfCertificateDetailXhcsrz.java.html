<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DwdbCtfCertificateDetailXhcsrz.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.entity.standard</a> &gt; <span class="el_source">DwdbCtfCertificateDetailXhcsrz.java</span></div><h1>DwdbCtfCertificateDetailXhcsrz.java</h1><pre class="source lang-java linenums">package com.example.certificate.entity.standard;

import lombok.Data;
import java.util.Date;

/**
 * 小型海船适任证书实体类
 */
<span class="nc bnc" id="L9" title="All 238 branches missed.">@Data</span>
public class DwdbCtfCertificateDetailXhcsrz {
<span class="nc" id="L11">    private String xhcsrzId;                    // 主键ID</span>
<span class="nc" id="L12">    private String dataId;                      // 数据ID</span>
<span class="nc" id="L13">    private String fullNameOfTheHolder1;        // 持证人姓名(中文)</span>
<span class="nc" id="L14">    private String fullNameOfTheHolder2;        // 持证人姓名(英文)</span>
<span class="nc" id="L15">    private String nationality1;                // 国籍(中文)</span>
<span class="nc" id="L16">    private String nationality2;                // 国籍(英文)</span>
<span class="nc" id="L17">    private String dateOfBirth1;                // 出生日期(中文格式)</span>
<span class="nc" id="L18">    private String dateOfBirth2;                // 出生日期(英文格式)</span>
<span class="nc" id="L19">    private String gender1;                     // 性别(中文)</span>
<span class="nc" id="L20">    private String gender2;                     // 性别(英文)</span>
<span class="nc" id="L21">    private String certificateNo;               // 证书编号</span>
<span class="nc" id="L22">    private String certificateExpiringDate1;    // 证书到期日期(中文格式)</span>
<span class="nc" id="L23">    private String certificateExpiringDate2;    // 证书到期日期(英文格式)</span>
<span class="nc" id="L24">    private String dateOfIssue1;                // 签发日期(中文格式)</span>
<span class="nc" id="L25">    private String dateOfIssue2;                // 签发日期(英文格式)</span>
<span class="nc" id="L26">    private String certificateHolderName;       // 持证人姓名</span>
<span class="nc" id="L27">    private String articleNumber1;              // 条款编号1(中文)</span>
<span class="nc" id="L28">    private String articleNumber2;              // 条款编号2(中文)</span>
<span class="nc" id="L29">    private String articleNumber3;              // 条款编号3(英文)</span>
<span class="nc" id="L30">    private String articleNumber4;              // 条款编号4(英文)</span>
<span class="nc" id="L31">    private String informationOfPhoto;          // 照片信息    </span>
<span class="nc" id="L32">    private String nameOfDulyAuthorizedOfficial1; // 授权官员姓名(中文)</span>
<span class="nc" id="L33">    private String nameOfDulyAuthorizedOfficial2; // 授权官员姓名(英文)</span>
<span class="nc" id="L34">    private String issuingAuthority1;           // 发证机关(中文)</span>
<span class="nc" id="L35">    private String issuingAuthority2;           // 发证机关(英文)</span>
<span class="nc" id="L36">    private String officialUseOnly1;            // 官方使用1</span>
<span class="nc" id="L37">    private String officialUseOnly2;            // 官方使用2</span>
<span class="nc" id="L38">    private Date createTime;                    // 创建时间</span>
<span class="nc" id="L39">    private Date updateTime;                    // 更新时间</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>