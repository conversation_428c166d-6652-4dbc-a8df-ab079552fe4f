<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateEtlServiceImpl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.html" class="el_package">com.example.certificate.service.impl</a> &gt; <span class="el_class">CertificateEtlServiceImpl</span></div><h1>CertificateEtlServiceImpl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">5,003 of 5,254</td><td class="ctr2">4%</td><td class="bar">581 of 596</td><td class="ctr2">2%</td><td class="ctr1">338</td><td class="ctr2">348</td><td class="ctr1">1,256</td><td class="ctr2">1,315</td><td class="ctr1">19</td><td class="ctr2">23</td></tr></tfoot><tbody><tr><td id="a18"><a href="CertificateEtlServiceImpl.java.html#L296" class="el_method">processSourceData(List)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="3,215" alt="3,215"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="382" alt="382"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">192</td><td class="ctr2" id="g0">192</td><td class="ctr1" id="h0">752</td><td class="ctr2" id="i0">752</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="CertificateEtlServiceImpl.java.html#L1462" class="el_method">batchInsertByTableType(String, List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="631" alt="631"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="90" alt="90"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">59</td><td class="ctr2" id="g1">59</td><td class="ctr1" id="h1">171</td><td class="ctr2" id="i1">171</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="CertificateEtlServiceImpl.java.html#L1966" class="el_method">cleanupTargetData(OdsCertificateData)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="307" alt="307"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="31" alt="31"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">29</td><td class="ctr2" id="g2">29</td><td class="ctr1" id="h2">92</td><td class="ctr2" id="i2">92</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a21"><a href="CertificateEtlServiceImpl.java.html#L1721" class="el_method">saveErrorRecord(Object, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="135" alt="135"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g10">4</td><td class="ctr1" id="h3">61</td><td class="ctr2" id="i3">61</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="CertificateEtlServiceImpl.java.html#L140" class="el_method">executeEtlTask(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="117" alt="117"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="55" alt="55"/></td><td class="ctr2" id="c3">31%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="e1">37%</td><td class="ctr1" id="f9">4</td><td class="ctr2" id="g9">5</td><td class="ctr1" id="h4">26</td><td class="ctr2" id="i4">42</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a17"><a href="CertificateEtlServiceImpl.java.html#L243" class="el_method">processRedoData(List, StringBuilder)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="108" alt="108"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h7">21</td><td class="ctr2" id="i8">21</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a10"><a href="CertificateEtlServiceImpl.java.html#L2196" class="el_method">getMappedCapacity(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="92" alt="92"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="12" alt="12"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h6">23</td><td class="ctr2" id="i7">23</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="CertificateEtlServiceImpl.java.html#L1818" class="el_method">getDataIdFromObject(Object)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="77" alt="77"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="12" alt="12"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">7</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h5">25</td><td class="ctr2" id="i6">25</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="CertificateEtlServiceImpl.java.html#L275" class="el_method">processIncrementalData(String, List, StringBuilder)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="53" alt="53"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h11">9</td><td class="ctr2" id="i12">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a16"><a href="CertificateEtlServiceImpl.java.html#L2143" class="el_method">processQueryOrg(String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="53" alt="53"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h8">14</td><td class="ctr2" id="i9">14</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a9"><a href="CertificateEtlServiceImpl.java.html#L1939" class="el_method">getLastDataId(List)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="44" alt="44"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h9">12</td><td class="ctr2" id="i10">12</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="CertificateEtlServiceImpl.java.html#L1915" class="el_method">getMaxFcdcDate(List)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="35" alt="35"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="12" alt="12"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f6">7</td><td class="ctr2" id="g6">7</td><td class="ctr1" id="h10">10</td><td class="ctr2" id="i11">10</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a14"><a href="CertificateEtlServiceImpl.java.html#L2129" class="el_method">initTask(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="30" alt="30"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a12"><a href="CertificateEtlServiceImpl.java.html#L2168" class="el_method">getOrgTypeByCategory(String)</a></td><td class="bar" id="b13"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">6</td><td class="ctr2" id="g8">6</td><td class="ctr1" id="h12">8</td><td class="ctr2" id="i13">8</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a3"><a href="CertificateEtlServiceImpl.java.html#L1864" class="el_method">castList(List, Class)</a></td><td class="bar" id="b14"/><td class="ctr2" id="c17">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h16">5</td><td class="ctr2" id="i18">5</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a7"><a href="CertificateEtlServiceImpl.java.html#L1804" class="el_method">findOriginalData(String)</a></td><td class="bar" id="b15"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h17">4</td><td class="ctr2" id="i19">4</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a2"><a href="CertificateEtlServiceImpl.java.html#L1699" class="el_method">batchInsertMainData(List)</a></td><td class="bar" id="b16"/><td class="ctr2" id="c19">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h14">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a0"><a href="CertificateEtlServiceImpl.java.html#L2109" class="el_method">batchInsertAttributes(List)</a></td><td class="bar" id="b17"/><td class="ctr2" id="c20">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h15">6</td><td class="ctr2" id="i17">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a20"><a href="CertificateEtlServiceImpl.java.html#L1708" class="el_method">recoverBatchInsert(Exception, List)</a></td><td class="bar" id="b18"/><td class="ctr2" id="c21">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h18">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a19"><a href="CertificateEtlServiceImpl.java.html#L2118" class="el_method">recover(Exception, List)</a></td><td class="bar" id="b19"/><td class="ctr2" id="c22">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a13"><a href="CertificateEtlServiceImpl.java.html#L87" class="el_method">initCache()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="163" alt="163"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d13"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g7">7</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i5">34</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a22"><a href="CertificateEtlServiceImpl.java.html#L42" class="el_method">static {...}</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="30" alt="30"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i14">8</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a4"><a href="CertificateEtlServiceImpl.java.html#L44" class="el_method">CertificateEtlServiceImpl()</a></td><td class="bar" id="b22"/><td class="ctr2" id="c2">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>