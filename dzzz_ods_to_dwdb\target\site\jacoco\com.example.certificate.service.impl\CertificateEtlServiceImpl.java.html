<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateEtlServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.service.impl</a> &gt; <span class="el_source">CertificateEtlServiceImpl.java</span></div><h1>CertificateEtlServiceImpl.java</h1><pre class="source lang-java linenums">package com.example.certificate.service.impl;

import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.*;
import com.example.certificate.mapper.aggregate.OdsCertificateDataMapper;
import com.example.certificate.mapper.standard.DataReceptionTaskMapper;
import com.example.certificate.mapper.standard.DwdbCertificateDataMapper;
import com.example.certificate.service.CertificateEtlService;
import com.example.certificate.util.CertificateConverter;
import com.example.certificate.dto.CertificateConvertResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
import com.alibaba.fastjson.JSON;
import java.util.Collections;
import java.util.Arrays;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.sql.SQLException;
import com.example.certificate.mapper.standard.CertTypeDirectoryMapper;
import com.example.certificate.mapper.standard.CtfSysDeptMapper;
import com.example.certificate.mapper.standard.DictYthOrgMappingMapper;
import com.example.certificate.mapper.standard.CertQueryOrgMapper;
import com.example.certificate.mapper.standard.CertCapMappingMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

<span class="fc" id="L42">@Slf4j</span>
@Service
<span class="fc" id="L44">public class CertificateEtlServiceImpl implements CertificateEtlService {</span>

    @Autowired
    private OdsCertificateDataMapper odsCertificateDataMapper;

    @Autowired
    private DwdbCertificateDataMapper dwdbCertificateDataMapper;

    @Autowired
    private DataReceptionTaskMapper dataReceptionTaskMapper;

    @Autowired
    private CertificateConverter certificateConverter;

    // 将缓存改为静态变量
<span class="fc" id="L59">    private static final Map&lt;String, CertTypeDirectory&gt; certTypeCache = new HashMap&lt;&gt;();</span>
<span class="fc" id="L60">    private static final Map&lt;String, DictYthOrgMapping&gt; orgMappingCache = new HashMap&lt;&gt;();</span>
<span class="fc" id="L61">    private static final Map&lt;String, CtfSysDept&gt; deptInfoCache = new HashMap&lt;&gt;();</span>
<span class="fc" id="L62">    private static final Map&lt;String, CertQueryOrg&gt; queryOrgCache = new HashMap&lt;&gt;();</span>
<span class="fc" id="L63">    private static final Map&lt;String, String&gt; capMappingCache = new HashMap&lt;&gt;();</span>

<span class="fc" id="L65">    private static boolean cacheInitialized = false;</span>
<span class="fc" id="L66">    private static final List&lt;CertQueryOrg&gt; newQueryOrgs = new ArrayList&lt;&gt;();</span>

    @Autowired
    private CertTypeDirectoryMapper certTypeDirectoryMapper;

    @Autowired
    private DictYthOrgMappingMapper dictYthOrgMappingMapper;

    @Autowired
    private CtfSysDeptMapper ctfSysDeptMapper;

    @Autowired
    private CertQueryOrgMapper certQueryOrgMapper;

    @Autowired
    private CertCapMappingMapper certCapMappingMapper;

    /**
     * 初始化基础参数表缓存
     */
    private synchronized void initCache() {
<span class="fc bfc" id="L87" title="All 2 branches covered.">        if (cacheInitialized) {</span>
<span class="fc" id="L88">            return;</span>
        }

<span class="fc" id="L91">        log.info(&quot;开始初始化基础参数表缓存...&quot;);</span>

        // 1. 加载证书类型目录
<span class="fc" id="L94">        List&lt;CertTypeDirectory&gt; certTypeList = certTypeDirectoryMapper.getAllCertTypeInfo();</span>
<span class="fc bfc" id="L95" title="All 2 branches covered.">        for (CertTypeDirectory certType : certTypeList) {</span>
<span class="fc" id="L96">            certTypeCache.put(certType.getCatalogId(), certType);</span>
<span class="fc" id="L97">        }</span>
<span class="fc" id="L98">        log.info(&quot;证书类型目录缓存加载完成，共 {} 条记录&quot;, certTypeCache.size());</span>

        // 2. 加载机构映射
<span class="fc" id="L101">        List&lt;DictYthOrgMapping&gt; orgMappingList = dictYthOrgMappingMapper.getAllOrgMapping();</span>
<span class="fc" id="L102">        log.info(&quot;从数据库加载机构映射数据，共 {} 条记录&quot;, orgMappingList.size());</span>
<span class="fc bfc" id="L103" title="All 2 branches covered.">        for (DictYthOrgMapping orgMapping : orgMappingList) {</span>
<span class="fc" id="L104">            orgMappingCache.put(orgMapping.getSrcOrgCode(), orgMapping);</span>
<span class="fc" id="L105">        }</span>
<span class="fc" id="L106">        log.info(&quot;机构映射缓存加载完成，共 {} 条记录&quot;, orgMappingCache.size());</span>

        // 3. 加载部门信息
<span class="fc" id="L109">        List&lt;CtfSysDept&gt; deptInfoList = ctfSysDeptMapper.getAllDeptInfo();</span>
<span class="fc bfc" id="L110" title="All 2 branches covered.">        for (CtfSysDept deptInfo : deptInfoList) {</span>
<span class="fc" id="L111">            deptInfoCache.put(deptInfo.getCode(), deptInfo);</span>
<span class="fc" id="L112">        }</span>
<span class="fc" id="L113">        log.info(&quot;部门信息缓存加载完成，共 {} 条记录&quot;, deptInfoCache.size());</span>

        // 4. 加载机构查询辅助表
<span class="fc" id="L116">        List&lt;CertQueryOrg&gt; queryOrgList = certQueryOrgMapper.getAllQueryOrgs();</span>
<span class="fc bfc" id="L117" title="All 2 branches covered.">        for (CertQueryOrg queryOrg : queryOrgList) {</span>
<span class="fc" id="L118">            queryOrgCache.put(queryOrg.getQueryOrgName(), queryOrg);</span>
<span class="fc" id="L119">        }</span>
<span class="fc" id="L120">        log.info(&quot;机构查询辅助表缓存加载完成，共 {} 条记录&quot;, queryOrgCache.size());</span>

        // 加载职务映射表
<span class="fc" id="L123">        log.info(&quot;开始加载职务映射表...&quot;);</span>
<span class="fc" id="L124">        List&lt;CertCapMapping&gt; capMappings = certCapMappingMapper.getAllCapMappings();</span>
<span class="fc bfc" id="L125" title="All 2 branches covered.">        for (CertCapMapping mapping : capMappings) {</span>
<span class="fc" id="L126">            capMappingCache.put(mapping.getSourceCapName(), mapping.getDestCapName());</span>
<span class="fc" id="L127">        }</span>
<span class="fc" id="L128">        log.info(&quot;职务映射表加载完成，共加载 {} 条记录&quot;, capMappings.size());</span>

<span class="fc" id="L130">        cacheInitialized = true;</span>
<span class="fc" id="L131">        log.info(&quot;基础参数表缓存初始化完成&quot;);</span>

        // 将缓存对象设置到CertificateConverter中
<span class="fc" id="L134">        certificateConverter.setCaches(certTypeCache, orgMappingCache, deptInfoCache);</span>
<span class="fc" id="L135">    }</span>

    @Override
    public String executeEtlTask(String taskName) {
        // 初始化缓存
<span class="fc" id="L140">        initCache();</span>

<span class="fc" id="L142">        log.info(&quot;开始执行证照数据ETL任务, taskName: {}&quot;, taskName);</span>
<span class="fc" id="L143">        long startTime = System.currentTimeMillis();</span>

        try {
<span class="fc" id="L146">            StringBuilder resultBuilder = new StringBuilder();</span>
            long costTime;

            // 0. 查询需要重处理的数据
<span class="fc" id="L150">            log.info(&quot;开始处理重处理任务&quot;);</span>
<span class="fc" id="L151">            List&lt;OdsCertificateData&gt; redoDataList = dwdbCertificateDataMapper.selectRedoData();</span>
<span class="pc bpc" id="L152" title="1 of 2 branches missed.">            if (!CollectionUtils.isEmpty(redoDataList)) {</span>
                // 使用单独的事务处理重处理数据
<span class="nc" id="L154">                processRedoData(redoDataList, resultBuilder);</span>
            }

            // 1、循环处理增量数据，直到没有新数据为止
            while (true) {
                // 2. 获取任务配置
<span class="fc" id="L160">                DataReceptionTask task = dataReceptionTaskMapper.getTaskByName(taskName);</span>
<span class="pc bpc" id="L161" title="1 of 2 branches missed.">                if (task == null) {</span>
<span class="nc" id="L162">                    task = initTask(taskName);</span>
                }

                // // 3. 计算时间范围
                // // 当前时间往前推5分钟，作为上限
                // LocalDateTime currentTimeMinus5Min = LocalDateTime.now().minusMinutes(5);
                // log.info(&quot;当前时间往前推5分钟: {}&quot;, currentTimeMinus5Min);
                //
                // // 如果当前时间往前推5分钟，小于task.getLastCompletedTime()，则退出任务
                // if (task.getLastCompletedTime() != null &amp;&amp;
                // currentTimeMinus5Min.isBefore(task.getLastCompletedTime())) {
                // log.warn(&quot;当前时间往前推5分钟({})小于上次任务完成时间({}), 任务退出&quot;,
                // currentTimeMinus5Min, task.getLastCompletedTime());
                // resultBuilder.append(&quot;任务提前退出: 当前时间往前推5分钟小于上次任务完成时间&quot;);
                // break;
                // }

                // // 将lastCompletedTime往前推5秒，作为下限
                // LocalDateTime adjustedLastCompletedTime = null;
                // if (task.getLastCompletedTime() != null) {
                // // 将时间往前推5秒，防止漏掉数据
                // adjustedLastCompletedTime = task.getLastCompletedTime().minusSeconds(5);
                // log.info(&quot;查询增量数据 - 原始时间: {}, 调整后时间(往前推5秒): {}&quot;,
                // task.getLastCompletedTime(), adjustedLastCompletedTime);
                // }

                // 3. 查询增量数据，增加fcdc_date上限条件
<span class="fc" id="L189">                List&lt;OdsCertificateData&gt; sourceDataList = odsCertificateDataMapper.selectIncrementalData(</span>
<span class="fc" id="L190">                        task.getLastCompletedTime(), task.getLastDataId());</span>

                // 如果没有增量数据了，退出循环
<span class="pc bpc" id="L193" title="1 of 2 branches missed.">                if (CollectionUtils.isEmpty(sourceDataList)) {</span>
<span class="fc" id="L194">                    break;</span>
                }

                // 分批处理数据，每批200条
<span class="nc" id="L198">                int batchSize = 200;</span>
<span class="nc" id="L199">                int totalSize = sourceDataList.size();</span>
<span class="nc" id="L200">                int batchCount = (totalSize + batchSize - 1) / batchSize; // 向上取整</span>

<span class="nc" id="L202">                log.info(&quot;开始分批处理增量数据，总数据量: {}，批次大小: {}，总批次数: {}&quot;,</span>
<span class="nc" id="L203">                        totalSize, batchSize, batchCount);</span>

<span class="nc bnc" id="L205" title="All 2 branches missed.">                for (int i = 0; i &lt; batchCount; i++) {</span>
<span class="nc" id="L206">                    int fromIndex = i * batchSize;</span>
<span class="nc" id="L207">                    int toIndex = Math.min((i + 1) * batchSize, totalSize);</span>
<span class="nc" id="L208">                    List&lt;OdsCertificateData&gt; batchDataList = sourceDataList.subList(fromIndex, toIndex);</span>

<span class="nc" id="L210">                    log.info(&quot;处理第 {} 批数据，数据量: {}&quot;, i + 1, batchDataList.size());</span>

                    // 使用单独的事务处理每批数据
<span class="nc" id="L213">                    processIncrementalData(taskName, batchDataList, resultBuilder);</span>

                    // 每处理一批数据后，休眠1秒钟，避免数据库压力过大
                    try {
<span class="nc" id="L217">                        Thread.sleep(1000);</span>
<span class="nc" id="L218">                    } catch (InterruptedException e) {</span>
<span class="nc" id="L219">                        log.error(&quot;线程休眠被中断&quot;, e);</span>
<span class="nc" id="L220">                    }</span>
                }

                // 每处理完一轮增量数据后，休眠5秒钟
                try {
<span class="nc" id="L225">                    Thread.sleep(5000);</span>
<span class="nc" id="L226">                } catch (InterruptedException e) {</span>
<span class="nc" id="L227">                    log.error(&quot;线程休眠被中断&quot;, e);</span>
<span class="nc" id="L228">                }</span>
<span class="nc" id="L229">            }</span>

<span class="fc" id="L231">            costTime = System.currentTimeMillis() - startTime;</span>
<span class="fc" id="L232">            log.info(&quot;{}, 耗时: {}毫秒&quot;, resultBuilder.toString(), costTime);</span>
<span class="fc" id="L233">            return resultBuilder.toString();</span>
<span class="nc" id="L234">        } catch (Exception e) {</span>
<span class="nc" id="L235">            log.error(&quot;ETL任务执行异常&quot;, e);</span>
<span class="nc" id="L236">            e.printStackTrace();</span>
<span class="nc" id="L237">            throw new RuntimeException(&quot;ETL任务执行失败: &quot; + e.getMessage());</span>
        }
    }

    @Transactional(rollbackFor = Exception.class)
    private void processRedoData(List&lt;OdsCertificateData&gt; redoDataList, StringBuilder resultBuilder) {
<span class="nc" id="L243">        int redoSuccessCount = 0;</span>
<span class="nc" id="L244">        int redoFailCount = 0;</span>

<span class="nc bnc" id="L246" title="All 2 branches missed.">        for (OdsCertificateData redoData : redoDataList) {</span>
            try {
<span class="nc" id="L248">                cleanupTargetData(redoData);</span>
<span class="nc" id="L249">                ProcessResult singleResult = processSourceData(Collections.singletonList(redoData));</span>

<span class="nc bnc" id="L251" title="All 2 branches missed.">                if (singleResult.isSuccess(redoData.getDataid())) {</span>
<span class="nc" id="L252">                    dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), &quot;1&quot;, null);</span>
<span class="nc" id="L253">                    redoSuccessCount++;</span>
                } else {
<span class="nc" id="L255">                    String failReason = singleResult.getFailReason(redoData.getDataid());</span>
<span class="nc" id="L256">                    dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), &quot;2&quot;, failReason);</span>
<span class="nc" id="L257">                    redoFailCount++;</span>
                }
<span class="nc" id="L259">            } catch (Exception e) {</span>
<span class="nc" id="L260">                log.error(&quot;重处理数据失败, dataId: &quot; + redoData.getDataid(), e);</span>
<span class="nc" id="L261">                dwdbCertificateDataMapper.updateRedoStatus(redoData.getDataid(), &quot;2&quot;,</span>
<span class="nc" id="L262">                        e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()));</span>
<span class="nc" id="L263">                redoFailCount++;</span>
<span class="nc" id="L264">            }</span>
<span class="nc" id="L265">        }</span>

<span class="nc" id="L267">        resultBuilder.append(String.format(&quot;重处理数据完成 - 总数: %d, 成功: %d, 失败: %d; &quot;,</span>
<span class="nc" id="L268">                redoDataList.size(), redoSuccessCount, redoFailCount));</span>
<span class="nc" id="L269">    }</span>

    @Transactional(rollbackFor = Exception.class)
    private void processIncrementalData(String taskName, List&lt;OdsCertificateData&gt; sourceDataList,
            StringBuilder resultBuilder) {
        // 处理增量数据
<span class="nc" id="L275">        ProcessResult incrementalResult = processSourceData(sourceDataList);</span>

        // 更新任务完成时间和最后数据ID
<span class="nc" id="L278">        OdsCertificateData lastRecord = sourceDataList.get(sourceDataList.size() - 1);</span>
<span class="nc" id="L279">        dataReceptionTaskMapper.updateLastCompletedTimeAndDataId(</span>
                taskName,
<span class="nc" id="L281">                lastRecord.getFcdcDate(),</span>
<span class="nc" id="L282">                lastRecord.getDataid());</span>
<span class="nc" id="L283">        log.info(&quot;更新任务完成时间为: {}, 最后数据ID: {}&quot;, lastRecord.getFcdcDate(), lastRecord.getDataid());</span>

<span class="nc" id="L285">        resultBuilder.append(String.format(&quot;增量数据处理完成 - 总数: %d, 成功: %d, 失败: %d; &quot;,</span>
<span class="nc" id="L286">                sourceDataList.size(), incrementalResult.getSuccessCount(), incrementalResult.getFailCount()));</span>
<span class="nc" id="L287">    }</span>

    /**
     * 处理源数据
     * 
     * @param sourceDataList 源数据列表
     * @return 处理结果
     */
    private ProcessResult processSourceData(List&lt;OdsCertificateData&gt; sourceDataList) {
<span class="nc" id="L296">        ProcessResult result = new ProcessResult();</span>

<span class="nc bnc" id="L298" title="All 4 branches missed.">        if (sourceDataList == null || sourceDataList.isEmpty()) {</span>
<span class="nc" id="L299">            return result;</span>
        }

<span class="nc" id="L302">        List&lt;DwdbCertificateData&gt; targetDataList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L303">        Map&lt;String, List&lt;Object&gt;&gt; subTableDataMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L304">        List&lt;DwdbCertificateDataAttribute&gt; unusedAttributesList = new ArrayList&lt;&gt;();</span>

<span class="nc bnc" id="L306" title="All 2 branches missed.">        for (OdsCertificateData sourceData : sourceDataList) {</span>
            try {
                // 1. 根据dataId查重，如果已存在则跳过处理
<span class="nc" id="L309">                String dataId = sourceData.getDataid();</span>
<span class="nc bnc" id="L310" title="All 2 branches missed.">                if (StringUtils.isNotBlank(dataId)) {</span>
<span class="nc" id="L311">                    boolean existsById = dwdbCertificateDataMapper.existsByDataId(dataId);</span>
<span class="nc bnc" id="L312" title="All 2 branches missed.">                    if (existsById) {</span>
<span class="nc" id="L313">                        log.info(&quot;跳过重复数据处理，数据ID已存在: {}&quot;, dataId);</span>
<span class="nc" id="L314">                        result.addSuccess(dataId); // 标记为成功，因为不需要处理</span>
<span class="nc" id="L315">                        continue; // 跳过此条数据的处理</span>
                    }
                }

                // 2. 根据证书ID查重，如果已存在则先清理再重新处理（更新数据）
<span class="nc" id="L320">                String certificateId = sourceData.getCertificateid();</span>
<span class="nc bnc" id="L321" title="All 2 branches missed.">                if (StringUtils.isNotBlank(certificateId)) {</span>
<span class="nc" id="L322">                    boolean existsByCertId = dwdbCertificateDataMapper.existsByCertificateId(certificateId);</span>
<span class="nc bnc" id="L323" title="All 2 branches missed.">                    if (existsByCertId) {</span>
<span class="nc" id="L324">                        log.info(&quot;发现证书ID重复数据，执行清理后重新处理，证书ID: {}, 数据ID: {}&quot;, certificateId, dataId);</span>
                        // 清理目标表中的数据，以便重新处理
<span class="nc" id="L326">                        cleanupTargetData(sourceData);</span>
                    }
                }

                // 处理机构查询辅助表，传入证书类型
<span class="nc" id="L331">                processQueryOrg(sourceData.getIssuedept(), sourceData.getCatalogname());</span>

                // 3. 转换主表数据
<span class="nc" id="L334">                DwdbCertificateData targetData = certificateConverter.convert(sourceData);</span>
<span class="nc" id="L335">                certificateConverter.validateRequiredFields(targetData);</span>
<span class="nc bnc" id="L336" title="All 2 branches missed.">                if (targetData != null) {</span>
<span class="nc" id="L337">                    targetDataList.add(targetData);</span>
                }

                // 4. 转换表面数据
<span class="nc" id="L341">                CertificateConvertResult convertResult = certificateConverter.convertSurfaceData(sourceData);</span>

<span class="nc bnc" id="L343" title="All 2 branches missed.">                if (!convertResult.isHasError()) {</span>
                    // 根据证书类型,从表面数据更新主表字段
<span class="nc bnc" id="L345" title="All 2 branches missed.">                    if (sourceData.getCatalogname().equals(&quot;游艇驾驶证&quot;)</span>
<span class="nc bnc" id="L346" title="All 2 branches missed.">                            || sourceData.getCatalogname().equals(&quot;游艇驾驶证（内河）&quot;)</span>
<span class="nc bnc" id="L347" title="All 2 branches missed.">                            || sourceData.getCatalogname().equals(&quot;游艇驾驶证（海上）&quot;)</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">                            || sourceData.getCatalogname().equals(&quot;游艇驾驶证海上&quot;)</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">                            || sourceData.getCatalogname().equals(&quot;游艇驾驶证内河&quot;)) {</span>
<span class="nc" id="L350">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailYtjsz) {</span>
<span class="nc" id="L352">                            DwdbCtfCertificateDetailYtjsz ytjsz = (DwdbCtfCertificateDetailYtjsz) mainData;</span>
<span class="nc" id="L353">                            targetData.setBirth(ytjsz.getDateOfBirth1());</span>
<span class="nc" id="L354">                            targetData.setBirthEn(ytjsz.getDateOfBirth2());</span>
<span class="nc" id="L355">                            targetData.setNameEn(ytjsz.getFullNameOfTheHolder2());</span>
<span class="nc" id="L356">                            targetData.setCountryCn(ytjsz.getNationality1());</span>
<span class="nc" id="L357">                            targetData.setCountryEn(ytjsz.getNationality2());</span>
<span class="nc" id="L358">                            targetData.setSignDeptEn(ytjsz.getSignDeptEn());</span>
<span class="nc" id="L359">                            targetData.setCertificateIssuedDateEn(ytjsz.getIssuedOn2());</span>
<span class="nc" id="L360">                            targetData.setQualificationCn(ytjsz.getQualificationCn());</span>
<span class="nc" id="L361">                            targetData.setCertificateExpiringDateEn(ytjsz.getDateOfExpiry2());</span>

                            // 游艇驾驶证主表的证照颁发机构有些数据名称不全，需要用照面表的数据刷回来
                            // 只有当新值非空时才更新
<span class="nc bnc" id="L365" title="All 2 branches missed.">                            if (StringUtils.isNotBlank(ytjsz.getSignDeptCn())) {</span>
<span class="nc" id="L366">                                targetData.setCertificateIssuingAuthorityName(ytjsz.getSignDeptCn());</span>
<span class="nc bnc" id="L367" title="All 2 branches missed.">                            } else if (StringUtils.isNotBlank(ytjsz.getOfficeOfIssueCn())) {</span>
<span class="nc" id="L368">                                targetData.setCertificateIssuingAuthorityName(ytjsz.getOfficeOfIssueCn());</span>
                            }
                            // 如果都为空，则保留原值，不做任何操作

                            // 根据资质代码设置证书名称
<span class="nc" id="L373">                            String qualificationCn = ytjsz.getQualificationCn();</span>
<span class="nc bnc" id="L374" title="All 2 branches missed.">                            if (qualificationCn != null) {</span>
<span class="nc bnc" id="L375" title="All 2 branches missed.">                                if (qualificationCn.startsWith(&quot;A&quot;)) {</span>
<span class="nc" id="L376">                                    targetData.setCertificateName(&quot;游艇驾驶证（海上）&quot;);</span>
<span class="nc bnc" id="L377" title="All 2 branches missed.">                                } else if (qualificationCn.startsWith(&quot;B&quot;)) {</span>
<span class="nc" id="L378">                                    targetData.setCertificateName(&quot;游艇驾驶证（内河）&quot;);</span>
                                }
                            } else {
                                // 根据证书类型名称设置证书名称
<span class="nc" id="L382">                                String catalogName = sourceData.getCatalogname();</span>
<span class="nc bnc" id="L383" title="All 2 branches missed.">                                if (&quot;游艇驾驶证海上&quot;.equals(catalogName)) {</span>
<span class="nc" id="L384">                                    targetData.setCertificateName(&quot;游艇驾驶证（海上）&quot;);</span>
<span class="nc bnc" id="L385" title="All 2 branches missed.">                                } else if (&quot;游艇驾驶证内河&quot;.equals(catalogName)) {</span>
<span class="nc" id="L386">                                    targetData.setCertificateName(&quot;游艇驾驶证（内河）&quot;);</span>
                                }
                            }
                        }
<span class="nc bnc" id="L390" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员内河航线行驶资格证明&quot;)) {</span>
<span class="nc" id="L391">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L392" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailNhhxxs) {</span>
<span class="nc" id="L393">                            DwdbCtfCertificateDetailNhhxxs nhhxxs = (DwdbCtfCertificateDetailNhhxxs) mainData;</span>
                            // 从身份证号提取出生年月日
<span class="nc" id="L395">                            String idCard = nhhxxs.getCreditCode();</span>
<span class="nc bnc" id="L396" title="All 4 branches missed.">                            if (idCard != null &amp;&amp; idCard.length() == 18) {</span>
<span class="nc" id="L397">                                targetData.setBirth(idCard.substring(6, 10) + &quot;-&quot; + idCard.substring(10, 12) + &quot;-&quot;</span>
<span class="nc" id="L398">                                        + idCard.substring(12, 14));</span>
                            }
<span class="nc" id="L400">                            targetData.setApplivationsCn(nhhxxs.getApplivations());</span>
                        }
<span class="nc bnc" id="L402" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;内河船舶船员适任证书&quot;)) {</span>
<span class="nc" id="L403">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L404" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailNhcbcy) {</span>
<span class="nc" id="L405">                            DwdbCtfCertificateDetailNhcbcy nhcbcy = (DwdbCtfCertificateDetailNhcbcy) mainData;</span>
<span class="nc" id="L406">                            targetData.setCrewType(nhcbcy.getType());</span>
<span class="nc" id="L407">                            targetData.setCertPrintNo(nhcbcy.getPrintNo());</span>

                            // 从身份证号提取出生年月日
<span class="nc" id="L410">                            String idCard = targetData.getCertificateHolderCode();</span>
<span class="nc bnc" id="L411" title="All 4 branches missed.">                            if (StringUtils.isNotBlank(idCard) &amp;&amp; idCard.length() == 18) {</span>
                                try {
<span class="nc" id="L413">                                    String birthYear = idCard.substring(6, 10);</span>
<span class="nc" id="L414">                                    String birthMonth = idCard.substring(10, 12);</span>
<span class="nc" id="L415">                                    String birthDay = idCard.substring(12, 14);</span>
<span class="nc" id="L416">                                    String birthDate = birthYear + &quot;-&quot; + birthMonth + &quot;-&quot; + birthDay;</span>
<span class="nc" id="L417">                                    targetData.setBirth(birthDate);</span>
                                    // log.info(&quot;从身份证号{}中提取出生日期: {}&quot;, idCard, birthDate);
<span class="nc" id="L419">                                } catch (Exception e) {</span>
<span class="nc" id="L420">                                    log.warn(&quot;从身份证号{}提取出生日期失败: {}&quot;, idCard, e.getMessage());</span>
<span class="nc" id="L421">                                }</span>
                            }
                        }
<span class="nc bnc" id="L424" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员培训合格证书&quot;)) {</span>
                        // 获取子表数据列表
<span class="nc" id="L426">                        List&lt;?&gt; trainingList = convertResult.getSubTableDataList(&quot;trainingList&quot;);</span>
<span class="nc bnc" id="L427" title="All 4 branches missed.">                        if (trainingList != null &amp;&amp; !trainingList.isEmpty()) {</span>
<span class="nc" id="L428">                            StringBuilder trainingNamesCn = new StringBuilder();</span>
<span class="nc" id="L429">                            StringBuilder trainingNamesEn = new StringBuilder();</span>
<span class="nc" id="L430">                            StringBuilder trainingIssueDatesCn = new StringBuilder();</span>
<span class="nc" id="L431">                            StringBuilder trainingIssueDatesEn = new StringBuilder();</span>
<span class="nc" id="L432">                            StringBuilder trainingEffectiveDatesCn = new StringBuilder();</span>
<span class="nc" id="L433">                            StringBuilder trainingEffectiveDatesEn = new StringBuilder();</span>

<span class="nc" id="L435">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L436" title="All 2 branches missed.">                            for (Object obj : trainingList) {</span>
<span class="nc" id="L437">                                DwdbCtfCertificateDetailHcpxhgTraining training = (DwdbCtfCertificateDetailHcpxhgTraining) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L440" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L441">                                    trainingNamesCn.append(&quot;#&quot;);</span>
<span class="nc" id="L442">                                    trainingNamesEn.append(&quot;#&quot;);</span>
<span class="nc" id="L443">                                    trainingIssueDatesCn.append(&quot;#&quot;);</span>
<span class="nc" id="L444">                                    trainingIssueDatesEn.append(&quot;#&quot;);</span>
<span class="nc" id="L445">                                    trainingEffectiveDatesCn.append(&quot;#&quot;);</span>
<span class="nc" id="L446">                                    trainingEffectiveDatesEn.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L448">                                    isFirst = false;</span>
                                }

                                // 添加培训项目名称
<span class="nc bnc" id="L452" title="All 2 branches missed.">                                trainingNamesCn.append(training.getTitleOfTheCertificate1() != null</span>
<span class="nc" id="L453">                                        ? training.getTitleOfTheCertificate1()</span>
                                        : &quot;&quot;);
<span class="nc bnc" id="L455" title="All 2 branches missed.">                                trainingNamesEn.append(training.getTitleOfTheCertificate2() != null</span>
<span class="nc" id="L456">                                        ? training.getTitleOfTheCertificate2()</span>
                                        : &quot;&quot;);

                                // 添加培训项目签发日期
<span class="nc" id="L460">                                trainingIssueDatesCn</span>
<span class="nc bnc" id="L461" title="All 2 branches missed.">                                        .append(training.getDateOfIssue1() != null ? training.getDateOfIssue1() : &quot;&quot;);</span>
<span class="nc" id="L462">                                trainingIssueDatesEn</span>
<span class="nc bnc" id="L463" title="All 2 branches missed.">                                        .append(training.getDateOfIssue2() != null ? training.getDateOfIssue2() : &quot;&quot;);</span>

                                // 添加培训项目有效期至
<span class="nc" id="L466">                                trainingEffectiveDatesCn</span>
<span class="nc bnc" id="L467" title="All 2 branches missed.">                                        .append(training.getDateOfExpiry1() != null ? training.getDateOfExpiry1() : &quot;&quot;);</span>
<span class="nc" id="L468">                                trainingEffectiveDatesEn</span>
<span class="nc bnc" id="L469" title="All 2 branches missed.">                                        .append(training.getDateOfExpiry2() != null ? training.getDateOfExpiry2() : &quot;&quot;);</span>
<span class="nc" id="L470">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L473">                            targetData.setTrainingNamesCn(trainingNamesCn.toString());</span>
<span class="nc" id="L474">                            targetData.setTrainingNamesEn(trainingNamesEn.toString());</span>
<span class="nc" id="L475">                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCn.toString());</span>
<span class="nc" id="L476">                            targetData.setTrainingIssueDatesEn(trainingIssueDatesEn.toString());</span>
<span class="nc" id="L477">                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCn.toString());</span>
<span class="nc" id="L478">                            targetData.setTrainingEffectiveDatesEn(trainingEffectiveDatesEn.toString());</span>
                        }

                        // 从主表数据中提取字段到targetData
<span class="nc" id="L482">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L483" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHcpxhg) {</span>
<span class="nc" id="L484">                            DwdbCtfCertificateDetailHcpxhg hcpxhg = (DwdbCtfCertificateDetailHcpxhg) mainData;</span>
<span class="nc" id="L485">                            targetData.setBirth(hcpxhg.getDateOfBirth1());</span>
<span class="nc" id="L486">                            targetData.setBirthEn(hcpxhg.getDateOfBirth2());</span>
<span class="nc" id="L487">                            targetData.setNameEn(hcpxhg.getFullNameOfTheHolder2());</span>
<span class="nc" id="L488">                            targetData.setCountryCn(hcpxhg.getNationality1());</span>
<span class="nc" id="L489">                            targetData.setCountryEn(hcpxhg.getNationality2());</span>
<span class="nc" id="L490">                            targetData.setSignDeptEn(hcpxhg.getOfficalSeal2());</span>
<span class="nc" id="L491">                            targetData.setCertificateIssuedDateEn(hcpxhg.getIssuedOn2());</span>
                        }
<span class="nc bnc" id="L493" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船高级船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L495">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L496" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHcgjcy) {</span>
<span class="nc" id="L497">                            DwdbCtfCertificateDetailHcgjcy hcgjcy = (DwdbCtfCertificateDetailHcgjcy) mainData;</span>
<span class="nc" id="L498">                            targetData.setBirth(hcgjcy.getDateOfBirth1());</span>
<span class="nc" id="L499">                            targetData.setBirthEn(hcgjcy.getDateOfBirth2());</span>
<span class="nc" id="L500">                            targetData.setNameEn(hcgjcy.getFullNameOfTheHolder2());</span>
<span class="nc" id="L501">                            targetData.setCountryCn(hcgjcy.getNationality1());</span>
<span class="nc" id="L502">                            targetData.setCountryEn(hcgjcy.getNationality2());</span>
<span class="nc" id="L503">                            targetData.setSignDeptEn(hcgjcy.getIssuingAuthority2());</span>
<span class="nc" id="L504">                            targetData.setCertificateIssuedDateEn(hcgjcy.getDateOfIssue2());</span>
<span class="nc" id="L505">                            targetData.setCertificateExpiringDateEn(hcgjcy.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L509">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L510" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L511">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L512">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L514">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L515" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L516">                                DwdbCtfCertificateDetailHcgjcyCapacity capacity = (DwdbCtfCertificateDetailHcgjcyCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L519" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null</span>
<span class="nc" id="L520">                                        ? capacity.getGradwAndCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L522" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null</span>
<span class="nc" id="L523">                                        ? capacity.getGradwAndCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L527" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L528">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L532" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L533">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L534">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L536">                                    isFirst = false;</span>
                                }

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L540">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L541">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L544">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L545">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L546">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L549">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L550">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L552" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船普通船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L554">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L555" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHcptcysrz) {</span>
<span class="nc" id="L556">                            DwdbCtfCertificateDetailHcptcysrz hcptcysrz = (DwdbCtfCertificateDetailHcptcysrz) mainData;</span>
<span class="nc" id="L557">                            targetData.setBirth(hcptcysrz.getDateOfBirth1());</span>
<span class="nc" id="L558">                            targetData.setBirthEn(hcptcysrz.getDateOfBirth2());</span>
<span class="nc" id="L559">                            targetData.setNameEn(hcptcysrz.getFullNameOfTheHolder2());</span>
<span class="nc" id="L560">                            targetData.setCountryCn(hcptcysrz.getNationality1());</span>
<span class="nc" id="L561">                            targetData.setCountryEn(hcptcysrz.getNationality2());</span>
<span class="nc" id="L562">                            targetData.setSignDeptEn(hcptcysrz.getIssuingAuthority2());</span>
<span class="nc" id="L563">                            targetData.setCertificateIssuedDateEn(hcptcysrz.getDateOfIssue2());</span>
<span class="nc" id="L564">                            targetData.setCertificateExpiringDateEn(hcptcysrz.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L568">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L569" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L570">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L571">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L573">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L574" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L575">                                DwdbCtfCertificateDetailHcptcysrzCapacity capacity = (DwdbCtfCertificateDetailHcptcysrzCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L578" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null</span>
<span class="nc" id="L579">                                        ? capacity.getGradwAndCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L581" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null</span>
<span class="nc" id="L582">                                        ? capacity.getGradwAndCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L586" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L587">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L591" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L592">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L593">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L595">                                    isFirst = false;</span>
                                }

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L599">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L600">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L603">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L604">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L605">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L608">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L609">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L611" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;小型海船适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L613">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L614" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailXhcsrz) {</span>
<span class="nc" id="L615">                            DwdbCtfCertificateDetailXhcsrz xhcsrz = (DwdbCtfCertificateDetailXhcsrz) mainData;</span>
<span class="nc" id="L616">                            targetData.setBirth(xhcsrz.getDateOfBirth1());</span>
<span class="nc" id="L617">                            targetData.setBirthEn(xhcsrz.getDateOfBirth2());</span>
<span class="nc" id="L618">                            targetData.setNameEn(xhcsrz.getFullNameOfTheHolder2());</span>
<span class="nc" id="L619">                            targetData.setCountryCn(xhcsrz.getNationality1());</span>
<span class="nc" id="L620">                            targetData.setCountryEn(xhcsrz.getNationality2());</span>
<span class="nc" id="L621">                            targetData.setSignDeptEn(xhcsrz.getIssuingAuthority2());</span>
<span class="nc" id="L622">                            targetData.setCertificateIssuedDateEn(xhcsrz.getDateOfIssue2());</span>
<span class="nc" id="L623">                            targetData.setCertificateExpiringDateEn(xhcsrz.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L627">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L628" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L629">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L630">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L632">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L633" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L634">                                DwdbCtfCertificateDetailXhcsrzCapacity capacity = (DwdbCtfCertificateDetailXhcsrzCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L637" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null</span>
<span class="nc" id="L638">                                        ? capacity.getGradwAndCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L640" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null</span>
<span class="nc" id="L641">                                        ? capacity.getGradwAndCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L645" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L646">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L650" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L651">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L652">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L654">                                    isFirst = false;</span>
                                }

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L658">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L659">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L662">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L663">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L664">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L667">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L668">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L670" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员适任证书承认签证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L672">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L673" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHccycr) {</span>
<span class="nc" id="L674">                            DwdbCtfCertificateDetailHccycr hccycr = (DwdbCtfCertificateDetailHccycr) mainData;</span>
<span class="nc" id="L675">                            targetData.setBirth(hccycr.getDateOfBirth1());</span>
<span class="nc" id="L676">                            targetData.setBirthEn(hccycr.getDateOfBirth2());</span>
<span class="nc" id="L677">                            targetData.setNameEn(hccycr.getHolderName2());</span>
<span class="nc" id="L678">                            targetData.setCountryCn(hccycr.getNationality1());</span>
<span class="nc" id="L679">                            targetData.setCountryEn(hccycr.getNationality2());</span>
<span class="nc" id="L680">                            targetData.setSignDeptEn(hccycr.getIssuingAminstration2());</span>
<span class="nc" id="L681">                            targetData.setCertificateIssuedDateEn(hccycr.getIssuedOn2());</span>
<span class="nc" id="L682">                            targetData.setCertificateExpiringDateEn(hccycr.getDateOfExpiry2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L686">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L687" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L688">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L689">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L691">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L692" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L693">                                DwdbCtfCertificateDetailHccycrCapacity capacity = (DwdbCtfCertificateDetailHccycrCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L696" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L700" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L701">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L705" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L706">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L707">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L709">                                    isFirst = false;</span>
                                }

<span class="nc bnc" id="L712" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()</span>
                                        : &quot;&quot;;

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L716">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L717">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L720">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L721">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L722">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L725">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L726">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L728" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;不参加航行和轮机值班海船船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L730">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L731" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailBcjhljzb) {</span>
<span class="nc" id="L732">                            DwdbCtfCertificateDetailBcjhljzb bcjhljzb = (DwdbCtfCertificateDetailBcjhljzb) mainData;</span>
<span class="nc" id="L733">                            targetData.setBirth(bcjhljzb.getDateOfBirth1());</span>
<span class="nc" id="L734">                            targetData.setBirthEn(bcjhljzb.getDateOfBirth2());</span>
<span class="nc" id="L735">                            targetData.setNameEn(bcjhljzb.getFullNameoftheHolder2());</span>
<span class="nc" id="L736">                            targetData.setCountryCn(bcjhljzb.getNationality1());</span>
<span class="nc" id="L737">                            targetData.setCountryEn(bcjhljzb.getNationality2());</span>
<span class="nc" id="L738">                            targetData.setSignDeptEn(bcjhljzb.getIssuingAuthority2());</span>
<span class="nc" id="L739">                            targetData.setCertificateIssuedDateEn(bcjhljzb.getCertificateIssuedDate2());</span>
<span class="nc" id="L740">                            targetData.setCertificateExpiringDateEn(bcjhljzb.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L744">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L745" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L746">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L747">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L749">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L750" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L751">                                DwdbCtfCertificateDetailBcjhljzbCapacity capacity = (DwdbCtfCertificateDetailBcjhljzbCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L754" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L756" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L760" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L761">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L765" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L766">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L767">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L769">                                    isFirst = false;</span>
                                }

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L773">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L774">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L777">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L778">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L779">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L782">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L783">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L785" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海上非自航船舶船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L787">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L788" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHsfhcysrz) {</span>
<span class="nc" id="L789">                            DwdbCtfCertificateDetailHsfhcysrz hsfhcysrz = (DwdbCtfCertificateDetailHsfhcysrz) mainData;</span>
<span class="nc" id="L790">                            targetData.setBirth(hsfhcysrz.getDateOfBirth());</span>
<span class="nc" id="L791">                            targetData.setCountryCn(hsfhcysrz.getPlaceOfBirth());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L795">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;shipList&quot;);</span>
<span class="nc bnc" id="L796" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L797">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>

<span class="nc" id="L799">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L800" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L801">                                DwdbCtfCertificateDetailHsfhcysrzShip capacity = (DwdbCtfCertificateDetailHsfhcysrzShip) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L804" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L805">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L807">                                    isFirst = false;</span>
                                }

                                // 添加职务等级信息
<span class="nc bnc" id="L811" title="All 2 branches missed.">                                crewTypeBuilder.append(capacity.getCapacity() != null ? capacity.getCapacity() : &quot;&quot;);</span>
<span class="nc" id="L812">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L815">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
                        }
<span class="nc bnc" id="L817" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;引航员船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L819">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L820" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailYhysrz) {</span>
<span class="nc" id="L821">                            DwdbCtfCertificateDetailYhysrz yhysrz = (DwdbCtfCertificateDetailYhysrz) mainData;</span>
<span class="nc" id="L822">                            targetData.setBirth(yhysrz.getDateOfBirth1());</span>
<span class="nc" id="L823">                            targetData.setBirthEn(yhysrz.getDateOfBirth2());</span>
<span class="nc" id="L824">                            targetData.setNameEn(yhysrz.getFullNameOfTheHolder2());</span>
<span class="nc" id="L825">                            targetData.setCountryCn(yhysrz.getNationality1());</span>
<span class="nc" id="L826">                            targetData.setCountryEn(yhysrz.getNationality2());</span>
<span class="nc" id="L827">                            targetData.setSignDeptEn(yhysrz.getIssuingAuthority2());</span>
<span class="nc" id="L828">                            targetData.setCertificateIssuedDateEn(yhysrz.getDateOfIssue2());</span>
<span class="nc" id="L829">                            targetData.setCertificateExpiringDateEn(yhysrz.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L833">                        List&lt;?&gt; rangeList = convertResult.getSubTableDataList(&quot;rangeList&quot;);</span>
<span class="nc bnc" id="L834" title="All 4 branches missed.">                        if (rangeList != null &amp;&amp; !rangeList.isEmpty()) {</span>
<span class="nc" id="L835">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L836">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L838">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L839" title="All 2 branches missed.">                            for (Object obj : rangeList) {</span>
<span class="nc" id="L840">                                DwdbCtfCertificateDetailYhysrzRange range = (DwdbCtfCertificateDetailYhysrzRange) obj;</span>

                                // 获取原始职务（组合type和level）
<span class="nc bnc" id="L843" title="All 2 branches missed.">                                String type1 = range.getType1() != null ? range.getType1() : &quot;&quot;;</span>

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L846" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(type1)) {</span>
<span class="nc" id="L847">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L851" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L852">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L853">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L855">                                    isFirst = false;</span>
                                }

<span class="nc bnc" id="L858" title="All 2 branches missed.">                                String level1 = range.getLevel1() != null ? range.getLevel1() : &quot;&quot;;</span>
<span class="nc bnc" id="L859" title="All 2 branches missed.">                                String type2 = range.getType2() != null ? range.getType2() : &quot;&quot;;</span>
<span class="nc bnc" id="L860" title="All 2 branches missed.">                                String level2 = range.getLevel2() != null ? range.getLevel2() : &quot;&quot;;</span>

<span class="nc" id="L862">                                String originalCapacityCn = type1 + level1;</span>
<span class="nc" id="L863">                                String originalCapacityEn = type2 + level2;</span>

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L866">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L867">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L870">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L871">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L872">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L875">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L876">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L878" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;公务船船员适任证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L880">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L881" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailGwccy) {</span>
<span class="nc" id="L882">                            DwdbCtfCertificateDetailGwccy gwccy = (DwdbCtfCertificateDetailGwccy) mainData;</span>
<span class="nc" id="L883">                            targetData.setBirth(gwccy.getDateOfBirth1());</span>
<span class="nc" id="L884">                            targetData.setBirthEn(gwccy.getDateOfBirth2());</span>
<span class="nc" id="L885">                            targetData.setNameEn(gwccy.getFullNameoftheHolder2());</span>
<span class="nc" id="L886">                            targetData.setCountryCn(gwccy.getNationality1());</span>
<span class="nc" id="L887">                            targetData.setCountryEn(gwccy.getNationality2());</span>
<span class="nc" id="L888">                            targetData.setSignDeptEn(gwccy.getIssuingAuthority2());</span>
<span class="nc" id="L889">                            targetData.setCertificateIssuedDateEn(gwccy.getDateOfIssue2());</span>
<span class="nc" id="L890">                            targetData.setCertificateExpiringDateEn(gwccy.getCertificateExpiringDate2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L894">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacities&quot;);</span>
<span class="nc bnc" id="L895" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L896">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L897">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L899">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L900" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L901">                                DwdbCtfCertificateDetailGwccyCapacity capacity = (DwdbCtfCertificateDetailGwccyCapacity) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L904" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getGradwAndCapacity1() != null</span>
<span class="nc" id="L905">                                        ? capacity.getGradwAndCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L907" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getGradwAndCapacity2() != null</span>
<span class="nc" id="L908">                                        ? capacity.getGradwAndCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L912" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L913">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L917" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L918">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L919">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L921">                                    isFirst = false;</span>
                                }

                                // 从职务映射缓存中查找目标职务
<span class="nc" id="L925">                                String mappedCapacityCn = getMappedCapacity(originalCapacityCn);</span>
<span class="nc" id="L926">                                String mappedCapacityEn = getMappedCapacity(originalCapacityEn);</span>

                                // 添加职务等级信息
<span class="nc" id="L929">                                crewTypeBuilder.append(mappedCapacityCn);</span>
<span class="nc" id="L930">                                crewTypeEnBuilder.append(mappedCapacityEn);</span>
<span class="nc" id="L931">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L934">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L935">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L937" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;内河船舶船员培训合格证&quot;)</span>
<span class="nc bnc" id="L938" title="All 2 branches missed.">                            || sourceData.getCatalogname().equals(&quot;内河船舶船员特殊培训合格证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L940">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L941" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailNhpxhg) {</span>
<span class="nc" id="L942">                            DwdbCtfCertificateDetailNhpxhg nhpxhg = (DwdbCtfCertificateDetailNhpxhg) mainData;</span>
<span class="nc" id="L943">                            targetData.setCertPrintNo(nhpxhg.getPrintNo());</span>
                        }

                        // 处理培训项目信息
<span class="nc" id="L947">                        List&lt;?&gt; nhpxhgItems = convertResult.getSubTableDataList(&quot;nhpxhgItems&quot;);</span>
<span class="nc bnc" id="L948" title="All 4 branches missed.">                        if (nhpxhgItems != null &amp;&amp; !nhpxhgItems.isEmpty()) {</span>
<span class="nc" id="L949">                            StringBuilder trainingNamesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L950">                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L951">                            StringBuilder trainingEffectiveDatesCnBuilder = new StringBuilder();</span>

<span class="nc" id="L953">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L954" title="All 2 branches missed.">                            for (Object obj : nhpxhgItems) {</span>
<span class="nc" id="L955">                                DwdbCtfCertificateDetailNhpxhgItem item = (DwdbCtfCertificateDetailNhpxhgItem) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L958" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L959">                                    trainingNamesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L960">                                    trainingIssueDatesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L961">                                    trainingEffectiveDatesCnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L963">                                    isFirst = false;</span>
                                }

                                // 添加培训项目信息
<span class="nc bnc" id="L967" title="All 2 branches missed.">                                trainingNamesCnBuilder.append(item.getProject() != null ? item.getProject() : &quot;&quot;);</span>
<span class="nc" id="L968">                                trainingIssueDatesCnBuilder</span>
<span class="nc bnc" id="L969" title="All 2 branches missed.">                                        .append(item.getSignDate() != null ? item.getSignDate() : &quot;&quot;);</span>
<span class="nc" id="L970">                                trainingEffectiveDatesCnBuilder</span>
<span class="nc bnc" id="L971" title="All 2 branches missed.">                                        .append(item.getEndDate() != null ? item.getEndDate() : &quot;&quot;);</span>
<span class="nc" id="L972">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L975">                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());</span>
<span class="nc" id="L976">                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());</span>
<span class="nc" id="L977">                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCnBuilder.toString());</span>
                        }

                        // 从身份证号提取出生年月日
<span class="nc" id="L981">                        String idCard = targetData.getCertificateHolderCode();</span>
<span class="nc bnc" id="L982" title="All 4 branches missed.">                        if (StringUtils.isNotBlank(idCard) &amp;&amp; idCard.length() == 18) {</span>
                            try {
<span class="nc" id="L984">                                String birthYear = idCard.substring(6, 10);</span>
<span class="nc" id="L985">                                String birthMonth = idCard.substring(10, 12);</span>
<span class="nc" id="L986">                                String birthDay = idCard.substring(12, 14);</span>
<span class="nc" id="L987">                                String birthDate = birthYear + &quot;-&quot; + birthMonth + &quot;-&quot; + birthDay;</span>
<span class="nc" id="L988">                                targetData.setBirth(birthDate);</span>
                                // log.info(&quot;从身份证号{}中提取出生日期: {}&quot;, idCard, birthDate);
<span class="nc" id="L990">                            } catch (Exception e) {</span>
<span class="nc" id="L991">                                log.warn(&quot;从身份证号{}提取出生日期失败: {}&quot;, idCard, e.getMessage());</span>
<span class="nc" id="L992">                            }</span>
                        }
<span class="nc" id="L994">                        targetData.setCertificateName(&quot;内河船舶船员培训合格证&quot;);</span>
<span class="nc bnc" id="L995" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;特定航线江海直达船舶船员行驶资格证明培训合格证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L997">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L998" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailTdhxjh) {</span>
<span class="nc" id="L999">                            DwdbCtfCertificateDetailTdhxjh tdhxjh = (DwdbCtfCertificateDetailTdhxjh) mainData;</span>
<span class="nc" id="L1000">                            targetData.setBirth(tdhxjh.getDateOfBirth());</span>
<span class="nc" id="L1001">                            targetData.setApplivationsCn(tdhxjh.getApplivations());</span>
<span class="nc" id="L1002">                            targetData.setCertificateExpiringDateEn(tdhxjh.getIssuingDate());// 初次签发日期</span>
                        }
<span class="nc bnc" id="L1004" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;船上厨师培训合格证明&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1006">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1007" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailCscspx) {</span>
<span class="nc" id="L1008">                            DwdbCtfCertificateDetailCscspx cscspx = (DwdbCtfCertificateDetailCscspx) mainData;</span>
<span class="nc" id="L1009">                            targetData.setBirth(cscspx.getDateOfBirth1());</span>
<span class="nc" id="L1010">                            targetData.setBirthEn(cscspx.getDateOfBirth2());</span>
<span class="nc" id="L1011">                            targetData.setNameEn(cscspx.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1012">                            targetData.setCountryCn(cscspx.getNationality1());</span>
<span class="nc" id="L1013">                            targetData.setCountryEn(cscspx.getNationality2());</span>
<span class="nc" id="L1014">                            targetData.setSignDeptEn(cscspx.getIssuingBody2());</span>
<span class="nc" id="L1015">                            targetData.setCertificateIssuedDateEn(cscspx.getDateOfIssue2());</span>
<span class="nc" id="L1016">                            targetData.setTrainManagerNameCn(cscspx.getNameOfTheTraingManager1());</span>
<span class="nc" id="L1017">                            targetData.setTrainManagerNameEn(cscspx.getNameOfTheTraingManager2());</span>
                        }
<span class="nc bnc" id="L1019" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;船上膳食服务辅助人员培训证明&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1021">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1022" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailCsssfzpx) {</span>
<span class="nc" id="L1023">                            DwdbCtfCertificateDetailCsssfzpx csssfzpx = (DwdbCtfCertificateDetailCsssfzpx) mainData;</span>
<span class="nc" id="L1024">                            targetData.setBirth(csssfzpx.getDateOfBirth1());</span>
<span class="nc" id="L1025">                            targetData.setBirthEn(csssfzpx.getDateOfBirth2());</span>
<span class="nc" id="L1026">                            targetData.setNameEn(csssfzpx.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1027">                            targetData.setCountryCn(csssfzpx.getNationality1());</span>
<span class="nc" id="L1028">                            targetData.setCountryEn(csssfzpx.getNationality2());</span>
<span class="nc" id="L1029">                            targetData.setSignDeptEn(csssfzpx.getIssuingBody2());</span>
<span class="nc" id="L1030">                            targetData.setCertificateIssuedDateEn(csssfzpx.getDateOfIssue2());</span>
<span class="nc" id="L1031">                            targetData.setTrainManagerNameCn(csssfzpx.getNameOfTheTraingManager1());</span>
<span class="nc" id="L1032">                            targetData.setTrainManagerNameEn(csssfzpx.getNameOfTheTraingManager2());</span>
                        }
<span class="nc bnc" id="L1034" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海上设施工作人员海上交通安全技能培训合格证明&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1036">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1037" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHsssjn) {</span>
<span class="nc" id="L1038">                            DwdbCtfCertificateDetailHsssjn hsssjn = (DwdbCtfCertificateDetailHsssjn) mainData;</span>
<span class="nc" id="L1039">                            targetData.setBirth(hsssjn.getDateOfBirth1());</span>
<span class="nc" id="L1040">                            targetData.setBirthEn(hsssjn.getDateOfBirth2());</span>
<span class="nc" id="L1041">                            targetData.setNameEn(hsssjn.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1042">                            targetData.setCountryCn(hsssjn.getNationality1());</span>
<span class="nc" id="L1043">                            targetData.setCountryEn(hsssjn.getNationality2());</span>
<span class="nc" id="L1044">                            targetData.setTrainingNamesCn(&quot;基础培训（消防、救生）#专业培训（避碰、信号、通信）&quot;);</span>
<span class="nc" id="L1045">                            targetData.setTrainingIssueDatesCn(</span>
<span class="nc" id="L1046">                                    hsssjn.getAnThorityName1() + &quot;#&quot; + hsssjn.getAnThorityName2());</span>
<span class="nc" id="L1047">                            targetData.setTrainingEffectiveDatesCn(&quot;长期#&quot; + hsssjn.getYear1() + &quot;年&quot; + hsssjn.getMonth1()</span>
<span class="nc" id="L1048">                                    + &quot;月&quot; + hsssjn.getDay1() + &quot;日&quot;);</span>
                        }
<span class="nc bnc" id="L1050" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员健康证明&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1052">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1053" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailJkzm) {</span>
<span class="nc" id="L1054">                            DwdbCtfCertificateDetailJkzm jkzm = (DwdbCtfCertificateDetailJkzm) mainData;</span>
<span class="nc" id="L1055">                            targetData.setBirth(jkzm.getDateOfBirth1());</span>
<span class="nc" id="L1056">                            targetData.setBirthEn(jkzm.getDateOfBirth2());</span>
<span class="nc" id="L1057">                            targetData.setNameEn(jkzm.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1058">                            targetData.setCountryCn(jkzm.getNationality1());</span>
<span class="nc" id="L1059">                            targetData.setCountryEn(jkzm.getNationality2());</span>
<span class="nc" id="L1060">                            targetData.setSignDeptEn(jkzm.getIssuingAuthority2());</span>
<span class="nc" id="L1061">                            targetData.setCertificateIssuedDateEn(jkzm.getDateOfIssue2());</span>
<span class="nc" id="L1062">                            targetData.setCertificateExpiringDateEn(jkzm.getCertificateExpiringDate2());</span>
<span class="nc" id="L1063">                            targetData.setAuthAuthorityCn(jkzm.getAuthorizingAuthority1());</span>
<span class="nc" id="L1064">                            targetData.setAuthAuthorityEn(jkzm.getAuthorizingAuthority2());</span>
                        }
<span class="nc bnc" id="L1066" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;船员培训质量管理体系证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1068">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1069" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailQms) {</span>
<span class="nc" id="L1070">                            DwdbCtfCertificateDetailQms qms = (DwdbCtfCertificateDetailQms) mainData;</span>
<span class="nc" id="L1071">                            targetData.setNameEn(qms.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1072">                            targetData.setSignDeptEn(qms.getEvaluationOrganization2());</span>
<span class="nc" id="L1073">                            targetData.setCertificateIssuedDateEn(qms.getDateOfIssue2());</span>
<span class="nc" id="L1074">                            targetData.setCertificateExpiringDateEn(qms.getCertificateExpiringDate());</span>
<span class="nc" id="L1075">                            targetData.setEvaOrgCn(qms.getEvaluationOrganization1());</span>
<span class="nc" id="L1076">                            targetData.setEvaOrgEn(qms.getEvaluationOrganization2());</span>
                        }
<span class="nc bnc" id="L1078" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海员外派机构资质证书&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1080">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1081" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailHywpjg) {</span>
<span class="nc" id="L1082">                            DwdbCtfCertificateDetailHywpjg hywpjg = (DwdbCtfCertificateDetailHywpjg) mainData;</span>
<span class="nc" id="L1083">                            targetData.setNameEn(hywpjg.getAnThorityName2());</span>
<span class="nc" id="L1084">                            targetData.setSignDeptEn(hywpjg.getIssuingAuthority2());</span>
<span class="nc" id="L1085">                            targetData.setCertificateIssuedDateEn(hywpjg.getDateOfIssue3());</span>
<span class="nc" id="L1086">                            targetData.setCertificateExpiringDateEn(hywpjg.getExpiryDate2());</span>
<span class="nc" id="L1087">                            targetData.setRepresentativeCn(hywpjg.getRepresentative1());</span>
<span class="nc" id="L1088">                            targetData.setRepresentativeEn(hywpjg.getRepresentative2());</span>
<span class="nc" id="L1089">                            targetData.setEvaOrgCn(hywpjg.getAnThorityName1()); // 机构名称-中文</span>
<span class="nc" id="L1090">                            targetData.setEvaOrgEn(hywpjg.getAnThorityName2()); // 机构名称-英文</span>
<span class="nc" id="L1091">                            targetData.setAuthAuthorityCn(hywpjg.getAddress1());// 机构地址-中文</span>
<span class="nc" id="L1092">                            targetData.setAuthAuthorityEn(hywpjg.getAddress2());// 机构地址-英文</span>
<span class="nc" id="L1093">                            targetData.setApplivationsCn(&quot;为外国籍或港澳台地区籍船舶提供配员。&quot;);// 服务范围</span>
<span class="nc" id="L1094">                            targetData.setApplivationsEn(</span>
                                    &quot;Crew manning for Vessels flying flags of foreign countries or Hong Kong, Macao and Taiwan.&quot;);
                        }
<span class="nc bnc" id="L1097" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员培训许可证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1099">                        List&lt;?&gt; lists = convertResult.getSubTableDataList(&quot;seamanPermit&quot;);</span>
<span class="nc" id="L1100">                        Object mainData = lists.get(0);</span>
<span class="nc bnc" id="L1101" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailSeamanPermit) {</span>
<span class="nc" id="L1102">                            DwdbCtfCertificateDetailSeamanPermit permit = (DwdbCtfCertificateDetailSeamanPermit) mainData;</span>
<span class="nc" id="L1103">                            targetData.setRepresentativeCn(permit.getRepresentative1());</span>
<span class="nc" id="L1104">                            targetData.setTrainingEffectiveDatesCn(</span>
<span class="nc" id="L1105">                                    permit.getTrainingProgram1() + permit.getTrainingProgram2());</span>
<span class="nc" id="L1106">                            targetData.setTrainingInstitutionCode(permit.getTrainingInstitutionCode1());</span>
<span class="nc" id="L1107">                            targetData.setTrainingLocation(permit.getTrainingLocation1());</span>
                        }

                        // 处理培训项目信息
<span class="nc" id="L1111">                        List&lt;?&gt; itemList = convertResult.getSubTableDataList(&quot;itemList&quot;);</span>
<span class="nc bnc" id="L1112" title="All 4 branches missed.">                        if (itemList != null &amp;&amp; !itemList.isEmpty()) {</span>
<span class="nc" id="L1113">                            StringBuilder trainingNamesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L1114">                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();</span>

<span class="nc" id="L1116">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L1117" title="All 2 branches missed.">                            for (Object obj : itemList) {</span>
<span class="nc" id="L1118">                                DwdbCtfCertificateDetailSeamanPermitItem item = (DwdbCtfCertificateDetailSeamanPermitItem) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L1121" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L1122">                                    trainingNamesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1123">                                    trainingIssueDatesCnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L1125">                                    isFirst = false;</span>
                                }

                                // 添加培训项目信息
<span class="nc" id="L1129">                                trainingNamesCnBuilder</span>
<span class="nc bnc" id="L1130" title="All 2 branches missed.">                                        .append(item.getAtrainingProgram() != null ? item.getAtrainingProgram() : &quot;&quot;);</span>
<span class="nc" id="L1131">                                trainingIssueDatesCnBuilder</span>
<span class="nc bnc" id="L1132" title="All 2 branches missed.">                                        .append(item.getTrainingScale() != null ? item.getTrainingScale() : &quot;&quot;);</span>
<span class="nc" id="L1133">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L1136">                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());</span>
<span class="nc" id="L1137">                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L1139" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;内河船员培训许可证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1141">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1142" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertificateDetailNhcyxkz) {</span>
<span class="nc" id="L1143">                            DwdbCtfCertificateDetailNhcyxkz nhcyxkz = (DwdbCtfCertificateDetailNhcyxkz) mainData;</span>
<span class="nc" id="L1144">                            targetData.setRepresentativeCn(nhcyxkz.getRepresentative1());</span>
<span class="nc" id="L1145">                            targetData.setTrainingEffectiveDatesCn(</span>
<span class="nc" id="L1146">                                    nhcyxkz.getTrainingProgram1() + nhcyxkz.getTrainingProgram2());</span>
<span class="nc" id="L1147">                            targetData.setTrainingInstitutionCode(nhcyxkz.getTrainingInstitutionCode1());</span>
<span class="nc" id="L1148">                            targetData.setTrainingLocation(nhcyxkz.getTrainingLocation1());</span>
                        }

                        // 处理培训项目信息
<span class="nc" id="L1152">                        List&lt;?&gt; itemList = convertResult.getSubTableDataList(&quot;itemList&quot;);</span>
<span class="nc bnc" id="L1153" title="All 4 branches missed.">                        if (itemList != null &amp;&amp; !itemList.isEmpty()) {</span>
<span class="nc" id="L1154">                            StringBuilder trainingNamesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L1155">                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();</span>

<span class="nc" id="L1157">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L1158" title="All 2 branches missed.">                            for (Object obj : itemList) {</span>
<span class="nc" id="L1159">                                DwdbCtfCertificateDetailNhcyxkzItem item = (DwdbCtfCertificateDetailNhcyxkzItem) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L1162" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L1163">                                    trainingNamesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1164">                                    trainingIssueDatesCnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L1166">                                    isFirst = false;</span>
                                }

                                // 添加培训项目信息
<span class="nc" id="L1170">                                trainingNamesCnBuilder</span>
<span class="nc bnc" id="L1171" title="All 2 branches missed.">                                        .append(item.getAtrainingProgram() != null ? item.getAtrainingProgram() : &quot;&quot;);</span>
<span class="nc" id="L1172">                                trainingIssueDatesCnBuilder</span>
<span class="nc bnc" id="L1173" title="All 2 branches missed.">                                        .append(item.getTrainingScale() != null ? item.getTrainingScale() : &quot;&quot;);</span>
<span class="nc" id="L1174">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L1177">                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());</span>
<span class="nc" id="L1178">                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L1180" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员培训合格证承认签证&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1182">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1183" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertDetailHcpxhgqz) {</span>
<span class="nc" id="L1184">                            DwdbCtfCertDetailHcpxhgqz hcpxhgqz = (DwdbCtfCertDetailHcpxhgqz) mainData;</span>
<span class="nc" id="L1185">                            targetData.setBirth(hcpxhgqz.getDateOfBirth1());</span>
<span class="nc" id="L1186">                            targetData.setBirthEn(hcpxhgqz.getDateOfBirth2());</span>
<span class="nc" id="L1187">                            targetData.setNameEn(hcpxhgqz.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1188">                            targetData.setCountryCn(hcpxhgqz.getNationality1());</span>
<span class="nc" id="L1189">                            targetData.setCountryEn(hcpxhgqz.getNationality2());</span>
<span class="nc" id="L1190">                            targetData.setSignDeptEn(hcpxhgqz.getIssuingAminstration2());</span>
<span class="nc" id="L1191">                            targetData.setCertificateIssuedDateEn(hcpxhgqz.getIssuedOn2());</span>
<span class="nc" id="L1192">                            targetData.setCertificateExpiringDateEn(hcpxhgqz.getDateOfExpiry2());</span>
                        }

                        // 处理培训项目信息
<span class="nc" id="L1196">                        List&lt;?&gt; trainingList = convertResult.getSubTableDataList(&quot;trainingList&quot;);</span>
<span class="nc bnc" id="L1197" title="All 4 branches missed.">                        if (trainingList != null &amp;&amp; !trainingList.isEmpty()) {</span>
<span class="nc" id="L1198">                            StringBuilder trainingNamesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L1199">                            StringBuilder trainingNamesEnBuilder = new StringBuilder();</span>
<span class="nc" id="L1200">                            StringBuilder trainingIssueDatesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L1201">                            StringBuilder trainingIssueDatesEnBuilder = new StringBuilder();</span>
<span class="nc" id="L1202">                            StringBuilder trainingEffectiveDatesCnBuilder = new StringBuilder();</span>
<span class="nc" id="L1203">                            StringBuilder trainingEffectiveDatesEnBuilder = new StringBuilder();</span>

<span class="nc" id="L1205">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L1206" title="All 2 branches missed.">                            for (Object obj : trainingList) {</span>
<span class="nc" id="L1207">                                DwdbCtfCertDetailHcpxqzTrain training = (DwdbCtfCertDetailHcpxqzTrain) obj;</span>

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L1210" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L1211">                                    trainingNamesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1212">                                    trainingNamesEnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1213">                                    trainingIssueDatesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1214">                                    trainingIssueDatesEnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1215">                                    trainingEffectiveDatesCnBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1216">                                    trainingEffectiveDatesEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L1218">                                    isFirst = false;</span>
                                }

                                // 添加培训项目信息
<span class="nc bnc" id="L1222" title="All 2 branches missed.">                                trainingNamesCnBuilder.append(training.getTitleOfTheCertificate1() != null</span>
<span class="nc" id="L1223">                                        ? training.getTitleOfTheCertificate1()</span>
                                        : &quot;&quot;);
<span class="nc bnc" id="L1225" title="All 2 branches missed.">                                trainingNamesEnBuilder.append(training.getTitleOfTheCertificate2() != null</span>
<span class="nc" id="L1226">                                        ? training.getTitleOfTheCertificate2()</span>
                                        : &quot;&quot;);
<span class="nc" id="L1228">                                trainingIssueDatesCnBuilder.append(</span>
<span class="nc bnc" id="L1229" title="All 2 branches missed.">                                        training.getCertificateNo1() != null ? training.getCertificateNo1() : &quot;&quot;);</span>
<span class="nc" id="L1230">                                trainingIssueDatesEnBuilder.append(</span>
<span class="nc bnc" id="L1231" title="All 2 branches missed.">                                        training.getCertificateNo2() != null ? training.getCertificateNo2() : &quot;&quot;);</span>
<span class="nc" id="L1232">                                trainingEffectiveDatesCnBuilder</span>
<span class="nc bnc" id="L1233" title="All 2 branches missed.">                                        .append(training.getDateOfExpiry3() != null ? training.getDateOfExpiry3() : &quot;&quot;);</span>
<span class="nc" id="L1234">                                trainingEffectiveDatesEnBuilder</span>
<span class="nc bnc" id="L1235" title="All 2 branches missed.">                                        .append(training.getDateOfExpiry4() != null ? training.getDateOfExpiry4() : &quot;&quot;);</span>
<span class="nc" id="L1236">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L1239">                            targetData.setTrainingNamesCn(trainingNamesCnBuilder.toString());</span>
<span class="nc" id="L1240">                            targetData.setTrainingNamesEn(trainingNamesEnBuilder.toString());</span>
<span class="nc" id="L1241">                            targetData.setTrainingIssueDatesCn(trainingIssueDatesCnBuilder.toString());</span>
<span class="nc" id="L1242">                            targetData.setTrainingIssueDatesEn(trainingIssueDatesEnBuilder.toString());</span>
<span class="nc" id="L1243">                            targetData.setTrainingEffectiveDatesCn(trainingEffectiveDatesCnBuilder.toString());</span>
<span class="nc" id="L1244">                            targetData.setTrainingEffectiveDatesEn(trainingEffectiveDatesEnBuilder.toString());</span>
                        }
<span class="nc bnc" id="L1246" title="All 2 branches missed.">                    } else if (sourceData.getCatalogname().equals(&quot;海船船员特免证明&quot;)) {</span>
                        // 从主表数据中提取字段到targetData
<span class="nc" id="L1248">                        Object mainData = convertResult.getMainTableData();</span>
<span class="nc bnc" id="L1249" title="All 2 branches missed.">                        if (mainData instanceof DwdbCtfCertDetailHccytm) {</span>
<span class="nc" id="L1250">                            DwdbCtfCertDetailHccytm hccytm = (DwdbCtfCertDetailHccytm) mainData;</span>
<span class="nc" id="L1251">                            targetData.setBirth(hccytm.getDateOfBirth1());</span>
<span class="nc" id="L1252">                            targetData.setBirthEn(hccytm.getDateOfBirth2());</span>
<span class="nc" id="L1253">                            targetData.setNameEn(hccytm.getFullNameOfTheHolder2());</span>
<span class="nc" id="L1254">                            targetData.setCountryCn(hccytm.getNationality1());</span>
<span class="nc" id="L1255">                            targetData.setCountryEn(hccytm.getNationality2());</span>
<span class="nc" id="L1256">                            targetData.setSignDeptEn(hccytm.getIssuingAminstration2());</span>
<span class="nc" id="L1257">                            targetData.setCertificateIssuedDateEn(hccytm.getIssuedOn2());</span>
<span class="nc" id="L1258">                            targetData.setCertificateExpiringDateEn(hccytm.getDateOfExpiry2());</span>
                        }

                        // 处理职务等级信息
<span class="nc" id="L1262">                        List&lt;?&gt; capacityList = convertResult.getSubTableDataList(&quot;capacityList&quot;);</span>
<span class="nc bnc" id="L1263" title="All 4 branches missed.">                        if (capacityList != null &amp;&amp; !capacityList.isEmpty()) {</span>
<span class="nc" id="L1264">                            StringBuilder crewTypeBuilder = new StringBuilder();</span>
<span class="nc" id="L1265">                            StringBuilder crewTypeEnBuilder = new StringBuilder();</span>

<span class="nc" id="L1267">                            boolean isFirst = true;</span>
<span class="nc bnc" id="L1268" title="All 2 branches missed.">                            for (Object obj : capacityList) {</span>
<span class="nc" id="L1269">                                DwdbCtfCertDetailHccytmCap capacity = (DwdbCtfCertDetailHccytmCap) obj;</span>

                                // 获取原始职务
<span class="nc bnc" id="L1272" title="All 2 branches missed.">                                String originalCapacityCn = capacity.getCapacity1() != null ? capacity.getCapacity1()</span>
                                        : &quot;&quot;;
<span class="nc bnc" id="L1274" title="All 2 branches missed.">                                String originalCapacityEn = capacity.getCapacity2() != null ? capacity.getCapacity2()</span>
                                        : &quot;&quot;;

                                // 如果中文职务是&quot;空白&quot;，则跳过此记录
<span class="nc bnc" id="L1278" title="All 2 branches missed.">                                if (&quot;空白&quot;.equals(originalCapacityCn)) {</span>
<span class="nc" id="L1279">                                    continue;</span>
                                }

                                // 不是第一个元素时添加分隔符
<span class="nc bnc" id="L1283" title="All 2 branches missed.">                                if (!isFirst) {</span>
<span class="nc" id="L1284">                                    crewTypeBuilder.append(&quot;#&quot;);</span>
<span class="nc" id="L1285">                                    crewTypeEnBuilder.append(&quot;#&quot;);</span>
                                } else {
<span class="nc" id="L1287">                                    isFirst = false;</span>
                                }

                                // 添加职务等级信息（直接使用，不进行映射）
<span class="nc" id="L1291">                                crewTypeBuilder.append(originalCapacityCn);</span>
<span class="nc" id="L1292">                                crewTypeEnBuilder.append(originalCapacityEn);</span>
<span class="nc" id="L1293">                            }</span>

                            // 设置到主表对象中
<span class="nc" id="L1296">                            targetData.setCrewType(crewTypeBuilder.toString());</span>
<span class="nc" id="L1297">                            targetData.setCrewTypeEn(crewTypeEnBuilder.toString());</span>
                        }
                    }

                    // 收集主表数据
<span class="nc bnc" id="L1302" title="All 2 branches missed.">                    if (convertResult.getMainTableData() != null) {</span>
                        // 根据证书类型分类存储主表数据
<span class="nc" id="L1304">                        String certificateType = convertResult.getCertificateType();</span>
<span class="nc bnc" id="L1305" title="All 2 branches missed.">                        if (!subTableDataMap.containsKey(certificateType + &quot;_main&quot;)) {</span>
<span class="nc" id="L1306">                            subTableDataMap.put(certificateType + &quot;_main&quot;, new ArrayList&lt;&gt;());</span>
                        }
<span class="nc" id="L1308">                        subTableDataMap.get(certificateType + &quot;_main&quot;).add(convertResult.getMainTableData());</span>
                    }

                    // 收集子表数据
<span class="nc bnc" id="L1312" title="All 2 branches missed.">                    for (Map.Entry&lt;String, List&lt;?&gt;&gt; entry : convertResult.getSubTableDataMap().entrySet()) {</span>
<span class="nc" id="L1313">                        String key = convertResult.getCertificateType() + &quot;_&quot; + entry.getKey();</span>
<span class="nc bnc" id="L1314" title="All 2 branches missed.">                        if (!subTableDataMap.containsKey(key)) {</span>
<span class="nc" id="L1315">                            subTableDataMap.put(key, new ArrayList&lt;&gt;());</span>
                        }
<span class="nc" id="L1317">                        subTableDataMap.get(key).addAll(entry.getValue());</span>

                        // log.info(&quot;开始处理子表数据, key: {}, valeu:{}&quot;, key, entry.getValue());

<span class="nc" id="L1321">                    }</span>

                    // 收集未使用的属性(不再直接保存)
<span class="nc bnc" id="L1324" title="All 2 branches missed.">                    if (convertResult.hasUnusedAttributes()) {</span>
<span class="nc" id="L1325">                        unusedAttributesList.addAll(convertResult.getUnusedAttributes());</span>
                    }

                    // 记录成功处理的数据
<span class="nc" id="L1329">                    result.addSuccess(sourceData.getDataid());</span>
                } else {
                    // 记录失败处理的数据
<span class="nc" id="L1332">                    result.addFail(sourceData.getDataid(), convertResult.getErrorMessage());</span>
<span class="nc" id="L1333">                    log.error(&quot;数据转换失败, sourceData: {}, error: {}&quot;,</span>
<span class="nc" id="L1334">                            sourceData.getDataid(), convertResult.getErrorMessage());</span>

                    // 将转换失败的数据写入重处理表
<span class="nc" id="L1337">                    saveErrorRecord(sourceData, convertResult.getErrorMessage());</span>
                }
<span class="nc" id="L1339">            } catch (Exception e) {</span>
                // 记录失败处理的数据
<span class="nc" id="L1341">                result.addFail(sourceData.getDataid(), e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()));</span>
<span class="nc" id="L1342">                log.error(&quot;数据处理异常, sourceData: &quot; + sourceData.getDataid(), e);</span>

                // 将处理异常的数据写入重处理表
<span class="nc" id="L1345">                saveErrorRecord(sourceData, e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()));</span>
<span class="nc" id="L1346">            }</span>
<span class="nc" id="L1347">        }</span>

        // 批量插入转换后的数据
<span class="nc bnc" id="L1350" title="All 2 branches missed.">        if (!targetDataList.isEmpty()) {</span>
            try {
                // 尝试批量插入
                // dwdbCertificateDataMapper.batchInsert(targetDataList);
<span class="nc" id="L1354">                batchInsertMainData(targetDataList);</span>
<span class="nc" id="L1355">            } catch (Exception e) {</span>
<span class="nc" id="L1356">                log.error(&quot;批量插入失败,尝试单条插入&quot;, e);</span>

                // 批量插入失败,改为逐条插入
<span class="nc bnc" id="L1359" title="All 2 branches missed.">                for (DwdbCertificateData targetData : targetDataList) {</span>
                    try {
                        // 修改这行:
                        // dwdbCertificateDataMapper.insert(targetData);
                        // 改为:
<span class="nc" id="L1364">                        List&lt;DwdbCertificateData&gt; singleDataList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L1365">                        singleDataList.add(targetData);</span>
<span class="nc" id="L1366">                        batchInsertMainData(singleDataList);</span>
<span class="nc" id="L1367">                    } catch (Exception ex) {</span>
<span class="nc" id="L1368">                        log.error(&quot;单条插入失败, dataId: &quot; + targetData.getDataId(), ex);</span>
<span class="nc" id="L1369">                        result.addFail(targetData.getDataId(),</span>
<span class="nc" id="L1370">                                ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
                        // 对于插入失败的记录写入重处理表
<span class="nc" id="L1372">                        saveErrorRecord(targetData, ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
<span class="nc" id="L1373">                    }</span>
<span class="nc" id="L1374">                }</span>
<span class="nc" id="L1375">            }</span>
        }

        // 批量保存各类子表数据
<span class="nc bnc" id="L1379" title="All 2 branches missed.">        for (Map.Entry&lt;String, List&lt;Object&gt;&gt; entry : subTableDataMap.entrySet()) {</span>
<span class="nc bnc" id="L1380" title="All 2 branches missed.">            if (!entry.getValue().isEmpty()) {</span>
<span class="nc" id="L1381">                String tableType = entry.getKey();</span>
<span class="nc" id="L1382">                List&lt;Object&gt; dataList = entry.getValue();</span>
                try {
                    // 尝试批量插入
<span class="nc" id="L1385">                    batchInsertByTableType(tableType, dataList);</span>
<span class="nc" id="L1386">                } catch (Exception e) {</span>
<span class="nc" id="L1387">                    log.error(&quot;批量保存子表数据失败,尝试单条插入, tableType: &quot; + tableType, e);</span>

                    // 批量插入失败,改为逐条插入
<span class="nc bnc" id="L1390" title="All 2 branches missed.">                    for (Object data : dataList) {</span>
                        try {
<span class="nc" id="L1392">                            List&lt;Object&gt; singleDataList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L1393">                            singleDataList.add(data);</span>
<span class="nc" id="L1394">                            batchInsertByTableType(tableType, singleDataList);</span>
<span class="nc" id="L1395">                        } catch (Exception ex) {</span>
<span class="nc" id="L1396">                            log.error(&quot;单条插入数据失败&quot;, ex);</span>
<span class="nc" id="L1397">                            String dataId = getDataIdFromObject(data);</span>
<span class="nc bnc" id="L1398" title="All 2 branches missed.">                            if (dataId != null) {</span>
<span class="nc" id="L1399">                                log.error(&quot;单条插入数据失败, dataId: &quot; + dataId, ex);</span>
<span class="nc" id="L1400">                                result.addFail(dataId, ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
                            }
<span class="nc" id="L1402">                            saveErrorRecord(data, ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
<span class="nc" id="L1403">                        }</span>
<span class="nc" id="L1404">                    }</span>
<span class="nc" id="L1405">                }</span>
            }
<span class="nc" id="L1407">        }</span>

        // 最后批量保存未使用的属性
<span class="nc bnc" id="L1410" title="All 2 branches missed.">        if (!unusedAttributesList.isEmpty()) {</span>
            try {
                // 先尝试批量插入
<span class="nc" id="L1413">                batchInsertAttributes(unusedAttributesList);</span>
<span class="nc" id="L1414">            } catch (Exception e) {</span>
<span class="nc" id="L1415">                log.error(&quot;批量插入属性失败,尝试逐条插入&quot;, e);</span>

                // 批量插入失败,改为逐条插入
<span class="nc bnc" id="L1418" title="All 2 branches missed.">                for (DwdbCertificateDataAttribute attribute : unusedAttributesList) {</span>
                    try {
<span class="nc" id="L1420">                        List&lt;DwdbCertificateDataAttribute&gt; singleAttributeList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L1421">                        singleAttributeList.add(attribute);</span>
<span class="nc" id="L1422">                        batchInsertAttributes(singleAttributeList);</span>
<span class="nc" id="L1423">                    } catch (Exception ex) {</span>
<span class="nc" id="L1424">                        log.error(&quot;插入属性失败. dataId: &quot; + attribute.getDataId() +</span>
<span class="nc" id="L1425">                                &quot;, attributeName: &quot; + attribute.getAttributeColumnName(), ex);</span>
<span class="nc" id="L1426">                        result.addFail(attribute.getDataId(),</span>
<span class="nc" id="L1427">                                ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
                        // 将失败的数据写入重处理表
<span class="nc" id="L1429">                        saveErrorRecord(attribute, &quot;插入属性失败: &quot; + attribute.getAttributeColumnName() + &quot;, &quot;</span>
<span class="nc" id="L1430">                                + ex.getMessage() + &quot;\n&quot; + Arrays.toString(ex.getStackTrace()));</span>
<span class="nc" id="L1431">                    }</span>
<span class="nc" id="L1432">                }</span>
<span class="nc" id="L1433">            }</span>
        }

        // 批量保存新增的机构查询辅助表数据
<span class="nc bnc" id="L1437" title="All 2 branches missed.">        if (!newQueryOrgs.isEmpty()) {</span>
            try {
<span class="nc" id="L1439">                certQueryOrgMapper.batchInsert(newQueryOrgs);</span>
<span class="nc" id="L1440">                log.info(&quot;批量保存机构查询辅助表数据成功，共 {} 条&quot;, newQueryOrgs.size());</span>
                // 清空新增列表
<span class="nc" id="L1442">                newQueryOrgs.clear();</span>
<span class="nc" id="L1443">            } catch (Exception e) {</span>
<span class="nc" id="L1444">                log.error(&quot;批量保存机构查询辅助表数据失败&quot;, e);</span>
<span class="nc" id="L1445">            }</span>
        }

<span class="nc" id="L1448">        return result;</span>
    }

    /**
     * 根据表类型执行批量插入
     * 
     * @param tableType 表类型
     * @param dataList  数据列表
     */
    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    private void batchInsertByTableType(String tableType, List&lt;Object&gt; dataList) {
        try {
            // 根据表类型选择对应的Mapper进行批量插入
<span class="nc bnc" id="L1462" title="All 2 branches missed.">            if (tableType.contains(&quot;_main&quot;)) {</span>
                // 处理主表数据
<span class="nc" id="L1464">                String certificateType = tableType.split(&quot;_main&quot;)[0];</span>
<span class="nc bnc" id="L1465" title="All 28 branches missed.">                switch (certificateType) {</span>
                    case &quot;不参加航行和轮机值班海船船员适任证书&quot;:
<span class="nc" id="L1467">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1468">                                .batchInsertBcjhljzb(castList(dataList, DwdbCtfCertificateDetailBcjhljzb.class));</span>
<span class="nc" id="L1469">                        break;</span>
                    case &quot;公务船船员适任证书&quot;:
<span class="nc" id="L1471">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1472">                                .batchInsertGwccy(castList(dataList, DwdbCtfCertificateDetailGwccy.class));</span>
<span class="nc" id="L1473">                        break;</span>
                    case &quot;船员适任证书申请表&quot;:
<span class="nc" id="L1475">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1476">                                .batchInsertCysrzsqb(castList(dataList, DwdbCtfCertificateDetailCysrzsqb.class));</span>
<span class="nc" id="L1477">                        break;</span>
                    case &quot;海船高级船员适任证书&quot;:
<span class="nc" id="L1479">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1480">                                .batchInsertHcgjcy(castList(dataList, DwdbCtfCertificateDetailHcgjcy.class));</span>
<span class="nc" id="L1481">                        break;</span>
                    case &quot;海船普通船员适任证书&quot;:
<span class="nc" id="L1483">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1484">                                .batchInsertHcptcysrz(castList(dataList, DwdbCtfCertificateDetailHcptcysrz.class));</span>
<span class="nc" id="L1485">                        break;</span>
                    case &quot;海船船员培训合格证书&quot;:
<span class="nc" id="L1487">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1488">                                .batchInsertHcpxhg(castList(dataList, DwdbCtfCertificateDetailHcpxhg.class));</span>
<span class="nc" id="L1489">                        break;</span>
                    case &quot;海上非自航船舶船员适任证书&quot;:
<span class="nc" id="L1491">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1492">                                .batchInsertHsfhcysrz(castList(dataList, DwdbCtfCertificateDetailHsfhcysrz.class));</span>
<span class="nc" id="L1493">                        break;</span>
                    case &quot;海员外派机构资质证书&quot;:
<span class="nc" id="L1495">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1496">                                .batchInsertHywpjg(castList(dataList, DwdbCtfCertificateDetailHywpjg.class));</span>
<span class="nc" id="L1497">                        break;</span>
                    case &quot;海船船员健康证明&quot;:
<span class="nc" id="L1499">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1500">                                .batchInsertJkzm(castList(dataList, DwdbCtfCertificateDetailJkzm.class));</span>
<span class="nc" id="L1501">                        break;</span>
                    case &quot;内河船舶船员适任证书&quot;:
<span class="nc" id="L1503">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1504">                                .batchInsertNhcbcy(castList(dataList, DwdbCtfCertificateDetailNhcbcy.class));</span>
<span class="nc" id="L1505">                        break;</span>
                    case &quot;海船船员内河航线行驶资格证明&quot;:
<span class="nc" id="L1507">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1508">                                .batchInsertNhhxxs(castList(dataList, DwdbCtfCertificateDetailNhhxxs.class));</span>
<span class="nc" id="L1509">                        break;</span>
                    case &quot;内河船舶船员培训合格证&quot;:
                    case &quot;内河船舶船员特殊培训合格证&quot;:
<span class="nc" id="L1512">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1513">                                .batchInsertNhpxhg(castList(dataList, DwdbCtfCertificateDetailNhpxhg.class));</span>
<span class="nc" id="L1514">                        break;</span>
                    case &quot;船员培训质量管理体系证书&quot;:
<span class="nc" id="L1516">                        dwdbCertificateDataMapper.batchInsertQms(castList(dataList, DwdbCtfCertificateDetailQms.class));</span>
<span class="nc" id="L1517">                        break;</span>
                    case &quot;海船船员培训许可证&quot;:
<span class="nc" id="L1519">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1520">                                .batchInsertSeamanInfo(castList(dataList, DwdbCtfCertificateDetailSeamanInfo.class));</span>
<span class="nc" id="L1521">                        break;</span>
                    case &quot;特定航线江海直达船舶船员行驶资格证明培训合格证&quot;:
<span class="nc" id="L1523">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1524">                                .batchInsertTdhxjh(castList(dataList, DwdbCtfCertificateDetailTdhxjh.class));</span>
<span class="nc" id="L1525">                        break;</span>
                    case &quot;小型海船适任证书&quot;:
<span class="nc" id="L1527">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1528">                                .batchInsertXhcsrz(castList(dataList, DwdbCtfCertificateDetailXhcsrz.class));</span>
<span class="nc" id="L1529">                        break;</span>
                    case &quot;引航员船员适任证书&quot;:
<span class="nc" id="L1531">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1532">                                .batchInsertYhysrz(castList(dataList, DwdbCtfCertificateDetailYhysrz.class));</span>
<span class="nc" id="L1533">                        break;</span>
                    case &quot;游艇驾驶证&quot;:
                    case &quot;游艇驾驶证（海上）&quot;:
                    case &quot;游艇驾驶证（内河）&quot;:
                    case &quot;游艇驾驶证海上&quot;:
                    case &quot;游艇驾驶证内河&quot;:
<span class="nc" id="L1539">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1540">                                .batchInsertYtjsz(castList(dataList, DwdbCtfCertificateDetailYtjsz.class));</span>
<span class="nc" id="L1541">                        break;</span>
                    case &quot;船上厨师培训合格证明&quot;:
<span class="nc" id="L1543">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1544">                                .batchInsertCscspx(castList(dataList, DwdbCtfCertificateDetailCscspx.class));</span>
<span class="nc" id="L1545">                        break;</span>
                    case &quot;船上膳食服务辅助人员培训证明&quot;:
<span class="nc" id="L1547">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1548">                                .batchInsertCsssfzpx(castList(dataList, DwdbCtfCertificateDetailCsssfzpx.class));</span>
<span class="nc" id="L1549">                        break;</span>
                    case &quot;海船船员适任证书承认签证&quot;:
<span class="nc" id="L1551">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1552">                                .batchInsertHccycr(castList(dataList, DwdbCtfCertificateDetailHccycr.class));</span>
<span class="nc" id="L1553">                        break;</span>
                    // case &quot;海船船员适任证书承认签证_capacityList&quot;:
                    // dwdbCertificateDataMapper.batchInsertHccycrCapacity(castList(dataList,
                    // DwdbCtfCertificateDetailHccycrCapacity.class));
                    // break;
                    case &quot;海船船员适任证书承认签证_functionList&quot;:
<span class="nc" id="L1559">                        dwdbCertificateDataMapper.batchInsertHccycrFunction(</span>
<span class="nc" id="L1560">                                castList(dataList, DwdbCtfCertificateDetailHccycrFunction.class));</span>
<span class="nc" id="L1561">                        break;</span>
                    case &quot;海上设施工作人员海上交通安全技能培训合格证明&quot;:
<span class="nc" id="L1563">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1564">                                .batchInsertHsssjn(castList(dataList, DwdbCtfCertificateDetailHsssjn.class));</span>
<span class="nc" id="L1565">                        break;</span>
                    case &quot;海船不参加船员适任证书&quot;:
<span class="nc" id="L1567">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1568">                                .batchInsertHcbcjcy(castList(dataList, DwdbCtfCertificateDetailHcbcjcy.class));</span>
<span class="nc" id="L1569">                        break;</span>

                    case &quot;内河船员培训许可证&quot;: // 新增内河船员培训许可证主表的处理
<span class="nc" id="L1572">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1573">                                .batchInsertNhcyxkz(castList(dataList, DwdbCtfCertificateDetailNhcyxkz.class));</span>
<span class="nc" id="L1574">                        break;</span>

                    case &quot;海船船员特免证明&quot;:
<span class="nc" id="L1577">                        dwdbCertificateDataMapper.batchInsertHccytm(castList(dataList, DwdbCtfCertDetailHccytm.class));</span>
<span class="nc" id="L1578">                        break;</span>

                    case &quot;海船船员培训合格证承认签证&quot;:
<span class="nc" id="L1581">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1582">                                .batchInsertHcpxhgqz(castList(dataList, DwdbCtfCertDetailHcpxhgqz.class));</span>
<span class="nc" id="L1583">                        break;</span>

                    default:
<span class="nc" id="L1586">                        log.warn(&quot;未知的证书类型: {}, 无法批量保存数据&quot;, certificateType);</span>
<span class="nc" id="L1587">                        throw new RuntimeException(&quot;未知的证书类型: &quot; + certificateType + &quot;, 无法批量保存数据&quot;);</span>
                }
<span class="nc" id="L1589">            } else {</span>
                // 处理子表数据
<span class="nc bnc" id="L1591" title="All 2 branches missed.">                if (tableType.contains(&quot;_capacities&quot;)) {</span>
<span class="nc bnc" id="L1592" title="All 2 branches missed.">                    if (tableType.contains(&quot;不参加航行和轮机值班海船船员适任证书&quot;)) {</span>
                        // log.info(&quot;开始处理不参加航行和轮机值班海船船员适任证书的能力列表数据&quot;);
<span class="nc" id="L1594">                        dwdbCertificateDataMapper.batchInsertBcjhljzbCapacity(</span>
<span class="nc" id="L1595">                                castList(dataList, DwdbCtfCertificateDetailBcjhljzbCapacity.class));</span>
<span class="nc bnc" id="L1596" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船高级船员适任证书&quot;)) {</span>
<span class="nc" id="L1597">                        dwdbCertificateDataMapper.batchInsertHcgjcyCapacity(</span>
<span class="nc" id="L1598">                                castList(dataList, DwdbCtfCertificateDetailHcgjcyCapacity.class));</span>
<span class="nc bnc" id="L1599" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船普通船员适任证书&quot;)) {</span>
<span class="nc" id="L1600">                        dwdbCertificateDataMapper.batchInsertHcptcysrzCapacity(</span>
<span class="nc" id="L1601">                                castList(dataList, DwdbCtfCertificateDetailHcptcysrzCapacity.class));</span>
<span class="nc bnc" id="L1602" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;小型海船适任证书&quot;)) {</span>
<span class="nc" id="L1603">                        dwdbCertificateDataMapper.batchInsertXhcsrzCapacity(</span>
<span class="nc" id="L1604">                                castList(dataList, DwdbCtfCertificateDetailXhcsrzCapacity.class));</span>
<span class="nc bnc" id="L1605" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船船员适任证书承认签证&quot;)) {</span>
<span class="nc" id="L1606">                        dwdbCertificateDataMapper.batchInsertHccycrCapacity(</span>
<span class="nc" id="L1607">                                castList(dataList, DwdbCtfCertificateDetailHccycrCapacity.class));</span>
<span class="nc bnc" id="L1608" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船不参加船员适任证书&quot;)) {</span>
<span class="nc" id="L1609">                        dwdbCertificateDataMapper.batchInsertHcbcjcyCapacity(</span>
<span class="nc" id="L1610">                                castList(dataList, DwdbCtfCertificateDetailHcbcjcyCapacity.class));</span>
<span class="nc bnc" id="L1611" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;公务船船员适任证书&quot;)) {</span>
                        // log.info(&quot;开始处理公务船船员适任证书的职务等级列表数据&quot;);
<span class="nc" id="L1613">                        dwdbCertificateDataMapper.batchInsertGwccyCapacity(</span>
<span class="nc" id="L1614">                                castList(dataList, DwdbCtfCertificateDetailGwccyCapacity.class));</span>
<span class="nc bnc" id="L1615" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船船员特免证明&quot;)) {</span>
<span class="nc" id="L1616">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1617">                                .batchInsertHccytmCapacity(castList(dataList, DwdbCtfCertDetailHccytmCap.class));</span>
                    } else {
<span class="nc" id="L1619">                        throw new RuntimeException(&quot;_capacities未知的子表类型: &quot; + tableType + &quot;, 无法处理数据&quot;);</span>
                    }
<span class="nc bnc" id="L1621" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_functions&quot;)) {</span>
<span class="nc bnc" id="L1622" title="All 2 branches missed.">                    if (tableType.contains(&quot;海船高级船员适任证书&quot;)) {</span>
<span class="nc" id="L1623">                        dwdbCertificateDataMapper.batchInsertHcgjcyFunction(</span>
<span class="nc" id="L1624">                                castList(dataList, DwdbCtfCertificateDetailHcgjcyFunction.class));</span>
<span class="nc bnc" id="L1625" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船普通船员适任证书&quot;)) {</span>
<span class="nc" id="L1626">                        dwdbCertificateDataMapper.batchInsertHcptcysrzFunction(</span>
<span class="nc" id="L1627">                                castList(dataList, DwdbCtfCertificateDetailHcptcysrzFunction.class));</span>
<span class="nc bnc" id="L1628" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;小型海船适任证书&quot;)) {</span>
<span class="nc" id="L1629">                        dwdbCertificateDataMapper.batchInsertXhcsrzFunction(</span>
<span class="nc" id="L1630">                                castList(dataList, DwdbCtfCertificateDetailXhcsrzFunction.class));</span>
<span class="nc bnc" id="L1631" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船船员适任证书承认签证&quot;)) {</span>
<span class="nc" id="L1632">                        dwdbCertificateDataMapper.batchInsertHccycrFunction(</span>
<span class="nc" id="L1633">                                castList(dataList, DwdbCtfCertificateDetailHccycrFunction.class));</span>
<span class="nc bnc" id="L1634" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船船员特免证明&quot;)) {</span>
<span class="nc" id="L1635">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1636">                                .batchInsertHccytmFunction(castList(dataList, DwdbCtfCertDetailHccytmFunc.class));</span>
                    } else {
<span class="nc" id="L1638">                        throw new RuntimeException(&quot;_functions未知的子表类型: &quot; + tableType + &quot;, 无法处理数据&quot;);</span>
                    }
<span class="nc bnc" id="L1640" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_shipList&quot;)) {</span>
<span class="nc" id="L1641">                    dwdbCertificateDataMapper</span>
<span class="nc" id="L1642">                            .batchInsertHsfhcysrzShip(castList(dataList, DwdbCtfCertificateDetailHsfhcysrzShip.class));</span>
<span class="nc bnc" id="L1643" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_experienceList&quot;)) {</span>
<span class="nc" id="L1644">                    dwdbCertificateDataMapper.batchInsertCysrzsqbExperience(</span>
<span class="nc" id="L1645">                            castList(dataList, DwdbCtfCertificateDetailCysrzsqbExperience.class));</span>
<span class="nc bnc" id="L1646" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_optionsList&quot;)) {</span>
<span class="nc" id="L1647">                    dwdbCertificateDataMapper.batchInsertCysrzsqbOptions(</span>
<span class="nc" id="L1648">                            castList(dataList, DwdbCtfCertificateDetailCysrzsqbOptions.class));</span>
<span class="nc bnc" id="L1649" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_nhpxhgItems&quot;)) {</span>
<span class="nc" id="L1650">                    dwdbCertificateDataMapper</span>
<span class="nc" id="L1651">                            .batchInsertNhpxhgItem(castList(dataList, DwdbCtfCertificateDetailNhpxhgItem.class));</span>
<span class="nc bnc" id="L1652" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_seamanPermit&quot;)) {</span>
                    // log.info(&quot;开始处理船员培训许可证数据&quot;);
<span class="nc" id="L1654">                    dwdbCertificateDataMapper</span>
<span class="nc" id="L1655">                            .batchInsertSeamanPermit(castList(dataList, DwdbCtfCertificateDetailSeamanPermit.class));</span>
<span class="nc bnc" id="L1656" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_itemList&quot;)) {</span>
<span class="nc bnc" id="L1657" title="All 2 branches missed.">                    if (tableType.contains(&quot;海船船员培训许可证&quot;)) {</span>
<span class="nc" id="L1658">                        dwdbCertificateDataMapper.batchInsertSeamanPermitItem(</span>
<span class="nc" id="L1659">                                castList(dataList, DwdbCtfCertificateDetailSeamanPermitItem.class));</span>
<span class="nc bnc" id="L1660" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;内河船员培训许可证&quot;)) { // 新增内河船员培训许可证子表的处理</span>
<span class="nc" id="L1661">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1662">                                .batchInsertNhcyxkzItems(castList(dataList, DwdbCtfCertificateDetailNhcyxkzItem.class));</span>
                    }
<span class="nc bnc" id="L1664" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_rangeList&quot;)) {</span>
<span class="nc bnc" id="L1665" title="All 2 branches missed.">                    if (tableType.contains(&quot;引航员船员适任证书&quot;)) {</span>
<span class="nc" id="L1666">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1667">                                .batchInsertYhysrzRange(castList(dataList, DwdbCtfCertificateDetailYhysrzRange.class));</span>
                    } else {
<span class="nc" id="L1669">                        throw new RuntimeException(&quot;rangeList 未知的子表类型: &quot; + tableType + &quot;, 无法处理数据&quot;);</span>
                    }
<span class="nc bnc" id="L1671" title="All 2 branches missed.">                } else if (tableType.contains(&quot;_trainingList&quot;)) {</span>
<span class="nc bnc" id="L1672" title="All 2 branches missed.">                    if (tableType.contains(&quot;海船船员培训合格证书&quot;)) {</span>
                        // log.info(&quot;开始处理海船船员培训合格证书的培训列表数据&quot;);
<span class="nc" id="L1674">                        dwdbCertificateDataMapper.batchInsertHcpxhgTraining(</span>
<span class="nc" id="L1675">                                castList(dataList, DwdbCtfCertificateDetailHcpxhgTraining.class));</span>
<span class="nc bnc" id="L1676" title="All 4 branches missed.">                    } else if (tableType.contains(&quot;内河船舶船员培训合格证&quot;) || tableType.contains(&quot;内河船舶船员特殊培训合格证&quot;)) {</span>
                        // log.info(&quot;开始处理内河船舶船员培训合格证的培训列表数据&quot;);
<span class="nc" id="L1678">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1679">                                .batchInsertNhpxhgItem(castList(dataList, DwdbCtfCertificateDetailNhpxhgItem.class));</span>
<span class="nc bnc" id="L1680" title="All 2 branches missed.">                    } else if (tableType.contains(&quot;海船船员培训合格证承认签证&quot;)) {</span>
                        // log.info(&quot;开始处理海船船员培训合格证承认签证的培训列表数据&quot;);
<span class="nc" id="L1682">                        dwdbCertificateDataMapper</span>
<span class="nc" id="L1683">                                .batchInsertHcpxqzTrain(castList(dataList, DwdbCtfCertDetailHcpxqzTrain.class));</span>
                    }
                } else {
<span class="nc" id="L1686">                    throw new RuntimeException(&quot;未知的子表类型: &quot; + tableType + &quot;, 无法处理数据&quot;);</span>
                }
            }
<span class="nc" id="L1689">        } catch (Exception e) {</span>
<span class="nc" id="L1690">            log.error(&quot;批量插入失败，准备重试: {}&quot;, e.getMessage());</span>
<span class="nc" id="L1691">            throw e;</span>
<span class="nc" id="L1692">        }</span>
<span class="nc" id="L1693">    }</span>

    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public void batchInsertMainData(List&lt;DwdbCertificateData&gt; targetDataList) {
        try {
<span class="nc" id="L1699">            dwdbCertificateDataMapper.batchInsert(targetDataList);</span>
<span class="nc" id="L1700">        } catch (Exception e) {</span>
<span class="nc" id="L1701">            log.error(&quot;批量插入主表数据失败，准备重试: {}&quot;, e.getMessage());</span>
<span class="nc" id="L1702">            throw e;</span>
<span class="nc" id="L1703">        }</span>
<span class="nc" id="L1704">    }</span>

    @Recover
    public void recoverBatchInsert(Exception e, List&lt;DwdbCertificateData&gt; targetDataList) {
<span class="nc" id="L1708">        log.error(&quot;主表数据批量插入重试3次后仍然失败，需要人工介入处理: {}&quot;, e.getMessage());</span>
        // 可以添加告警逻辑
<span class="nc" id="L1710">    }</span>

    /**
     * 保存错误记录
     * 
     * @param data     数据对象
     * @param errorMsg 错误信息
     */
    private void saveErrorRecord(Object data, String errorMsg) {
        try {
            // 获取数据ID
<span class="nc" id="L1721">            String dataId = getDataIdFromObject(data);</span>
<span class="nc bnc" id="L1722" title="All 2 branches missed.">            if (dataId == null) {</span>
<span class="nc" id="L1723">                log.error(&quot;无法获取数据ID,无法保存到重处理表&quot;);</span>
<span class="nc" id="L1724">                return;</span>
            }

            // 检查重处理表中是否已存在该记录
<span class="nc bnc" id="L1728" title="All 2 branches missed.">            if (dwdbCertificateDataMapper.existsInRedoTable(dataId)) {</span>
<span class="nc" id="L1729">                log.info(&quot;数据已存在于重处理表中,跳过插入, dataId: {}&quot;, dataId);</span>
<span class="nc" id="L1730">                return;</span>
            }

            // 查询原始数据
<span class="nc" id="L1734">            OdsCertificateData originalData = findOriginalData(dataId);</span>
<span class="nc bnc" id="L1735" title="All 2 branches missed.">            if (originalData == null) {</span>
<span class="nc" id="L1736">                log.error(&quot;无法找到原始数据,dataId: {}&quot;, dataId);</span>
<span class="nc" id="L1737">                return;</span>
            }

            // 保存到重处理表
<span class="nc" id="L1741">            dwdbCertificateDataMapper.insertRedoData(</span>
<span class="nc" id="L1742">                    originalData.getDataid(),</span>
<span class="nc" id="L1743">                    originalData.getCertificateid(),</span>
<span class="nc" id="L1744">                    originalData.getCatalogid(),</span>
<span class="nc" id="L1745">                    originalData.getCatalogname(),</span>
<span class="nc" id="L1746">                    originalData.getTemplateid(),</span>
<span class="nc" id="L1747">                    originalData.getCertificatetype(),</span>
<span class="nc" id="L1748">                    originalData.getCertificatetypecode(),</span>
<span class="nc" id="L1749">                    originalData.getIssuedept(),</span>
<span class="nc" id="L1750">                    originalData.getIssuedeptcode(),</span>
<span class="nc" id="L1751">                    originalData.getCertificateareacode(),</span>
<span class="nc" id="L1752">                    originalData.getCertificateholder(),</span>
<span class="nc" id="L1753">                    originalData.getCertificateholdercode(),</span>
<span class="nc" id="L1754">                    originalData.getCertificateholdertype(),</span>
<span class="nc" id="L1755">                    originalData.getCertificatenumber(),</span>
<span class="nc" id="L1756">                    originalData.getIssuedate(),</span>
<span class="nc" id="L1757">                    originalData.getValidbegindate(),</span>
<span class="nc" id="L1758">                    originalData.getValidenddate(),</span>
<span class="nc" id="L1759">                    originalData.getSurfacedata(),</span>
<span class="nc" id="L1760">                    originalData.getStatus(),</span>
<span class="nc" id="L1761">                    originalData.getCreator(),</span>
<span class="nc" id="L1762">                    originalData.getCreatetime(),</span>
<span class="nc" id="L1763">                    originalData.getOperator(),</span>
<span class="nc" id="L1764">                    originalData.getUpdatetime(),</span>
<span class="nc" id="L1765">                    originalData.getFilepath(),</span>
<span class="nc" id="L1766">                    originalData.getSyncstatus(),</span>
<span class="nc" id="L1767">                    originalData.getRemarks(),</span>
<span class="nc" id="L1768">                    originalData.getDeptid(),</span>
<span class="nc" id="L1769">                    originalData.getApplynum(),</span>
<span class="nc" id="L1770">                    originalData.getAffairname(),</span>
<span class="nc" id="L1771">                    originalData.getAffairtype(),</span>
<span class="nc" id="L1772">                    originalData.getServebusiness(),</span>
<span class="nc" id="L1773">                    originalData.getAffairid(),</span>
<span class="nc" id="L1774">                    originalData.getAffairnum(),</span>
<span class="nc" id="L1775">                    originalData.getQztype(),</span>
<span class="nc" id="L1776">                    originalData.getZztype(),</span>
<span class="nc" id="L1777">                    originalData.getDrafturl(),</span>
<span class="nc" id="L1778">                    originalData.getIsview(),</span>
<span class="nc" id="L1779">                    originalData.getSortname(),</span>
<span class="nc" id="L1780">                    originalData.getCol1(),</span>
<span class="nc" id="L1781">                    originalData.getVerifydate(),</span>
<span class="nc" id="L1782">                    originalData.getVerification(),</span>
<span class="nc" id="L1783">                    originalData.getCreditcode(),</span>
<span class="nc" id="L1784">                    originalData.getSealname(),</span>
<span class="nc" id="L1785">                    originalData.getFcdcDate(),</span>
                    &quot;0&quot;, // redo_status 设为0表示待处理
                    errorMsg // 错误原因
            );

<span class="nc" id="L1790">            log.info(&quot;数据已添加到重处理表, dataId: {}&quot;, dataId);</span>
<span class="nc" id="L1791">        } catch (Exception e) {</span>
<span class="nc" id="L1792">            log.error(&quot;保存到重处理表失败&quot;, e);</span>
<span class="nc" id="L1793">        }</span>
<span class="nc" id="L1794">    }</span>

    /**
     * 根据数据ID查找原始数据
     * 
     * @param dataId 数据ID
     * @return 原始数据
     */
    private OdsCertificateData findOriginalData(String dataId) {
        try {
<span class="nc" id="L1804">            return odsCertificateDataMapper.selectById(dataId);</span>
<span class="nc" id="L1805">        } catch (Exception e) {</span>
<span class="nc" id="L1806">            log.error(&quot;查询原始数据失败, dataId: &quot; + dataId, e);</span>
<span class="nc" id="L1807">            return null;</span>
        }
    }

    /**
     * 从对象中获取数据ID
     * 
     * @param obj 对象
     * @return 数据ID
     */
    private String getDataIdFromObject(Object obj) {
<span class="nc bnc" id="L1818" title="All 2 branches missed.">        if (obj == null) {</span>
<span class="nc" id="L1819">            return null;</span>
        }

        try {
            // 尝试获取 dataId 字段
<span class="nc bnc" id="L1824" title="All 2 branches missed.">            if (obj instanceof Map) {</span>
<span class="nc" id="L1825">                Map&lt;?, ?&gt; map = (Map&lt;?, ?&gt;) obj;</span>
<span class="nc bnc" id="L1826" title="All 2 branches missed.">                if (map.containsKey(&quot;dataId&quot;)) {</span>
<span class="nc" id="L1827">                    return String.valueOf(map.get(&quot;dataId&quot;));</span>
<span class="nc bnc" id="L1828" title="All 2 branches missed.">                } else if (map.containsKey(&quot;dataid&quot;)) {</span>
<span class="nc" id="L1829">                    return String.valueOf(map.get(&quot;dataid&quot;));</span>
                }
<span class="nc" id="L1831">            } else {</span>
                // 使用反射获取字段值
                try {
<span class="nc" id="L1834">                    java.lang.reflect.Method getDataId = obj.getClass().getMethod(&quot;getDataId&quot;);</span>
<span class="nc" id="L1835">                    Object result = getDataId.invoke(obj);</span>
<span class="nc bnc" id="L1836" title="All 2 branches missed.">                    if (result != null) {</span>
<span class="nc" id="L1837">                        return result.toString();</span>
                    }
<span class="nc" id="L1839">                } catch (NoSuchMethodException e) {</span>
                    // 尝试获取 dataid 字段
                    try {
<span class="nc" id="L1842">                        java.lang.reflect.Method getDataid = obj.getClass().getMethod(&quot;getDataid&quot;);</span>
<span class="nc" id="L1843">                        Object result = getDataid.invoke(obj);</span>
<span class="nc bnc" id="L1844" title="All 2 branches missed.">                        if (result != null) {</span>
<span class="nc" id="L1845">                            return result.toString();</span>
                        }
<span class="nc" id="L1847">                    } catch (NoSuchMethodException ex) {</span>
                        // 两个方法都不存在
<span class="nc" id="L1849">                    }</span>
<span class="nc" id="L1850">                }</span>
            }
<span class="nc" id="L1852">        } catch (Exception e) {</span>
<span class="nc" id="L1853">            log.error(&quot;获取数据ID失败&quot;, e);</span>
<span class="nc" id="L1854">        }</span>

<span class="nc" id="L1856">        return null;</span>
    }

    /**
     * 类型转换辅助方法
     */
    @SuppressWarnings(&quot;unchecked&quot;)
    private &lt;T&gt; List&lt;T&gt; castList(List&lt;?&gt; sourceList, Class&lt;T&gt; clazz) {
<span class="nc" id="L1864">        List&lt;T&gt; targetList = new ArrayList&lt;&gt;(sourceList.size());</span>
<span class="nc bnc" id="L1865" title="All 2 branches missed.">        for (Object obj : sourceList) {</span>
<span class="nc" id="L1866">            targetList.add((T) obj);</span>
<span class="nc" id="L1867">        }</span>
<span class="nc" id="L1868">        return targetList;</span>
    }

    /**
     * 处理结果类
     */
<span class="nc" id="L1874">    private static class ProcessResult {</span>
<span class="nc" id="L1875">        private Map&lt;String, Boolean&gt; processResults = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1876">        private Map&lt;String, String&gt; failReasons = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1877">        private int successCount = 0;</span>
<span class="nc" id="L1878">        private int failCount = 0;</span>

        public void addSuccess(String dataId) {
<span class="nc" id="L1881">            processResults.put(dataId, true);</span>
<span class="nc" id="L1882">            successCount++;</span>
<span class="nc" id="L1883">        }</span>

        public void addFail(String dataId, String reason) {
<span class="nc" id="L1886">            processResults.put(dataId, false);</span>
<span class="nc" id="L1887">            failReasons.put(dataId, reason);</span>
<span class="nc" id="L1888">            failCount++;</span>
<span class="nc" id="L1889">        }</span>

        public boolean isSuccess(String dataId) {
<span class="nc" id="L1892">            return processResults.getOrDefault(dataId, false);</span>
        }

        public String getFailReason(String dataId) {
<span class="nc" id="L1896">            return failReasons.get(dataId);</span>
        }

        public int getSuccessCount() {
<span class="nc" id="L1900">            return successCount;</span>
        }

        public int getFailCount() {
<span class="nc" id="L1904">            return failCount;</span>
        }
    }

    /**
     * 获取数据列表中的最大fcdcDate
     * 
     * @param dataList 数据列表
     * @return 最大的fcdcDate
     */
    private LocalDateTime getMaxFcdcDate(List&lt;OdsCertificateData&gt; dataList) {
<span class="nc bnc" id="L1915" title="All 4 branches missed.">        if (dataList == null || dataList.isEmpty()) {</span>
<span class="nc" id="L1916">            return null;</span>
        }

<span class="nc" id="L1919">        LocalDateTime maxDate = null;</span>
<span class="nc bnc" id="L1920" title="All 2 branches missed.">        for (OdsCertificateData data : dataList) {</span>
<span class="nc" id="L1921">            LocalDateTime fcdcDate = data.getFcdcDate();</span>
<span class="nc bnc" id="L1922" title="All 2 branches missed.">            if (fcdcDate != null) {</span>
<span class="nc bnc" id="L1923" title="All 4 branches missed.">                if (maxDate == null || fcdcDate.isAfter(maxDate)) {</span>
<span class="nc" id="L1924">                    maxDate = fcdcDate;</span>
                }
            }
<span class="nc" id="L1927">        }</span>

<span class="nc" id="L1929">        return maxDate;</span>
    }

    /**
     * 获取数据列表中最后一条数据的ID
     * 
     * @param dataList 数据列表
     * @return 最后一条数据的ID
     */
    private String getLastDataId(List&lt;OdsCertificateData&gt; dataList) {
<span class="nc bnc" id="L1939" title="All 4 branches missed.">        if (dataList == null || dataList.isEmpty()) {</span>
<span class="nc" id="L1940">            return null;</span>
        }

        // 按照fcdcDate排序,获取最后一条数据的ID
<span class="nc" id="L1944">        OdsCertificateData lastData = null;</span>
<span class="nc" id="L1945">        LocalDateTime maxDate = null;</span>

<span class="nc bnc" id="L1947" title="All 2 branches missed.">        for (OdsCertificateData data : dataList) {</span>
<span class="nc" id="L1948">            LocalDateTime fcdcDate = data.getFcdcDate();</span>
<span class="nc bnc" id="L1949" title="All 2 branches missed.">            if (fcdcDate != null) {</span>
<span class="nc bnc" id="L1950" title="All 4 branches missed.">                if (maxDate == null || fcdcDate.isAfter(maxDate)) {</span>
<span class="nc" id="L1951">                    maxDate = fcdcDate;</span>
<span class="nc" id="L1952">                    lastData = data;</span>
                }
            }
<span class="nc" id="L1955">        }</span>

<span class="nc bnc" id="L1957" title="All 2 branches missed.">        return lastData != null ? lastData.getDataid() : null;</span>
    }

    /**
     * 清理目标表中的数据
     * 
     * @param sourceData 源数据
     */
    private void cleanupTargetData(OdsCertificateData sourceData) {
<span class="nc" id="L1966">        String dataId = sourceData.getDataid();</span>
<span class="nc" id="L1967">        String certificateType = sourceData.getCatalogname();</span>

        // 新增逻辑：通过certificateId查询dataId
<span class="nc" id="L1970">        String certificateId = sourceData.getCertificateid();</span>
<span class="nc bnc" id="L1971" title="All 2 branches missed.">        if (StringUtils.isNotBlank(certificateId)) {</span>
<span class="nc" id="L1972">            List&lt;String&gt; existingDataIds = dwdbCertificateDataMapper.findDataIdsByCertificateId(certificateId);</span>
<span class="nc bnc" id="L1973" title="All 2 branches missed.">            if (!CollectionUtils.isEmpty(existingDataIds)) {</span>
                // 如果找到了记录，使用第一条记录的dataId
<span class="nc" id="L1975">                log.info(&quot;原来的dataId变量: {}&quot;, dataId);</span>
<span class="nc" id="L1976">                dataId = existingDataIds.get(0);</span>
<span class="nc" id="L1977">                log.info(&quot;通过certificateId={}找到已存在记录，使用dataId={}&quot;, certificateId, dataId);</span>
            }
        }

        try {
            // 1. 删除主表数据
<span class="nc" id="L1983">            dwdbCertificateDataMapper.deleteByDataId(dataId);</span>

            // 2. 根据证书类型删除对应的明细表数据
<span class="nc bnc" id="L1986" title="All 27 branches missed.">            switch (certificateType) {</span>
                case &quot;不参加航行和轮机值班海船船员适任证书&quot;:
<span class="nc" id="L1988">                    dwdbCertificateDataMapper.deleteBcjhljzbCapacityByDataId(dataId);</span>
<span class="nc" id="L1989">                    dwdbCertificateDataMapper.deleteBcjhljzbByDataId(dataId);</span>
<span class="nc" id="L1990">                    break;</span>
                case &quot;公务船船员适任证书&quot;:
<span class="nc" id="L1992">                    dwdbCertificateDataMapper.deleteGwccyCapacityByDataId(dataId);</span>
<span class="nc" id="L1993">                    dwdbCertificateDataMapper.deleteGwccyByDataId(dataId);</span>
<span class="nc" id="L1994">                    break;</span>
                case &quot;船员适任证书申请表&quot;:
<span class="nc" id="L1996">                    dwdbCertificateDataMapper.deleteCysrzsqbExperienceByDataId(dataId);</span>
<span class="nc" id="L1997">                    dwdbCertificateDataMapper.deleteCysrzsqbOptionsByDataId(dataId);</span>
<span class="nc" id="L1998">                    dwdbCertificateDataMapper.deleteCysrzsqbByDataId(dataId);</span>
<span class="nc" id="L1999">                    break;</span>
                case &quot;海船高级船员适任证书&quot;:
<span class="nc" id="L2001">                    dwdbCertificateDataMapper.deleteHcgjcyCapacityByDataId(dataId);</span>
<span class="nc" id="L2002">                    dwdbCertificateDataMapper.deleteHcgjcyFunctionByDataId(dataId);</span>
<span class="nc" id="L2003">                    dwdbCertificateDataMapper.deleteHcgjcyByDataId(dataId);</span>
<span class="nc" id="L2004">                    break;</span>
                case &quot;海船普通船员适任证书&quot;:
<span class="nc" id="L2006">                    dwdbCertificateDataMapper.deleteHcptcysrzCapacityByDataId(dataId);</span>
<span class="nc" id="L2007">                    dwdbCertificateDataMapper.deleteHcptcysrzFunctionByDataId(dataId);</span>
<span class="nc" id="L2008">                    dwdbCertificateDataMapper.deleteHcptcysrzByDataId(dataId);</span>
<span class="nc" id="L2009">                    break;</span>
                case &quot;海船船员培训合格证书&quot;:
<span class="nc" id="L2011">                    dwdbCertificateDataMapper.deleteHcpxhgTrainingByDataId(dataId);</span>
<span class="nc" id="L2012">                    dwdbCertificateDataMapper.deleteHcpxhgByDataId(dataId);</span>
<span class="nc" id="L2013">                    break;</span>
                case &quot;海上非自航船舶船员适任证书&quot;:
<span class="nc" id="L2015">                    dwdbCertificateDataMapper.deleteHsfhcysrzShipByDataId(dataId);</span>
<span class="nc" id="L2016">                    dwdbCertificateDataMapper.deleteHsfhcysrzByDataId(dataId);</span>
<span class="nc" id="L2017">                    break;</span>
                case &quot;海员外派机构资质证书&quot;:
<span class="nc" id="L2019">                    dwdbCertificateDataMapper.deleteHywpjgByDataId(dataId);</span>
<span class="nc" id="L2020">                    break;</span>
                case &quot;海船船员健康证明&quot;:
<span class="nc" id="L2022">                    dwdbCertificateDataMapper.deleteJkzmByDataId(dataId);</span>
<span class="nc" id="L2023">                    break;</span>
                case &quot;内河船舶船员适任证书&quot;:
<span class="nc" id="L2025">                    dwdbCertificateDataMapper.deleteNhcbcyByDataId(dataId);</span>
<span class="nc" id="L2026">                    break;</span>
                case &quot;海船船员内河航线行驶资格证明&quot;:
<span class="nc" id="L2028">                    dwdbCertificateDataMapper.deleteNhhxxsByDataId(dataId);</span>
<span class="nc" id="L2029">                    break;</span>
                case &quot;内河船舶船员培训合格证&quot;:
                case &quot;内河船舶船员特殊培训合格证&quot;:
<span class="nc" id="L2032">                    dwdbCertificateDataMapper.deleteNhpxhgItemByDataId(dataId);</span>
<span class="nc" id="L2033">                    dwdbCertificateDataMapper.deleteNhpxhgByDataId(dataId);</span>
<span class="nc" id="L2034">                    break;</span>
                case &quot;船员培训质量管理体系证书&quot;:
<span class="nc" id="L2036">                    dwdbCertificateDataMapper.deleteQmsByDataId(dataId);</span>
<span class="nc" id="L2037">                    break;</span>
                case &quot;海船船员培训许可证&quot;:
<span class="nc" id="L2039">                    dwdbCertificateDataMapper.deleteSeamanPermitItemByDataId(dataId);</span>
<span class="nc" id="L2040">                    dwdbCertificateDataMapper.deleteSeamanInfoByDataId(dataId);</span>
<span class="nc" id="L2041">                    dwdbCertificateDataMapper.deleteSeamanPermitByDataId(dataId);</span>
<span class="nc" id="L2042">                    break;</span>
                case &quot;特定航线江海直达船舶船员行驶资格证明培训合格证&quot;:
<span class="nc" id="L2044">                    dwdbCertificateDataMapper.deleteTdhxjhByDataId(dataId);</span>
<span class="nc" id="L2045">                    break;</span>
                case &quot;小型海船适任证书&quot;:
<span class="nc" id="L2047">                    dwdbCertificateDataMapper.deleteXhcsrzCapacityByDataId(dataId);</span>
<span class="nc" id="L2048">                    dwdbCertificateDataMapper.deleteXhcsrzFunctionByDataId(dataId);</span>
<span class="nc" id="L2049">                    dwdbCertificateDataMapper.deleteXhcsrzByDataId(dataId);</span>
<span class="nc" id="L2050">                    break;</span>
                case &quot;引航员船员适任证书&quot;:
<span class="nc" id="L2052">                    dwdbCertificateDataMapper.deleteYhysrzByDataId(dataId);</span>
<span class="nc" id="L2053">                    break;</span>
                case &quot;游艇驾驶证（海上）&quot;:
                case &quot;游艇驾驶证（内河）&quot;:
                case &quot;游艇驾驶证内河&quot;:
                case &quot;游艇驾驶证海上&quot;:
                case &quot;游艇驾驶证&quot;:
<span class="nc" id="L2059">                    dwdbCertificateDataMapper.deleteYtjszByDataId(dataId);</span>
<span class="nc" id="L2060">                    break;</span>
                case &quot;船上厨师培训合格证明&quot;:
<span class="nc" id="L2062">                    dwdbCertificateDataMapper.deleteCscspxByDataId(dataId);</span>
<span class="nc" id="L2063">                    break;</span>
                case &quot;船上膳食服务辅助人员培训证明&quot;:
<span class="nc" id="L2065">                    dwdbCertificateDataMapper.deleteCsssfzpxByDataId(dataId);</span>
<span class="nc" id="L2066">                    break;</span>
                case &quot;海船船员适任证书承认签证&quot;:
<span class="nc" id="L2068">                    dwdbCertificateDataMapper.deleteHccycrCapacityByDataId(dataId);</span>
<span class="nc" id="L2069">                    dwdbCertificateDataMapper.deleteHccycrFunctionByDataId(dataId);</span>
<span class="nc" id="L2070">                    dwdbCertificateDataMapper.deleteHccycrByDataId(dataId);</span>
<span class="nc" id="L2071">                    break;</span>
                case &quot;海上设施工作人员海上交通安全技能培训合格证明&quot;:
<span class="nc" id="L2073">                    dwdbCertificateDataMapper.deleteHsssjnByDataId(dataId);</span>
<span class="nc" id="L2074">                    break;</span>
                case &quot;海船不参加船员适任证书&quot;:
<span class="nc" id="L2076">                    dwdbCertificateDataMapper.deleteHcbcjcyCapacityByDataId(dataId);</span>
<span class="nc" id="L2077">                    dwdbCertificateDataMapper.deleteHcbcjcyByDataId(dataId);</span>
<span class="nc" id="L2078">                    break;</span>
                case &quot;内河船员培训许可证&quot;: // 新增内河船员培训许可证的处理分支
<span class="nc" id="L2080">                    dwdbCertificateDataMapper.deleteNhcyxkzItemsByDataId(dataId);</span>
<span class="nc" id="L2081">                    dwdbCertificateDataMapper.deleteNhcyxkzByDataId(dataId);</span>
<span class="nc" id="L2082">                    break;</span>
                case &quot;海船船员特免证明&quot;:
<span class="nc" id="L2084">                    dwdbCertificateDataMapper.deleteHccytmCapacityByDataId(dataId);</span>
<span class="nc" id="L2085">                    dwdbCertificateDataMapper.deleteHccytmFunctionByDataId(dataId);</span>
<span class="nc" id="L2086">                    dwdbCertificateDataMapper.deleteHccytmByDataId(dataId);</span>
<span class="nc" id="L2087">                    break;</span>
                case &quot;海船船员培训合格证承认签证&quot;:
<span class="nc" id="L2089">                    dwdbCertificateDataMapper.deleteHcpxqzTrainByDataId(dataId);</span>
<span class="nc" id="L2090">                    dwdbCertificateDataMapper.deleteHcpxhgqzByDataId(dataId);</span>
<span class="nc" id="L2091">                    break;</span>
                default:
<span class="nc" id="L2093">                    log.warn(&quot;未知的证书类型: {}, 无法清理对应的明细表数据&quot;, certificateType);</span>
                    break;
            }

<span class="nc" id="L2097">            log.info(&quot;已清理目标表中的数据, dataId: {}, certificateType: {}&quot;, dataId, certificateType);</span>
<span class="nc" id="L2098">        } catch (Exception e) {</span>
<span class="nc" id="L2099">            log.error(&quot;清理目标表数据失败, dataId: &quot; + dataId, e);</span>
<span class="nc" id="L2100">            throw new RuntimeException(&quot;清理目标表数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()),</span>
                    e);
<span class="nc" id="L2102">        }</span>
<span class="nc" id="L2103">    }</span>

    @Retryable(value = { SQLException.class,
            DataAccessException.class }, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    public void batchInsertAttributes(List&lt;DwdbCertificateDataAttribute&gt; attributes) {
        try {
<span class="nc" id="L2109">            dwdbCertificateDataMapper.batchInsertAttributes(attributes);</span>
<span class="nc" id="L2110">        } catch (Exception e) {</span>
<span class="nc" id="L2111">            log.error(&quot;插入属性失败，准备重试: {}&quot;, e.getMessage());</span>
<span class="nc" id="L2112">            throw e;</span>
<span class="nc" id="L2113">        }</span>
<span class="nc" id="L2114">    }</span>

    @Recover
    public void recover(Exception e, List&lt;DwdbCertificateDataAttribute&gt; attributes) {
<span class="nc" id="L2118">        log.error(&quot;重试3次后仍然失败，需要人工介入处理: {}&quot;, e.getMessage());</span>
        // 可以将失败的数据写入重试表或发送告警
<span class="nc" id="L2120">    }</span>

    /**
     * 初始化任务配置
     * 
     * @param taskName 任务名称
     * @return 任务配置
     */
    private DataReceptionTask initTask(String taskName) {
<span class="nc" id="L2129">        log.info(&quot;初始化任务配置: {}&quot;, taskName);</span>
<span class="nc" id="L2130">        DataReceptionTask task = new DataReceptionTask();</span>
<span class="nc" id="L2131">        task.setTaskName(taskName);</span>
<span class="nc" id="L2132">        task.setLastCompletedTime(LocalDateTime.of(1970, 1, 1, 0, 0, 0));</span>
<span class="nc" id="L2133">        task.setLastDataId(&quot;0&quot;);</span>

        // 保存到数据库
<span class="nc" id="L2136">        dataReceptionTaskMapper.insert(task);</span>

<span class="nc" id="L2138">        return task;</span>
    }

    // 添加处理机构查询辅助表的方法
    private void processQueryOrg(String issuedept, String certificateType) {
<span class="nc bnc" id="L2143" title="All 2 branches missed.">        if (StringUtils.isBlank(issuedept)) {</span>
<span class="nc" id="L2144">            return;</span>
        }

        // 检查缓存中是否已存在
<span class="nc bnc" id="L2148" title="All 2 branches missed.">        if (!queryOrgCache.containsKey(issuedept)) {</span>
            // 创建新记录
<span class="nc" id="L2150">            CertQueryOrg queryOrg = new CertQueryOrg();</span>
<span class="nc" id="L2151">            queryOrg.setQueryOrgId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2152">            queryOrg.setQueryOrgName(issuedept);</span>
<span class="nc" id="L2153">            queryOrg.setQueryOrgType(getOrgTypeByCategory(certificateType)); // 根据证书类型设置机构类型</span>
<span class="nc" id="L2154">            queryOrg.setRecCreateDate(new Date());</span>
<span class="nc" id="L2155">            queryOrg.setRecModifyDate(new Date());</span>
<span class="nc" id="L2156">            queryOrg.setNew(true);</span>

            // 添加到缓存和新增列表
<span class="nc" id="L2159">            queryOrgCache.put(issuedept, queryOrg);</span>
<span class="nc" id="L2160">            newQueryOrgs.add(queryOrg);</span>

<span class="nc" id="L2162">            log.debug(&quot;添加新的机构查询辅助表记录: {}, 类型: {}&quot;, issuedept, queryOrg.getQueryOrgType());</span>
        }
<span class="nc" id="L2164">    }</span>

    // 根据证书类型获取机构类型
    private String getOrgTypeByCategory(String certificateType) {
<span class="nc bnc" id="L2168" title="All 2 branches missed.">        if (StringUtils.isBlank(certificateType)) {</span>
<span class="nc" id="L2169">            return &quot;1&quot;; // 默认为签发机关</span>
        }

<span class="nc bnc" id="L2172" title="All 5 branches missed.">        switch (certificateType) {</span>
            case &quot;海员外派机构资质证书&quot;:
<span class="nc" id="L2174">                return &quot;4&quot;; // 外派机构</span>
            case &quot;海船船员健康证明&quot;:
<span class="nc" id="L2176">                return &quot;3&quot;; // 主管医师机构</span>
            case &quot;船员培训质量管理体系证书&quot;:
<span class="nc" id="L2178">                return &quot;5&quot;; // 审核机构</span>
            case &quot;船上厨师培训合格证明&quot;:
            case &quot;船上膳食服务辅助人员培训证明&quot;:
<span class="nc" id="L2181">                return &quot;2&quot;; // 培训机构</span>
            default:
<span class="nc" id="L2183">                return &quot;1&quot;; // 签发机关</span>
        }
    }

    /**
     * 获取映射后的职务名称
     * 先从缓存中查询，如果缓存没有命中或目标职务为空，则从数据库中查询
     * 如果数据库中也没有命中，则插入一条新记录并加入缓存
     * 
     * @param originalCapName 原始职务名称
     * @return 映射后的职务名称
     */
    private String getMappedCapacity(String originalCapName) {
<span class="nc bnc" id="L2196" title="All 2 branches missed.">        if (StringUtils.isBlank(originalCapName)) {</span>
<span class="nc" id="L2197">            return originalCapName;</span>
        }
<span class="nc" id="L2199">        originalCapName = originalCapName.trim();</span>
        // 先从缓存中查询
<span class="nc" id="L2201">        String mappedCapName = capMappingCache.get(originalCapName);</span>

        // 如果缓存没有命中或目标职务为空，则从数据库中查询
<span class="nc bnc" id="L2204" title="All 4 branches missed.">        if (mappedCapName == null || StringUtils.isBlank(mappedCapName)) {</span>
<span class="nc" id="L2205">            CertCapMapping mapping = certCapMappingMapper.selectBySourceCapName(originalCapName);</span>

<span class="nc bnc" id="L2207" title="All 2 branches missed.">            if (mapping != null) {</span>
                // 如果数据库中查到了映射关系，则加入到缓存中
<span class="nc" id="L2209">                mappedCapName = mapping.getDestCapName();</span>
<span class="nc" id="L2210">                capMappingCache.put(originalCapName, mappedCapName);</span>
            } else {
                // 如果数据库中没有记录，则插入一条新记录
<span class="nc" id="L2213">                CertCapMapping newMapping = new CertCapMapping();</span>
<span class="nc" id="L2214">                newMapping.setCapMappingId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2215">                newMapping.setSourceCapName(originalCapName);</span>
<span class="nc" id="L2216">                newMapping.setDestCapName(&quot;&quot;); // 目标职务设为空</span>
<span class="nc" id="L2217">                newMapping.setDelFlag(&quot;0&quot;);</span>
<span class="nc" id="L2218">                newMapping.setRecCreateDate(new Date());</span>
<span class="nc" id="L2219">                newMapping.setRecModifyDate(new Date());</span>

                try {
<span class="nc" id="L2222">                    certCapMappingMapper.insert(newMapping);</span>
                    // 加入到缓存中
<span class="nc" id="L2224">                    capMappingCache.put(originalCapName, &quot;&quot;);</span>
<span class="nc" id="L2225">                    log.info(&quot;新增职务映射记录：sourceCapName={}&quot;, originalCapName);</span>
<span class="nc" id="L2226">                } catch (Exception e) {</span>
<span class="nc" id="L2227">                    log.error(&quot;插入职务映射记录失败：sourceCapName={}, error={}&quot;, originalCapName, e.getMessage());</span>
<span class="nc" id="L2228">                }</span>
            }
        }

        // 如果没有找到映射或映射的目标职务为空，则使用原职务
<span class="nc bnc" id="L2233" title="All 4 branches missed.">        return (mappedCapName != null &amp;&amp; !StringUtils.isBlank(mappedCapName)) ? mappedCapName : originalCapName;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>