<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.example.certificate.service.impl</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <span class="el_package">com.example.certificate.service.impl</span></div><h1>com.example.certificate.service.impl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">5,077 of 5,328</td><td class="ctr2">4%</td><td class="bar">581 of 596</td><td class="ctr2">2%</td><td class="ctr1">345</td><td class="ctr2">355</td><td class="ctr1">1,272</td><td class="ctr2">1,331</td><td class="ctr1">26</td><td class="ctr2">30</td><td class="ctr1">1</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="CertificateEtlServiceImpl.html" class="el_class">CertificateEtlServiceImpl</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="114" height="10" title="5,003" alt="5,003"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="251" alt="251"/></td><td class="ctr2" id="c0">4%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="116" height="10" title="581" alt="581"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="15" alt="15"/></td><td class="ctr2" id="e0">2%</td><td class="ctr1" id="f0">338</td><td class="ctr2" id="g0">348</td><td class="ctr1" id="h0">1,256</td><td class="ctr2" id="i0">1,315</td><td class="ctr1" id="j0">19</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CertificateEtlServiceImpl$ProcessResult.html" class="el_class">CertificateEtlServiceImpl.ProcessResult</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="74" alt="74"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j1">7</td><td class="ctr2" id="k1">7</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>