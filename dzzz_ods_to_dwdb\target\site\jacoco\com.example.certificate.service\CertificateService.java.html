<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.service</a> &gt; <span class="el_source">CertificateService.java</span></div><h1>CertificateService.java</h1><pre class="source lang-java linenums">package com.example.certificate.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

<span class="fc" id="L9">@Slf4j</span>
@Service
<span class="fc" id="L11">public class CertificateService {</span>

    @Autowired
    private CertificateEtlService certificateEtlService;

    @PostConstruct
    public void init() {
        // 测试证照数据ETL流程
        try {
<span class="fc" id="L20">            String result = certificateEtlService.executeEtlTask(&quot;ods_certificate_data&quot;);</span>
<span class="fc" id="L21">            log.info(&quot;证照数据ETL测试结果: {}&quot;, result);</span>
<span class="nc" id="L22">        } catch (Exception e) {</span>
<span class="nc" id="L23">            log.error(&quot;证照数据ETL测试失败&quot;, e);</span>
<span class="fc" id="L24">        }</span>
<span class="fc" id="L25">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>