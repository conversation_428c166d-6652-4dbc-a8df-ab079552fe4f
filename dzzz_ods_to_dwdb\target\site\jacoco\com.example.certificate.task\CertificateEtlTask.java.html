<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateEtlTask.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.task</a> &gt; <span class="el_source">CertificateEtlTask.java</span></div><h1>CertificateEtlTask.java</h1><pre class="source lang-java linenums">package com.example.certificate.task;

import com.example.certificate.config.TaskConfig;
import com.example.certificate.service.CertificateEtlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

<span class="fc" id="L13">@Slf4j</span>
@Component
@EnableScheduling
<span class="fc" id="L16">public class CertificateEtlTask {</span>

<span class="fc" id="L18">    private final AtomicBoolean isRunning = new AtomicBoolean(false);</span>

    @Autowired
    private CertificateEtlService certificateEtlService;

    @Autowired
    private TaskConfig taskConfig;

    @Scheduled(cron = &quot;${task.certificate.cron}&quot;)
    public void execute() {
        // 如果任务正在运行，则跳过本次执行
<span class="nc bnc" id="L29" title="All 2 branches missed.">        if (!isRunning.compareAndSet(false, true)) {</span>
<span class="nc" id="L30">            log.info(&quot;上一次任务还在执行中，本次任务将跳过执行&quot;);</span>
<span class="nc" id="L31">            return;</span>
        }

        try {
<span class="nc" id="L35">            log.info(&quot;开始执行定时任务...&quot;);</span>
<span class="nc" id="L36">            String result = certificateEtlService.executeEtlTask(taskConfig.getTaskName());</span>
<span class="nc" id="L37">            log.info(&quot;定时任务执行结果: {}&quot;, result);</span>
<span class="nc" id="L38">        } catch (Exception e) {</span>
<span class="nc" id="L39">            log.error(&quot;定时任务执行异常&quot;, e);</span>
<span class="nc" id="L40">            System.exit(1); // 遇到不可恢复的异常时退出进程</span>
        } finally {
<span class="nc" id="L42">            isRunning.set(false);</span>
        }
<span class="nc" id="L44">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>