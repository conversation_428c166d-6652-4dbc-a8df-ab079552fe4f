<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateConverter</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.html" class="el_package">com.example.certificate.util</a> &gt; <span class="el_class">CertificateConverter</span></div><h1>CertificateConverter</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">14,010 of 14,062</td><td class="ctr2">0%</td><td class="bar">451 of 451</td><td class="ctr2">0%</td><td class="ctr1">281</td><td class="ctr2">284</td><td class="ctr1">1,873</td><td class="ctr2">1,886</td><td class="ctr1">41</td><td class="ctr2">44</td></tr></tfoot><tbody><tr><td id="a6"><a href="CertificateConverter.java.html#L609" class="el_method">convertCysrzsqbCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="882" alt="882"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f8">12</td><td class="ctr2" id="g8">12</td><td class="ctr1" id="h1">105</td><td class="ctr2" id="i1">105</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="CertificateConverter.java.html#L2916" class="el_method">convertHccytmCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="104" height="10" title="768" alt="768"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="107" height="10" title="44" alt="44"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">23</td><td class="ctr2" id="g2">23</td><td class="ctr1" id="h2">98</td><td class="ctr2" id="i2">98</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="CertificateConverter.java.html#L855" class="el_method">convertHcgjcyCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="753" alt="753"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="24" alt="24"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">13</td><td class="ctr2" id="g4">13</td><td class="ctr1" id="h3">90</td><td class="ctr2" id="i3">90</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a29"><a href="CertificateConverter.java.html#L1734" class="el_method">convertXhcsrzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="752" alt="752"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="24" alt="24"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f5">13</td><td class="ctr2" id="g5">13</td><td class="ctr1" id="h4">90</td><td class="ctr2" id="i4">90</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="CertificateConverter.java.html#L2544" class="el_method">convertHccycrCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="752" alt="752"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="24" alt="24"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">13</td><td class="ctr2" id="g6">13</td><td class="ctr1" id="h5">90</td><td class="ctr2" id="i5">90</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a12"><a href="CertificateConverter.java.html#L1017" class="el_method">convertHcptcysrzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="738" alt="738"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="24" alt="24"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">13</td><td class="ctr2" id="g7">13</td><td class="ctr1" id="h6">89</td><td class="ctr2" id="i6">89</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a26"><a href="CertificateConverter.java.html#L1569" class="el_method">convertSeamanPermitCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="99" height="10" title="731" alt="731"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f16">5</td><td class="ctr2" id="g16">5</td><td class="ctr1" id="h7">89</td><td class="ctr2" id="i7">89</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a27"><a href="CertificateConverter.java.html#L2102" class="el_method">convertSurfaceData(OdsCertificateData)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="663" alt="663"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="49" alt="49"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f0">38</td><td class="ctr2" id="g0">38</td><td class="ctr1" id="h0">143</td><td class="ctr2" id="i0">143</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="CertificateConverter.java.html#L3059" class="el_method">convertHcpxhgqzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="629" alt="629"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="30" alt="30"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f3">16</td><td class="ctr2" id="g3">16</td><td class="ctr1" id="h8">86</td><td class="ctr2" id="i8">86</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="CertificateConverter.java.html#L1987" class="el_method">convertHcpxhgCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="608" alt="608"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="18" alt="18"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f10">10</td><td class="ctr2" id="g10">10</td><td class="ctr1" id="h9">69</td><td class="ctr2" id="i9">69</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a30"><a href="CertificateConverter.java.html#L1881" class="el_method">convertYhysrzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="584" alt="584"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="44" height="10" title="18" alt="18"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">10</td><td class="ctr2" id="g11">10</td><td class="ctr1" id="h10">67</td><td class="ctr2" id="i10">67</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a3"><a href="CertificateConverter.java.html#L485" class="el_method">convertBcjhljzbCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="556" alt="556"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="14" alt="14"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f13">8</td><td class="ctr2" id="g13">8</td><td class="ctr1" id="h11">65</td><td class="ctr2" id="i11">65</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a31"><a href="CertificateConverter.java.html#L2319" class="el_method">convertYtjszCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="69" height="10" title="512" alt="512"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h13">57</td><td class="ctr2" id="i13">57</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a8"><a href="CertificateConverter.java.html#L2734" class="el_method">convertHcbcjcyCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="62" height="10" title="456" alt="456"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f14">6</td><td class="ctr2" id="g14">6</td><td class="ctr1" id="h14">57</td><td class="ctr2" id="i14">57</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a7"><a href="CertificateConverter.java.html#L761" class="el_method">convertGwccyCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="449" alt="449"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f23">3</td><td class="ctr2" id="g23">3</td><td class="ctr1" id="h15">55</td><td class="ctr2" id="i15">55</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a22"><a href="CertificateConverter.java.html#L2829" class="el_method">convertNhcyxkzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="59" height="10" title="435" alt="435"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f17">5</td><td class="ctr2" id="g17">5</td><td class="ctr1" id="h16">55</td><td class="ctr2" id="i16">55</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a20"><a href="CertificateConverter.java.html#L1300" class="el_method">convertJkzmCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="404" alt="404"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h17">49</td><td class="ctr2" id="i17">49</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a19"><a href="CertificateConverter.java.html#L1250" class="el_method">convertHywpjgCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="318" alt="318"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h21">37</td><td class="ctr2" id="i21">37</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a17"><a href="CertificateConverter.java.html#L1172" class="el_method">convertHsfhcysrzCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="42" height="10" title="314" alt="314"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="8" alt="8"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f18">5</td><td class="ctr2" id="g18">5</td><td class="ctr1" id="h19">44</td><td class="ctr2" id="i19">44</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a24"><a href="CertificateConverter.java.html#L1448" class="el_method">convertNhpxhg(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="306" alt="306"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f15">6</td><td class="ctr2" id="g15">6</td><td class="ctr1" id="h20">41</td><td class="ctr2" id="i20">41</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a18"><a href="CertificateConverter.java.html#L2681" class="el_method">convertHsssjnCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="292" alt="292"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h22">35</td><td class="ctr2" id="i22">35</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a2"><a href="CertificateConverter.java.html#L293" class="el_method">convert(OdsCertificateData)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="252" alt="252"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="20" alt="20"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f9">11</td><td class="ctr2" id="g9">11</td><td class="ctr1" id="h12">58</td><td class="ctr2" id="i12">58</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a4"><a href="CertificateConverter.java.html#L2440" class="el_method">convertCscspxCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="252" alt="252"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h23">31</td><td class="ctr2" id="i23">31</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a5"><a href="CertificateConverter.java.html#L2492" class="el_method">convertCsssfzpxCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="34" height="10" title="252" alt="252"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h24">31</td><td class="ctr2" id="i24">31</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a43"><a href="CertificateConverter.java.html#L402" class="el_method">validateRequiredFields(DwdbCertificateData)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="206" alt="206"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="112" height="10" title="46" alt="46"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f1">24</td><td class="ctr2" id="g1">24</td><td class="ctr1" id="h18">47</td><td class="ctr2" id="i18">47</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a21"><a href="CertificateConverter.java.html#L1372" class="el_method">convertNhcbcyCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="198" alt="198"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h26">25</td><td class="ctr2" id="i26">25</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a25"><a href="CertificateConverter.java.html#L1528" class="el_method">convertQmsCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="198" alt="198"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h27">25</td><td class="ctr2" id="i27">25</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a28"><a href="CertificateConverter.java.html#L1695" class="el_method">convertTdhxjhCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="183" alt="183"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h28">23</td><td class="ctr2" id="i28">23</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a23"><a href="CertificateConverter.java.html#L1412" class="el_method">convertNhhxxsCertificate(OdsCertificateData, Map, Set)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="163" alt="163"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h30">21</td><td class="ctr2" id="i30">21</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a39"><a href="CertificateConverter.java.html#L261" class="el_method">setBasicFields(OdsCertificateData, DwdbCertificateData)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="113" alt="113"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h25">29</td><td class="ctr2" id="i25">29</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a32"><a href="CertificateConverter.java.html#L157" class="el_method">generateCertificateIdentifier(DwdbCertificateData)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="87" alt="87"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f21">4</td><td class="ctr2" id="g21">4</td><td class="ctr1" id="h31">17</td><td class="ctr2" id="i31">17</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a0"><a href="CertificateConverter.java.html#L199" class="el_method">calculateCheckDigit(String)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="87" alt="87"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="16" alt="16"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f12">9</td><td class="ctr2" id="g12">9</td><td class="ctr1" id="h29">22</td><td class="ctr2" id="i29">22</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a33"><a href="CertificateConverter.java.html#L2417" class="el_method">getFirstNonBlankValue(Map, Set, String[])</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="46" alt="46"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">4</td><td class="ctr2" id="g22">4</td><td class="ctr1" id="h32">9</td><td class="ctr2" id="i32">9</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a15"><a href="CertificateConverter.java.html#L133" class="el_method">convertHolderCategory(String)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="21" alt="21"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f19">5</td><td class="ctr2" id="g19">5</td><td class="ctr1" id="h33">7</td><td class="ctr2" id="i33">7</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a16"><a href="CertificateConverter.java.html#L244" class="el_method">convertHolderTypeName(String)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="21" alt="21"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f20">5</td><td class="ctr2" id="g20">5</td><td class="ctr1" id="h34">7</td><td class="ctr2" id="i34">7</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a41"><a href="CertificateConverter.java.html#L591" class="el_method">setFieldAndMarkUsed(String, Map, Set, Consumer)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="14" alt="14"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h35">4</td><td class="ctr2" id="i37">4</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a34"><a href="CertificateConverter.java.html#L1516" class="el_method">getStringValue(JsonNode, String)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="11" alt="11"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h36">2</td><td class="ctr2" id="i38">2</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a38"><a href="CertificateConverter.java.html#L549" class="el_method">lambda$convertBcjhljzbCertificate$3(String)</a></td><td class="bar" id="b37"/><td class="ctr2" id="c40">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a37"><a href="CertificateConverter.java.html#L547" class="el_method">lambda$convertBcjhljzbCertificate$2(String)</a></td><td class="bar" id="b38"/><td class="ctr2" id="c41">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a36"><a href="CertificateConverter.java.html#L545" class="el_method">lambda$convertBcjhljzbCertificate$1(String)</a></td><td class="bar" id="b39"/><td class="ctr2" id="c42">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a35"><a href="CertificateConverter.java.html#L543" class="el_method">lambda$convertBcjhljzbCertificate$0(String)</a></td><td class="bar" id="b40"/><td class="ctr2" id="c43">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a40"><a href="CertificateConverter.java.html#L124" class="el_method">setCaches(Map, Map, Map)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="33" alt="33"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">0</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">0</td><td class="ctr2" id="i35">6</td><td class="ctr1" id="j41">0</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a1"><a href="CertificateConverter.java.html#L109" class="el_method">CertificateConverter(Map, Map, Map)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">0</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">0</td><td class="ctr2" id="i36">6</td><td class="ctr1" id="j42">0</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a42"><a href="CertificateConverter.java.html#L85" class="el_method">static {...}</a></td><td class="bar" id="b43"/><td class="ctr2" id="c2">100%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">0</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">0</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">0</td><td class="ctr2" id="k43">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>