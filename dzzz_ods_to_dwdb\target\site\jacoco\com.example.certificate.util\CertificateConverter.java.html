<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate.util</a> &gt; <span class="el_source">CertificateConverter.java</span></div><h1>CertificateConverter.java</h1><pre class="source lang-java linenums">package com.example.certificate.util;

import com.example.certificate.entity.aggregate.OdsCertificateData;
import com.example.certificate.entity.standard.DwdbCertificateData;
import com.example.certificate.entity.standard.CertTypeDirectory;
import com.example.certificate.entity.standard.DictYthOrgMapping;
import com.example.certificate.entity.standard.CtfSysDept;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqb;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbExperience;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCysrzsqbOptions;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcgjcyFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcptcysrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsfhcysrzShip;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHywpjg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailJkzm;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcbcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhhxxs;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhpxhgItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailQms;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanInfo;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermit;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailSeamanPermitItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailTdhxjh;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhg;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcpxhgTraining;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailXhcsrzFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYhysrzRange;
import com.example.certificate.mapper.standard.CertTypeDirectoryMapper;
import com.example.certificate.mapper.standard.DictYthOrgMappingMapper;
import com.example.certificate.mapper.standard.CtfSysDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import com.example.certificate.dto.CertificateConvertResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.HashSet;
import java.util.Set;
import com.example.certificate.entity.standard.DwdbCertificateDataAttribute;
import java.util.function.Consumer;
import java.util.Arrays;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailYtjsz;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCscspx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailCsssfzpx;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycr;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHccycrFunction;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHsssjn;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcy;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailHcbcjcyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailBcjhljzbCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailGwccyCapacity;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkzItem;
import com.example.certificate.entity.standard.DwdbCtfCertificateDetailNhcyxkz;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytm;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmCap;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHccytmFunc;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxhgqz;
import com.example.certificate.entity.standard.DwdbCtfCertDetailHcpxqzTrain;

<span class="fc" id="L85">@Slf4j</span>
@Component
@Service
public class CertificateConverter {

    @Autowired
    private CertTypeDirectoryMapper certTypeDirectoryMapper;

    @Autowired
    private DictYthOrgMappingMapper dictYthOrgMappingMapper;

    @Autowired
    private CtfSysDeptMapper ctfSysDeptMapper;

    // 修改缓存Map的声明方式，不再使用字段注入
    private Map&lt;String, CertTypeDirectory&gt; certTypeCache;
    private Map&lt;String, DictYthOrgMapping&gt; orgMappingCache;
    private Map&lt;String, CtfSysDept&gt; deptInfoCache;

    // 使用构造函数注入缓存，确保在创建Bean时就注入缓存
    @Autowired
    public CertificateConverter(
            @Qualifier(&quot;certTypeCache&quot;) Map&lt;String, CertTypeDirectory&gt; certTypeCache,
            @Qualifier(&quot;orgMappingCache&quot;) Map&lt;String, DictYthOrgMapping&gt; orgMappingCache,
<span class="fc" id="L109">            @Qualifier(&quot;deptInfoCache&quot;) Map&lt;String, CtfSysDept&gt; deptInfoCache) {</span>

<span class="fc" id="L111">        this.certTypeCache = certTypeCache;</span>
<span class="fc" id="L112">        this.orgMappingCache = orgMappingCache;</span>
<span class="fc" id="L113">        this.deptInfoCache = deptInfoCache;</span>

<span class="fc" id="L115">        log.info(&quot;CertificateConverter初始化完成，缓存对象注入成功&quot;);</span>
<span class="fc" id="L116">    }</span>

    // 添加方法接收静态缓存
    public void setCaches(
            Map&lt;String, CertTypeDirectory&gt; certTypeCache,
            Map&lt;String, DictYthOrgMapping&gt; orgMappingCache,
            Map&lt;String, CtfSysDept&gt; deptInfoCache) {

<span class="fc" id="L124">        this.certTypeCache = certTypeCache;</span>
<span class="fc" id="L125">        this.orgMappingCache = orgMappingCache;</span>
<span class="fc" id="L126">        this.deptInfoCache = deptInfoCache;</span>

<span class="fc" id="L128">        log.info(&quot;CertificateConverter手动设置缓存完成，缓存大小: certType={}, orgMapping={}, deptInfo={}&quot;,</span>
<span class="fc" id="L129">                certTypeCache.size(), orgMappingCache.size(), deptInfoCache.size());</span>
<span class="fc" id="L130">    }</span>

    private String convertHolderCategory(String category) {
<span class="nc bnc" id="L133" title="All 2 branches missed.">        if (StringUtils.isBlank(category)) {</span>
<span class="nc" id="L134">            return &quot;其他&quot;;</span>
        }
<span class="nc bnc" id="L136" title="All 4 branches missed.">        switch (category) {</span>
            case &quot;自然人&quot;:
<span class="nc" id="L138">                return &quot;111&quot;;</span>
            case &quot;法人或其他组织&quot;:
<span class="nc" id="L140">                return &quot;001&quot;;</span>
            case &quot;混合&quot;:
<span class="nc" id="L142">                return &quot;099&quot;;</span>
            default:
<span class="nc" id="L144">                return &quot;999&quot;;</span>
        }
    }

    /**
     * 生成电子证照唯一标识码
     * 规则: 根代码.证照类型代码.颁发机构代码.流水号.版本号.校验位
     * 
     * @param target 目标数据对象
     * @return 生成的电子证照唯一标识码
     */
    private String generateCertificateIdentifier(DwdbCertificateData target) {
        // 1. 电子证照根代码
<span class="nc" id="L157">        String rootCode = &quot;1.2.156.3005.2&quot;;</span>

        // 2. 证照类型代码
<span class="nc" id="L160">        String typeCode = target.getCertificateTypeCode();</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">        if (StringUtils.isBlank(typeCode)) {</span>
<span class="nc" id="L162">            log.warn(&quot;证照类型代码为空,使用默认值&quot;);</span>
<span class="nc" id="L163">            typeCode = &quot;UNKNOWN&quot;;</span>
        }

        // 3. 证照颁发机构代码
<span class="nc" id="L167">        String issuingCode = target.getCertificateIssuingAuthorityCode();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">        if (StringUtils.isBlank(issuingCode)) {</span>
<span class="nc" id="L169">            log.warn(&quot;证照颁发机构代码为空,使用默认值&quot;);</span>
<span class="nc" id="L170">            issuingCode = &quot;UNKNOWN&quot;;</span>
        }

        // 4. 流水号
<span class="nc" id="L174">        String serialNumber = target.getCertificateId();</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">        if (StringUtils.isBlank(serialNumber)) {</span>
<span class="nc" id="L176">            log.warn(&quot;证照ID为空,使用随机UUID&quot;);</span>
<span class="nc" id="L177">            serialNumber = UUID.randomUUID().toString().replace(&quot;-&quot;, &quot;&quot;);</span>
        }

        // 5. 版本号
<span class="nc" id="L181">        String versionNumber = &quot;999&quot;;</span>

        // 6. 计算校验位
<span class="nc" id="L184">        String checkContent = typeCode + issuingCode + serialNumber + versionNumber;</span>
<span class="nc" id="L185">        String checkDigit = calculateCheckDigit(checkContent);</span>

        // 7. 组合生成最终标识码
<span class="nc" id="L188">        return String.format(&quot;%s.%s.%s.%s.%s.%s&quot;,</span>
                rootCode, typeCode, issuingCode, serialNumber, versionNumber, checkDigit);
    }

    /**
     * 基于GB/T 17710-2008中的&quot;ISO/IEC 7064 MOD 37, 36&quot;校验算法计算校验位
     * 
     * @param content 需要计算校验位的内容
     * @return 校验位
     */
    private String calculateCheckDigit(String content) {
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (StringUtils.isBlank(content)) {</span>
<span class="nc" id="L200">            return &quot;0&quot;;</span>
        }

        // 字符集: 0-9, A-Z, 不包含I,O (共34个字符)
<span class="nc" id="L204">        String charset = &quot;0123456789ABCDEFGHJKLMNPQRSTUVWXYZ&quot;;</span>
<span class="nc" id="L205">        int modulus = 36; // MOD 37,36 中的36</span>

<span class="nc" id="L207">        int sum = 0;</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">        for (int i = 0; i &lt; content.length(); i++) {</span>
<span class="nc" id="L209">            char c = content.charAt(i);</span>
            int value;

<span class="nc bnc" id="L212" title="All 2 branches missed.">            if (Character.isDigit(c)) {</span>
<span class="nc" id="L213">                value = c - '0';</span>
<span class="nc bnc" id="L214" title="All 2 branches missed.">            } else if (Character.isLetter(c)) {</span>
<span class="nc" id="L215">                c = Character.toUpperCase(c);</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">                if (c == 'I')</span>
<span class="nc" id="L217">                    c = 'J'; // I被替换为J</span>
<span class="nc bnc" id="L218" title="All 2 branches missed.">                if (c == 'O')</span>
<span class="nc" id="L219">                    c = 'P'; // O被替换为P</span>
<span class="nc" id="L220">                value = c - 'A' + 10;</span>
            } else {
                // 忽略非字母数字字符
                continue;
            }

            // 计算加权和
<span class="nc" id="L227">            sum = (sum + value) % modulus;</span>
<span class="nc" id="L228">            sum = (sum * 2) % modulus;</span>
        }

        // 计算校验位
<span class="nc" id="L232">        int checkValue = (modulus - sum) % modulus;</span>

        // 确保checkValue在有效范围内
<span class="nc bnc" id="L235" title="All 4 branches missed.">        if (checkValue &lt; 0 || checkValue &gt;= charset.length()) {</span>
<span class="nc" id="L236">            checkValue = checkValue % charset.length();</span>
        }

        // 转换为字符
<span class="nc" id="L240">        return String.valueOf(charset.charAt(checkValue));</span>
    }

    private static String convertHolderTypeName(String holderType) {
<span class="nc bnc" id="L244" title="All 2 branches missed.">        if (StringUtils.isBlank(holderType)) {</span>
<span class="nc" id="L245">            return &quot;其他&quot;;</span>
        }
<span class="nc bnc" id="L247" title="All 4 branches missed.">        switch (holderType) {</span>
            case &quot;1&quot;:
<span class="nc" id="L249">                return &quot;统一社会信用代码&quot;;</span>
            case &quot;2&quot;:
<span class="nc" id="L251">                return &quot;公民身份号码&quot;;</span>
            case &quot;3&quot;:
<span class="nc" id="L253">                return &quot;护照号&quot;;</span>
            default:
<span class="nc" id="L255">                return &quot;其他&quot;;</span>
        }
    }

    private void setBasicFields(OdsCertificateData source, DwdbCertificateData target) {
        // 直接映射字段
<span class="nc" id="L261">        target.setDataId(source.getDataid());</span>
<span class="nc" id="L262">        target.setCertificateId(source.getCertificateid());</span>
<span class="nc" id="L263">        target.setTemplateId(source.getTemplateid());</span>
<span class="nc" id="L264">        target.setCertificateName(source.getCatalogname());</span>
<span class="nc" id="L265">        target.setCertificateNumber(source.getCertificatenumber());</span>
<span class="nc" id="L266">        target.setCertificateIssuingAuthorityName(source.getIssuedept());</span>
<span class="nc" id="L267">        target.setCertificateIssuedDate(source.getIssuedate());</span>
<span class="nc" id="L268">        target.setCertificateHolderName(source.getCertificateholder());</span>
<span class="nc" id="L269">        target.setCertificateHolderCode(source.getCertificateholdercode());</span>
<span class="nc" id="L270">        target.setCertificateEffectiveDate(source.getValidbegindate());</span>
<span class="nc" id="L271">        target.setCertificateExpiringDate(source.getValidenddate());</span>
<span class="nc" id="L272">        target.setIssueDeptCode2(source.getIssuedeptcode());</span>
<span class="nc" id="L273">        target.setCertificateAreaCode(source.getCertificateareacode());</span>
        // target.setSurfaceData(source.getSurfacedata());
<span class="nc" id="L275">        target.setCertificateStatus(source.getStatus());</span>
<span class="nc" id="L276">        target.setCreatorId(source.getCreator());</span>
<span class="nc" id="L277">        target.setCreateTime(source.getCreatetime());</span>
<span class="nc" id="L278">        target.setOperatorId(source.getOperator());</span>
<span class="nc" id="L279">        target.setUpdateTime(source.getUpdatetime());</span>
<span class="nc" id="L280">        target.setFilePath(source.getFilepath());</span>
<span class="nc" id="L281">        target.setSyncStatus(source.getSyncstatus());</span>
<span class="nc" id="L282">        target.setRemarks(source.getRemarks());</span>
<span class="nc" id="L283">        target.setDeptId(source.getDeptid());</span>
<span class="nc" id="L284">        target.setApplyNum(source.getApplynum());</span>
<span class="nc" id="L285">        target.setServeBusiness(source.getServebusiness());</span>
<span class="nc" id="L286">        target.setQzType(source.getQztype());</span>
<span class="nc" id="L287">        target.setDraftUrl(source.getDrafturl());</span>
<span class="nc" id="L288">        target.setSortName(source.getSortname());</span>
<span class="nc" id="L289">        target.setSealname(source.getSealname());</span>
<span class="nc" id="L290">    }</span>

    public DwdbCertificateData convert(OdsCertificateData source) throws Exception {
<span class="nc bnc" id="L293" title="All 2 branches missed.">        if (source == null) {</span>
<span class="nc" id="L294">            return null;</span>
        }

<span class="nc" id="L297">        DwdbCertificateData target = new DwdbCertificateData();</span>

        try {
            // 使用缓存获取证书类型信息
<span class="nc" id="L301">            String catalogid = source.getCatalogid();</span>
            // 游艇驾驶证海上、游艇驾驶证内河 兼容游艇驾驶证的配置
<span class="nc bnc" id="L303" title="All 2 branches missed.">            if (catalogid.equals(&quot;a3842fcff22549f8bc28b84d03c45ae7&quot;)</span>
<span class="nc bnc" id="L304" title="All 2 branches missed.">                    || catalogid.equals(&quot;83d2bb123ee54fdf8425126067b1b616&quot;)) {</span>
<span class="nc" id="L305">                catalogid = &quot;d89558cf8da043999a6c7b40f67a0a88&quot;;</span>
            }
<span class="nc" id="L307">            CertTypeDirectory certTypeInfo = certTypeCache.get(catalogid);</span>
<span class="nc bnc" id="L308" title="All 2 branches missed.">            if (certTypeInfo == null) {</span>
                // 缓存中没有，再从数据库查询
<span class="nc" id="L310">                certTypeInfo = certTypeDirectoryMapper.getCertTypeInfo(catalogid);</span>
<span class="nc bnc" id="L311" title="All 2 branches missed.">                if (certTypeInfo != null) {</span>
                    // 更新缓存
<span class="nc" id="L313">                    certTypeCache.put(catalogid, certTypeInfo);</span>
                }
            }

<span class="nc bnc" id="L317" title="All 2 branches missed.">            if (certTypeInfo != null) {</span>
                // log.info(&quot;证照类型信息: {}&quot;, certTypeInfo.toString());
<span class="nc" id="L319">                target.setCertificateTypeName(certTypeInfo.getCertificateTypeName());</span>
<span class="nc" id="L320">                target.setCertificateTypeCode(certTypeInfo.getCertificateTypeCode());</span>
<span class="nc" id="L321">                target.setCertificateDefineAuthorityName(certTypeInfo.getCertificateDefineAuthorityName());</span>
<span class="nc" id="L322">                target.setCertificateDefineAuthorityCode(certTypeInfo.getCertificateDefineAuthorityCode());</span>
<span class="nc" id="L323">                target.setRelatedItemName(certTypeInfo.getRelatedItemName());</span>
<span class="nc" id="L324">                target.setRelatedItemCode(certTypeInfo.getRelatedItemCode());</span>
<span class="nc" id="L325">                target.setCertificateHolderCategory(certTypeInfo.getCertificateHolderCategory());</span>
<span class="nc" id="L326">                target.setCertificateHolderCategoryName(</span>
<span class="nc" id="L327">                        convertHolderCategory(certTypeInfo.getCertificateHolderCategory()));</span>
<span class="nc" id="L328">                target.setValidityRange(certTypeInfo.getValidityRange());</span>
<span class="nc" id="L329">                target.setAffairType(certTypeInfo.getRelatedItemName());</span>
<span class="nc" id="L330">                target.setAffairId(certTypeInfo.getRelatedItemId()); // 使用新增的 relatedItemId 字段</span>
<span class="nc" id="L331">                target.setAffairNum(certTypeInfo.getRelatedItemCode());</span>
            } else {
<span class="nc" id="L333">                log.warn(&quot;未找到证照类型信息，templateId: {}&quot;, source.getTemplateid());</span>
<span class="nc" id="L334">                throw new Exception(&quot;未找到证照类型信息，查询条件为catalogId: &quot; + source.getCatalogid());</span>
            }

            // 3. 处理证照颁发机构代码
<span class="nc" id="L338">            DictYthOrgMapping orgMapping = orgMappingCache.get(source.getIssuedeptcode());</span>
            // log.info(&quot;orgMappingCache的总数: {}, issueDeptCode: {}&quot;, orgMappingCache.size(),
            // source.getIssuedeptcode());
<span class="nc bnc" id="L341" title="All 2 branches missed.">            if (orgMapping != null) {</span>
                // log.info(&quot;机构映射信息: {}&quot;, orgMapping.toString());
<span class="nc" id="L343">                String orgCode = orgMapping.getOrgCode();</span>

                // 检查机构编码前缀是否为010299（地方海事局或交通运输厅的机构）
<span class="nc bnc" id="L346" title="All 4 branches missed.">                if (orgCode != null &amp;&amp; orgCode.startsWith(&quot;010299&quot;)) {</span>
                    // 如果是地方海事局或交通运输厅的机构，issue_dept_code3保持和issue_dept_code2一致
<span class="nc" id="L348">                    target.setIssueDeptCode3(source.getIssuedeptcode());</span>
<span class="nc" id="L349">                    log.debug(&quot;检测到地方海事局或交通运输厅机构，orgCode: {}, issueDeptCode3设置为: {}&quot;, orgCode,</span>
<span class="nc" id="L350">                            source.getIssuedeptcode());</span>
                } else {
                    // 其他机构正常设置
<span class="nc" id="L353">                    target.setIssueDeptCode3(orgCode);</span>
                }

<span class="nc" id="L356">                target.setMsaOrgCode(orgCode);</span>

                // 获取统一社会信用代码
<span class="nc" id="L359">                CtfSysDept deptInfo = deptInfoCache.get(orgCode);</span>
<span class="nc bnc" id="L360" title="All 2 branches missed.">                if (deptInfo != null) {</span>
                    // log.info(&quot;机构信息: {}&quot;, deptInfo.toString());
<span class="nc" id="L362">                    target.setCertificateIssuingAuthorityCode(deptInfo.getUscc());</span>
                } else {
<span class="nc" id="L364">                    log.warn(&quot;未找到机构信息，orgCode: {}&quot;, orgCode);</span>
<span class="nc" id="L365">                    throw new Exception(&quot;获取统一社会信用代码时，未找到机构信息，orgCode: &quot; + orgCode);</span>
                }
<span class="nc" id="L367">            } else {</span>
                // log.warn(&quot;未找到机构映射信息，issueDeptCode: {}&quot;, source.getIssuedeptcode());
                // 先临时处理，写死
<span class="nc" id="L370">                target.setIssueDeptCode3(source.getIssuedeptcode());</span>
<span class="nc" id="L371">                target.setMsaOrgCode(source.getIssuedeptcode());</span>
<span class="nc" id="L372">                target.setCertificateIssuingAuthorityCode(&quot;555000007842028302&quot;);</span>
                // throw new Exception(&quot;获取统一社会信用代码时，未找到机构映射信息，issueDeptCode
            }

            // 4. 设置持证主体代码类型
<span class="nc" id="L377">            target.setCertificateHolderTypeName(convertHolderTypeName(source.getCertificateholdertype()));</span>

            // 5. 设置源系统代码
<span class="nc" id="L380">            target.setSourceCode(&quot;CYGLXT&quot;); // 默认值</span>

            // 6. 设置其他基础字段
<span class="nc" id="L383">            setBasicFields(source, target);</span>

            // 2. 生成电子证照唯一标识码
<span class="nc" id="L386">            target.setCertificateIdentifier(generateCertificateIdentifier(target));</span>

            // 7. 设置记录创建日期、记录修改日期
<span class="nc" id="L389">            target.setRecCreateDate(new Date());</span>
<span class="nc" id="L390">            target.setRecModifyDate(new Date());</span>

<span class="nc" id="L392">        } catch (Exception e) {</span>
<span class="nc" id="L393">            log.error(&quot;数据转换异常，sourceId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L394">            throw new RuntimeException(</span>
<span class="nc" id="L395">                    &quot;数据转换过程中发生异常&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L396">        }</span>

<span class="nc" id="L398">        return target;</span>
    }

    public void validateRequiredFields(DwdbCertificateData data) throws IllegalArgumentException {
<span class="nc bnc" id="L402" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getDataId())) {</span>
<span class="nc" id="L403">            throw new IllegalArgumentException(&quot;数据ID不能为空&quot;);</span>
        }
<span class="nc bnc" id="L405" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateId())) {</span>
<span class="nc" id="L406">            throw new IllegalArgumentException(&quot;证照标识码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L408" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getTemplateId())) {</span>
<span class="nc" id="L409">            throw new IllegalArgumentException(&quot;模板ID不能为空&quot;);</span>
        }
<span class="nc bnc" id="L411" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateName())) {</span>
<span class="nc" id="L412">            throw new IllegalArgumentException(&quot;证照名称不能为空&quot;);</span>
        }
<span class="nc bnc" id="L414" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateNumber())) {</span>
<span class="nc" id="L415">            throw new IllegalArgumentException(&quot;证照编号不能为空&quot;);</span>
        }
<span class="nc bnc" id="L417" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateIssuingAuthorityName())) {</span>
<span class="nc" id="L418">            throw new IllegalArgumentException(&quot;证照颁发机构不能为空&quot;);</span>
        }
<span class="nc bnc" id="L420" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateIssuingAuthorityCode())) {</span>
<span class="nc" id="L421">            throw new IllegalArgumentException(&quot;证照颁发机构代码（统一社会信用代码）&quot;);</span>
        }
<span class="nc bnc" id="L423" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateIssuedDate())) {</span>
<span class="nc" id="L424">            throw new IllegalArgumentException(&quot;颁证日期不能为空&quot;);</span>
        }
<span class="nc bnc" id="L426" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateHolderName())) {</span>
<span class="nc" id="L427">            throw new IllegalArgumentException(&quot;持证者名称不能为空&quot;);</span>
        }
<span class="nc bnc" id="L429" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateHolderCode())) {</span>
<span class="nc" id="L430">            throw new IllegalArgumentException(&quot;持证者代码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L432" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateExpiringDate())) {</span>
<span class="nc" id="L433">            throw new IllegalArgumentException(&quot;有效期截止日不能为空&quot;);</span>
        }
<span class="nc bnc" id="L435" title="All 2 branches missed.">        if (data.getRecCreateDate() == null) {</span>
<span class="nc" id="L436">            throw new IllegalArgumentException(&quot;创建记录日期不能为空&quot;);</span>
        }
<span class="nc bnc" id="L438" title="All 2 branches missed.">        if (data.getRecModifyDate() == null) {</span>
<span class="nc" id="L439">            throw new IllegalArgumentException(&quot;记录修改日期不能为空&quot;);</span>
        }
<span class="nc bnc" id="L441" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getSyncStatus())) {</span>
<span class="nc" id="L442">            throw new IllegalArgumentException(&quot;同步状态不能为空&quot;);</span>
        }
<span class="nc bnc" id="L444" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateTypeName())) {</span>
<span class="nc" id="L445">            throw new IllegalArgumentException(&quot;证照类型名称不能为空&quot;);</span>
        }
<span class="nc bnc" id="L447" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateTypeCode())) {</span>
<span class="nc" id="L448">            throw new IllegalArgumentException(&quot;证照类型代码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L450" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateDefineAuthorityName())) {</span>
<span class="nc" id="L451">            throw new IllegalArgumentException(&quot;证照定义机构不能为空&quot;);</span>
        }
<span class="nc bnc" id="L453" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateDefineAuthorityCode())) {</span>
<span class="nc" id="L454">            throw new IllegalArgumentException(&quot;证照定义机构代码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L456" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateHolderCategoryName())) {</span>
<span class="nc" id="L457">            throw new IllegalArgumentException(&quot;持证主体类别名称不能为空&quot;);</span>
        }
<span class="nc bnc" id="L459" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateIdentifier())) {</span>
<span class="nc" id="L460">            throw new IllegalArgumentException(&quot;电子证照唯一标识码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L462" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getCertificateHolderTypeName())) {</span>
<span class="nc" id="L463">            throw new IllegalArgumentException(&quot;持证主体代码类型不能为空&quot;);</span>
        }
<span class="nc bnc" id="L465" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getMsaOrgCode())) {</span>
<span class="nc" id="L466">            throw new IllegalArgumentException(&quot;MSA机构代码不能为空&quot;);</span>
        }
<span class="nc bnc" id="L468" title="All 2 branches missed.">        if (StringUtils.isBlank(data.getSourceCode())) {</span>
<span class="nc" id="L469">            throw new IllegalArgumentException(&quot;来源代码不能为空&quot;);</span>
        }
<span class="nc" id="L471">    }</span>

    /**
     * 处理不参加航行和轮机值班海船船员适任证书的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的证书明细对象
     */
    private Map&lt;String, Object&gt; convertBcjhljzbCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L485">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L486">        List&lt;DwdbCtfCertificateDetailBcjhljzbCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L490">            DwdbCtfCertificateDetailBcjhljzb mainData = new DwdbCtfCertificateDetailBcjhljzb();</span>
<span class="nc" id="L491">            String bcjhljzbId = UUID.randomUUID().toString();</span>
<span class="nc" id="L492">            mainData.setBcjhljzbId(bcjhljzbId);</span>
<span class="nc" id="L493">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L494">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L495">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L498">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameoftheHolder1);</span>
<span class="nc" id="L499">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameoftheHolder2);</span>
<span class="nc" id="L500">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L501">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L502">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L503">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L504">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L505">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L506">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L507">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L509">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L511">            setFieldAndMarkUsed(&quot;certificateIssuedDate1&quot;, dataMap, usedAttributes, mainData::setCertificateIssuedDate1);</span>
<span class="nc" id="L512">            setFieldAndMarkUsed(&quot;certificateIssuedDate2&quot;, dataMap, usedAttributes, mainData::setCertificateIssuedDate2);</span>
<span class="nc" id="L513">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L514">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L515">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L517">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);
<span class="nc" id="L519">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L520">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L521">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L522">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>

            // 2. 处理职务等级信息表数据
<span class="nc" id="L525">            boolean hasOthersCapacity = false; // 标记是否已经添加过&quot;其他/Others&quot;记录</span>
<span class="nc bnc" id="L526" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) { // 假设最多31组数据</span>
<span class="nc" id="L527">                int baseIndex = (i - 1) * 2 + 1; // 计算json属性的基础索引</span>

<span class="nc" id="L529">                String capacity1 = dataMap.get(&quot;capacity&quot; + baseIndex);</span>
<span class="nc" id="L530">                String capacity2 = dataMap.get(&quot;capacity&quot; + (baseIndex + 1));</span>
<span class="nc" id="L531">                String applivations1 = dataMap.get(&quot;applivations&quot; + baseIndex);</span>
<span class="nc" id="L532">                String applivations2 = dataMap.get(&quot;applivations&quot; + (baseIndex + 1));</span>

                // 检查是否有数据
<span class="nc bnc" id="L535" title="All 2 branches missed.">                if (StringUtils.isAllBlank(capacity1, capacity2, applivations1, applivations2)) {</span>
<span class="nc" id="L536">                    continue;</span>
                }

                // 去重逻辑：如果是&quot;其他/Others&quot;且已经添加过，则跳过
<span class="nc bnc" id="L540" title="All 6 branches missed.">                if (&quot;其他&quot;.equals(capacity1) &amp;&amp; &quot;Others&quot;.equals(capacity2) &amp;&amp; hasOthersCapacity) {</span>
                    // 标记这些字段为已使用，但不创建记录
<span class="nc" id="L542">                    setFieldAndMarkUsed(&quot;capacity&quot; + baseIndex, dataMap, usedAttributes, value -&gt; {</span>
<span class="nc" id="L543">                    });</span>
<span class="nc" id="L544">                    setFieldAndMarkUsed(&quot;capacity&quot; + (baseIndex + 1), dataMap, usedAttributes, value -&gt; {</span>
<span class="nc" id="L545">                    });</span>
<span class="nc" id="L546">                    setFieldAndMarkUsed(&quot;applivations&quot; + baseIndex, dataMap, usedAttributes, value -&gt; {</span>
<span class="nc" id="L547">                    });</span>
<span class="nc" id="L548">                    setFieldAndMarkUsed(&quot;applivations&quot; + (baseIndex + 1), dataMap, usedAttributes, value -&gt; {</span>
<span class="nc" id="L549">                    });</span>
<span class="nc" id="L550">                    continue;</span>
                }

                // 创建新的职务等级记录
<span class="nc" id="L554">                DwdbCtfCertificateDetailBcjhljzbCapacity capacity = new DwdbCtfCertificateDetailBcjhljzbCapacity();</span>
<span class="nc" id="L555">                capacity.setBcjhljzbCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L556">                capacity.setDataId(source.getDataid());</span>
<span class="nc" id="L557">                capacity.setBcjhljzbId(bcjhljzbId);</span>
<span class="nc" id="L558">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L559">                capacity.setUpdateTime(new Date());</span>

                // 设置字段值并标记已使用
<span class="nc" id="L562">                setFieldAndMarkUsed(&quot;capacity&quot; + baseIndex, dataMap, usedAttributes, capacity::setCapacity1);</span>
<span class="nc" id="L563">                setFieldAndMarkUsed(&quot;capacity&quot; + (baseIndex + 1), dataMap, usedAttributes, capacity::setCapacity2);</span>
<span class="nc" id="L564">                setFieldAndMarkUsed(&quot;applivations&quot; + baseIndex, dataMap, usedAttributes, capacity::setApplivations1);</span>
<span class="nc" id="L565">                setFieldAndMarkUsed(&quot;applivations&quot; + (baseIndex + 1), dataMap, usedAttributes,</span>
                        capacity::setApplivations2);

<span class="nc" id="L568">                capacityList.add(capacity);</span>

                // 如果这是&quot;其他/Others&quot;记录，标记已添加
<span class="nc bnc" id="L571" title="All 4 branches missed.">                if (&quot;其他&quot;.equals(capacity1) &amp;&amp; &quot;Others&quot;.equals(capacity2)) {</span>
<span class="nc" id="L572">                    hasOthersCapacity = true;</span>
                }
            }

            // 将数据放入结果Map
<span class="nc" id="L577">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L578">            result.put(&quot;capacityList&quot;, capacityList);</span>

<span class="nc" id="L580">        } catch (Exception e) {</span>
<span class="nc" id="L581">            log.error(&quot;解析不参加航行和轮机值班海船船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L582">            throw new RuntimeException(</span>
<span class="nc" id="L583">                    &quot;解析不参加航行和轮机值班海船船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L584">        }</span>

<span class="nc" id="L586">        return result;</span>
    }

    private void setFieldAndMarkUsed(String key, Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes, Consumer&lt;String&gt; setter) {
<span class="nc bnc" id="L591" title="All 2 branches missed.">        if (dataMap.containsKey(key)) {</span>
<span class="nc" id="L592">            setter.accept(dataMap.get(key));</span>
<span class="nc" id="L593">            usedAttributes.add(key);</span>
        }
<span class="nc" id="L595">    }</span>

    /**
     * 处理船员适任证书申请表的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的数据对象Map
     */
    private Map&lt;String, Object&gt; convertCysrzsqbCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L609">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L610">        String cysrzsqbId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L614">            DwdbCtfCertificateDetailCysrzsqb mainData = new DwdbCtfCertificateDetailCysrzsqb();</span>
<span class="nc" id="L615">            mainData.setCysrzsqbId(cysrzsqbId);</span>
<span class="nc" id="L616">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L617">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L618">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L621">            setFieldAndMarkUsed(&quot;sqYear&quot;, dataMap, usedAttributes, mainData::setSqYear);</span>
<span class="nc" id="L622">            setFieldAndMarkUsed(&quot;sqMonth&quot;, dataMap, usedAttributes, mainData::setSqMonth);</span>
<span class="nc" id="L623">            setFieldAndMarkUsed(&quot;sqDay&quot;, dataMap, usedAttributes, mainData::setSqDay);</span>
<span class="nc" id="L624">            setFieldAndMarkUsed(&quot;number&quot;, dataMap, usedAttributes, mainData::setNumber);</span>
<span class="nc" id="L625">            setFieldAndMarkUsed(&quot;name&quot;, dataMap, usedAttributes, mainData::setName);</span>
<span class="nc" id="L626">            setFieldAndMarkUsed(&quot;py&quot;, dataMap, usedAttributes, mainData::setPy);</span>
<span class="nc" id="L627">            setFieldAndMarkUsed(&quot;sex&quot;, dataMap, usedAttributes, mainData::setSex);</span>
<span class="nc" id="L628">            setFieldAndMarkUsed(&quot;idCode&quot;, dataMap, usedAttributes, mainData::setIdCode);</span>
<span class="nc" id="L629">            setFieldAndMarkUsed(&quot;birthYear&quot;, dataMap, usedAttributes, mainData::setBirthYear);</span>
<span class="nc" id="L630">            setFieldAndMarkUsed(&quot;birthMonth&quot;, dataMap, usedAttributes, mainData::setBirthMonth);</span>
<span class="nc" id="L631">            setFieldAndMarkUsed(&quot;birthDay&quot;, dataMap, usedAttributes, mainData::setBirthDay);</span>
<span class="nc" id="L632">            setFieldAndMarkUsed(&quot;company&quot;, dataMap, usedAttributes, mainData::setCompany);</span>
<span class="nc" id="L633">            setFieldAndMarkUsed(&quot;education&quot;, dataMap, usedAttributes, mainData::setEducation);</span>
<span class="nc" id="L634">            setFieldAndMarkUsed(&quot;school&quot;, dataMap, usedAttributes, mainData::setSchool);</span>
<span class="nc" id="L635">            setFieldAndMarkUsed(&quot;special&quot;, dataMap, usedAttributes, mainData::setSpecial);</span>
<span class="nc" id="L636">            setFieldAndMarkUsed(&quot;certNumber&quot;, dataMap, usedAttributes, mainData::setCertNumber);</span>
<span class="nc" id="L637">            setFieldAndMarkUsed(&quot;certDate&quot;, dataMap, usedAttributes, mainData::setCertDate);</span>
<span class="nc" id="L638">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setPhoto);</span>
<span class="nc" id="L639">            setFieldAndMarkUsed(&quot;gmdssJob&quot;, dataMap, usedAttributes, mainData::setGmdssJob);</span>
<span class="nc" id="L640">            setFieldAndMarkUsed(&quot;gmdssNo&quot;, dataMap, usedAttributes, mainData::setGmdssNo);</span>
<span class="nc" id="L641">            setFieldAndMarkUsed(&quot;gYear&quot;, dataMap, usedAttributes, mainData::setGYear);</span>
<span class="nc" id="L642">            setFieldAndMarkUsed(&quot;gMonth&quot;, dataMap, usedAttributes, mainData::setGMonth);</span>
<span class="nc" id="L643">            setFieldAndMarkUsed(&quot;gDay&quot;, dataMap, usedAttributes, mainData::setGDay);</span>
<span class="nc" id="L644">            setFieldAndMarkUsed(&quot;area2&quot;, dataMap, usedAttributes, mainData::setArea2);</span>
<span class="nc" id="L645">            setFieldAndMarkUsed(&quot;level2&quot;, dataMap, usedAttributes, mainData::setLevel2);</span>
<span class="nc" id="L646">            setFieldAndMarkUsed(&quot;job2&quot;, dataMap, usedAttributes, mainData::setJob2);</span>
<span class="nc" id="L647">            setFieldAndMarkUsed(&quot;limit&quot;, dataMap, usedAttributes, mainData::setLimitInfo);</span>
<span class="nc" id="L648">            setFieldAndMarkUsed(&quot;hgzNo&quot;, dataMap, usedAttributes, mainData::setHgzNo);</span>
<span class="nc" id="L649">            setFieldAndMarkUsed(&quot;year1&quot;, dataMap, usedAttributes, mainData::setYear1);</span>
<span class="nc" id="L650">            setFieldAndMarkUsed(&quot;month1&quot;, dataMap, usedAttributes, mainData::setMonth1);</span>
<span class="nc" id="L651">            setFieldAndMarkUsed(&quot;day1&quot;, dataMap, usedAttributes, mainData::setDay1);</span>
<span class="nc" id="L652">            setFieldAndMarkUsed(&quot;signature&quot;, dataMap, usedAttributes, mainData::setSignature);</span>
<span class="nc" id="L653">            setFieldAndMarkUsed(&quot;seal&quot;, dataMap, usedAttributes, mainData::setSeal);</span>
<span class="nc" id="L654">            setFieldAndMarkUsed(&quot;link&quot;, dataMap, usedAttributes, mainData::setLink);</span>
<span class="nc" id="L655">            setFieldAndMarkUsed(&quot;tel&quot;, dataMap, usedAttributes, mainData::setTel);</span>

<span class="nc" id="L657">            setFieldAndMarkUsed(&quot;certNo1&quot;, dataMap, usedAttributes, mainData::setCertNo1);</span>
<span class="nc" id="L658">            setFieldAndMarkUsed(&quot;sDay&quot;, dataMap, usedAttributes, mainData::setSDay);</span>
<span class="nc" id="L659">            setFieldAndMarkUsed(&quot;job1&quot;, dataMap, usedAttributes, mainData::setJob1);</span>
<span class="nc" id="L660">            setFieldAndMarkUsed(&quot;sYear&quot;, dataMap, usedAttributes, mainData::setSYear);</span>
<span class="nc" id="L661">            setFieldAndMarkUsed(&quot;level1&quot;, dataMap, usedAttributes, mainData::setLevel1);</span>
<span class="nc" id="L662">            setFieldAndMarkUsed(&quot;sMonth&quot;, dataMap, usedAttributes, mainData::setSMonth);</span>
<span class="nc" id="L663">            setFieldAndMarkUsed(&quot;area1&quot;, dataMap, usedAttributes, mainData::setArea1);</span>

            // 2. 处理服务资历信息表数据
<span class="nc" id="L666">            List&lt;DwdbCtfCertificateDetailCysrzsqbExperience&gt; experienceList = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L667" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) { // 假设最多10条记录</span>
                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L669">                String jobs = dataMap.get(&quot;jobs&quot; + i);</span>
<span class="nc" id="L670">                String shipName = dataMap.get(&quot;shipName&quot; + i);</span>
<span class="nc" id="L671">                String type = dataMap.get(&quot;type&quot; + i);</span>
<span class="nc" id="L672">                String class_ = dataMap.get(&quot;class&quot; + i);</span>
<span class="nc" id="L673">                String weight = dataMap.get(&quot;weight&quot; + i);</span>
<span class="nc" id="L674">                String rzDate = dataMap.get(&quot;rzDate&quot; + i);</span>
<span class="nc" id="L675">                String lzDate = dataMap.get(&quot;lzDate&quot; + i);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L678" title="All 4 branches missed.">                if (StringUtils.isBlank(jobs) &amp;&amp; StringUtils.isBlank(shipName) &amp;&amp;</span>
<span class="nc bnc" id="L679" title="All 4 branches missed.">                        StringUtils.isBlank(type) &amp;&amp; StringUtils.isBlank(class_) &amp;&amp;</span>
<span class="nc bnc" id="L680" title="All 4 branches missed.">                        StringUtils.isBlank(weight) &amp;&amp; StringUtils.isBlank(rzDate) &amp;&amp;</span>
<span class="nc bnc" id="L681" title="All 2 branches missed.">                        StringUtils.isBlank(lzDate)) {</span>
<span class="nc" id="L682">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L685">                DwdbCtfCertificateDetailCysrzsqbExperience exp = new DwdbCtfCertificateDetailCysrzsqbExperience();</span>
<span class="nc" id="L686">                exp.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L687">                exp.setCysrzsqbExperienceId(UUID.randomUUID().toString());</span>
<span class="nc" id="L688">                exp.setCysrzsqbId(cysrzsqbId);</span>
<span class="nc" id="L689">                exp.setCreateTime(new Date());</span>
<span class="nc" id="L690">                exp.setUpdateTime(new Date());</span>

                // 设置字段并记录已使用的属性
<span class="nc" id="L693">                setFieldAndMarkUsed(&quot;jobs&quot; + i, dataMap, usedAttributes, exp::setJobs);</span>
<span class="nc" id="L694">                setFieldAndMarkUsed(&quot;shipName&quot; + i, dataMap, usedAttributes, exp::setShipName);</span>
<span class="nc" id="L695">                setFieldAndMarkUsed(&quot;type&quot; + i, dataMap, usedAttributes, exp::setType);</span>
<span class="nc" id="L696">                setFieldAndMarkUsed(&quot;class&quot; + i, dataMap, usedAttributes, exp::setClass_);</span>
<span class="nc" id="L697">                setFieldAndMarkUsed(&quot;weight&quot; + i, dataMap, usedAttributes, exp::setWeight);</span>
<span class="nc" id="L698">                setFieldAndMarkUsed(&quot;rzDate&quot; + i, dataMap, usedAttributes, exp::setRzDate);</span>
<span class="nc" id="L699">                setFieldAndMarkUsed(&quot;lzDate&quot; + i, dataMap, usedAttributes, exp::setLzDate);</span>

<span class="nc" id="L701">                experienceList.add(exp);</span>

            }

            // 3. 处理选项信息表数据
<span class="nc" id="L706">            List&lt;DwdbCtfCertificateDetailCysrzsqbOptions&gt; optionsList = new ArrayList&lt;&gt;();</span>
            // 处理s1-s9选项
<span class="nc bnc" id="L708" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) {</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L711">                String s = dataMap.get(&quot;s&quot; + i);</span>
<span class="nc" id="L712">                String c = dataMap.get(&quot;c&quot; + i);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L715" title="All 4 branches missed.">                if (s == null &amp;&amp; c == null) {</span>
<span class="nc" id="L716">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L719">                String key = &quot;s&quot; + i;</span>
<span class="nc" id="L720">                String value = &quot;c&quot; + i;</span>

<span class="nc" id="L722">                DwdbCtfCertificateDetailCysrzsqbOptions opt = new DwdbCtfCertificateDetailCysrzsqbOptions();</span>
<span class="nc" id="L723">                opt.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L724">                opt.setCysrzsqbOptionsId(UUID.randomUUID().toString());</span>
<span class="nc" id="L725">                opt.setCysrzsqbId(cysrzsqbId);</span>
<span class="nc" id="L726">                opt.setCreateTime(new Date());</span>
<span class="nc" id="L727">                opt.setUpdateTime(new Date());</span>
<span class="nc" id="L728">                opt.setOptionKey(key);</span>
<span class="nc" id="L729">                opt.setOptionValue(value);</span>
<span class="nc" id="L730">                setFieldAndMarkUsed(key, dataMap, usedAttributes, opt::setOptionKey);</span>
<span class="nc" id="L731">                setFieldAndMarkUsed(value, dataMap, usedAttributes, opt::setOptionValue);</span>
<span class="nc" id="L732">                optionsList.add(opt);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L736">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L737">            result.put(&quot;experienceList&quot;, experienceList);</span>
<span class="nc" id="L738">            result.put(&quot;optionsList&quot;, optionsList);</span>

<span class="nc" id="L740">        } catch (Exception e) {</span>
<span class="nc" id="L741">            log.error(&quot;解析船员适任证书申请表数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L742">            throw new RuntimeException(&quot;解析船员适任证书申请表数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L743">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L744">        }</span>

<span class="nc" id="L746">        return result;</span>
    }

    /**
     * 处理公务船船员适任证书的数据转换
     * 
     * @param source          ODS证照数据
     * @param surfaceDataJson JSON格式的证照表面信息
     * @return 转换后的证书明细对象
     */
    private Map&lt;String, Object&gt; convertGwccyCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L761">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L762">        List&lt;DwdbCtfCertificateDetailGwccyCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L766">            DwdbCtfCertificateDetailGwccy target = new DwdbCtfCertificateDetailGwccy();</span>
<span class="nc" id="L767">            String gwccyId = UUID.randomUUID().toString();</span>
<span class="nc" id="L768">            target.setGwccyId(gwccyId);</span>
<span class="nc" id="L769">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L770">            target.setCreateTime(new Date());</span>
<span class="nc" id="L771">            target.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L774">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, target::setFullNameoftheHolder1);</span>
<span class="nc" id="L775">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, target::setFullNameoftheHolder2);</span>
<span class="nc" id="L776">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, target::setNationality1);</span>
<span class="nc" id="L777">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, target::setNationality2);</span>
<span class="nc" id="L778">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, target::setDateOfBirth1);</span>
<span class="nc" id="L779">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, target::setDateOfBirth2);</span>
<span class="nc" id="L780">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, target::setGender1);</span>
<span class="nc" id="L781">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, target::setGender2);</span>
<span class="nc" id="L782">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, target::setCertificateNo);</span>
<span class="nc" id="L783">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    target::setCertificateExpiringDate1);
<span class="nc" id="L785">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    target::setCertificateExpiringDate2);
<span class="nc" id="L787">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, target::setDateOfIssue1);</span>
<span class="nc" id="L788">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, target::setDateOfIssue2);</span>
<span class="nc" id="L789">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, target::setCertificateHolderName);</span>
<span class="nc" id="L790">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, target::setInformationOfPhoto);</span>
<span class="nc" id="L791">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, target::setIssuingAuthority1);</span>
<span class="nc" id="L792">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, target::setIssuingAuthority2);</span>
<span class="nc" id="L793">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, target::setOfficialUseOnly1);</span>
<span class="nc" id="L794">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, target::setOfficialUseOnly2);</span>

            // 2. 处理职务等级信息表数据
<span class="nc bnc" id="L797" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) { // 每次递增2,因为1,2是一组,3,4是一组</span>
<span class="nc" id="L798">                String baseIndex = String.valueOf(i);</span>
<span class="nc" id="L799">                String nextIndex = String.valueOf(i + 1);</span>

                // 获取当前组的所有字段值
<span class="nc" id="L802">                String gradwAndCapacity1 = dataMap.get(&quot;gradwAndCapacity&quot; + baseIndex);</span>
<span class="nc" id="L803">                String gradwAndCapacity2 = dataMap.get(&quot;gradwAndCapacity&quot; + nextIndex);</span>
<span class="nc" id="L804">                String alimitationsApplying1 = dataMap.get(&quot;limiationsApplying&quot; + baseIndex);</span>
<span class="nc" id="L805">                String alimitationsApplying2 = dataMap.get(&quot;limiationsApplying&quot; + nextIndex);</span>

                // 检查是否所有字段都为空
<span class="nc bnc" id="L808" title="All 2 branches missed.">                if (StringUtils.isAllBlank(gradwAndCapacity1, gradwAndCapacity2,</span>
                        alimitationsApplying1, alimitationsApplying2)) {
<span class="nc" id="L810">                    continue;</span>
                }

                // 创建新的职务等级记录
<span class="nc" id="L814">                DwdbCtfCertificateDetailGwccyCapacity capacity = new DwdbCtfCertificateDetailGwccyCapacity();</span>
<span class="nc" id="L815">                capacity.setGwccyCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L816">                capacity.setGwccyId(gwccyId);</span>
<span class="nc" id="L817">                capacity.setDataId(source.getDataid());</span>
<span class="nc" id="L818">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L819">                capacity.setUpdateTime(new Date());</span>

                // 设置字段值并标记已使用
<span class="nc" id="L822">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + baseIndex, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity1);
<span class="nc" id="L824">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + nextIndex, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity2);
<span class="nc" id="L826">                setFieldAndMarkUsed(&quot;limiationsApplying&quot; + baseIndex, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L828">                setFieldAndMarkUsed(&quot;limiationsApplying&quot; + nextIndex, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L831">                capacityList.add(capacity);</span>
            }

            // 将职务等级列表放入主表对象
<span class="nc" id="L835">            result.put(&quot;mainData&quot;, target);</span>
<span class="nc" id="L836">            result.put(&quot;capacityList&quot;, capacityList);</span>

<span class="nc" id="L838">        } catch (Exception e) {</span>
<span class="nc" id="L839">            log.error(&quot;解析公务船船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L840">            throw new RuntimeException(</span>
<span class="nc" id="L841">                    &quot;解析公务船船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L842">        }</span>

<span class="nc" id="L844">        return result;</span>
    }

    /**
     * 处理海船高级船员适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertHcgjcyCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L855">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L856">        String hcgjcyId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L860">            DwdbCtfCertificateDetailHcgjcy mainData = new DwdbCtfCertificateDetailHcgjcy();</span>
<span class="nc" id="L861">            mainData.setHcgjcyId(hcgjcyId);</span>
<span class="nc" id="L862">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L863">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L864">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L867">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L868">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L869">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L870">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L871">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L872">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L873">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L874">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L875">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L876">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L878">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L880">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L881">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L882">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L883">            setFieldAndMarkUsed(&quot;articleNumber1&quot;, dataMap, usedAttributes, mainData::setArticleNumber1);</span>
<span class="nc" id="L884">            setFieldAndMarkUsed(&quot;articleNumber2&quot;, dataMap, usedAttributes, mainData::setArticleNumber2);</span>
<span class="nc" id="L885">            setFieldAndMarkUsed(&quot;articleNumber3&quot;, dataMap, usedAttributes, mainData::setArticleNumber3);</span>
<span class="nc" id="L886">            setFieldAndMarkUsed(&quot;articleNumber4&quot;, dataMap, usedAttributes, mainData::setArticleNumber4);</span>
<span class="nc" id="L887">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L888">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L890">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);
<span class="nc" id="L892">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L893">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L894">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L895">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>

            // 2. 处理职务等级信息表数据
<span class="nc" id="L898">            List&lt;DwdbCtfCertificateDetailHcgjcyCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L901" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L902">                String capacityPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L903">                String capacityPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L906">                String gradwAndCapacity1 = getFirstNonBlankValue(dataMap, usedAttributes,</span>
                        &quot;gradwAndCapacity&quot; + capacityPrefix1, &quot;capacity&quot; + capacityPrefix1);// dataMap.get(&quot;gradwAndCapacity&quot;
                                                                                            // + capacityPrefix1);
<span class="nc" id="L909">                String gradwAndCapacity2 = getFirstNonBlankValue(dataMap, usedAttributes,</span>
                        &quot;gradwAndCapacity&quot; + capacityPrefix2, &quot;capacity&quot; + capacityPrefix2);// dataMap.get(&quot;gradwAndCapacity&quot;
                                                                                            // + capacityPrefix2);
<span class="nc" id="L912">                String alimitationsApplying1 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix1);</span>
<span class="nc" id="L913">                String alimitationsApplying2 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L916" title="All 4 branches missed.">                if (StringUtils.isBlank(gradwAndCapacity1) &amp;&amp; StringUtils.isBlank(gradwAndCapacity2) &amp;&amp;</span>
<span class="nc bnc" id="L917" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L918">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L921">                DwdbCtfCertificateDetailHcgjcyCapacity capacity = new DwdbCtfCertificateDetailHcgjcyCapacity();</span>
<span class="nc" id="L922">                capacity.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L923">                capacity.setHcgjcyCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L924">                capacity.setHcgjcyId(hcgjcyId);</span>
<span class="nc" id="L925">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L926">                capacity.setUpdateTime(new Date());</span>

                // 设置职务等级信息，同时记录已使用的属性
                // gradwAndCapacity1是职务等级中文，gradwAndCapacity2是职务等级英文
                // gradwAndCapacity3是职务等级补充中文，gradwAndCapacity4是职务等级补充英文
                // setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix1, dataMap,
                // usedAttributes, capacity::setGradwAndCapacity1);
                // setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix2, dataMap,
                // usedAttributes, capacity::setGradwAndCapacity2);
<span class="nc" id="L935">                capacity.setGradwAndCapacity1(gradwAndCapacity1);</span>
<span class="nc" id="L936">                capacity.setGradwAndCapacity2(gradwAndCapacity2);</span>
                // alimitationsApplying1是职务限制中文，alimitationsApplying2是职务限制英文
                // alimitationsApplying3是职务限制补充1，alimitationsApplying4是职务限制补充2
<span class="nc" id="L939">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L941">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L944">                capacityList.add(capacity);</span>
            }

            // 3. 处理职能信息表数据
<span class="nc" id="L948">            List&lt;DwdbCtfCertificateDetailHcgjcyFunction&gt; functionList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L951" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L952">                String functionPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L953">                String functionPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L956">                String function1 = dataMap.get(&quot;function&quot; + functionPrefix1);</span>
<span class="nc" id="L957">                String function2 = dataMap.get(&quot;function&quot; + functionPrefix2);</span>
<span class="nc" id="L958">                String level1 = dataMap.get(&quot;level&quot; + functionPrefix1);</span>
<span class="nc" id="L959">                String level2 = dataMap.get(&quot;level&quot; + functionPrefix2);</span>
<span class="nc" id="L960">                String limitationsApplying1 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix1);</span>
<span class="nc" id="L961">                String limitationsApplying2 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L964" title="All 4 branches missed.">                if (StringUtils.isBlank(function1) &amp;&amp; StringUtils.isBlank(function2) &amp;&amp;</span>
<span class="nc bnc" id="L965" title="All 4 branches missed.">                        StringUtils.isBlank(limitationsApplying1) &amp;&amp; StringUtils.isBlank(limitationsApplying2) &amp;&amp;</span>
<span class="nc bnc" id="L966" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2)) {</span>
<span class="nc" id="L967">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L970">                DwdbCtfCertificateDetailHcgjcyFunction func = new DwdbCtfCertificateDetailHcgjcyFunction();</span>
<span class="nc" id="L971">                func.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L972">                func.setHcgjcyFunctionId(UUID.randomUUID().toString());</span>
<span class="nc" id="L973">                func.setHcgjcyId(hcgjcyId);</span>
<span class="nc" id="L974">                func.setCreateTime(new Date());</span>
<span class="nc" id="L975">                func.setUpdateTime(new Date());</span>

                // 设置职能信息，同时记录已使用的属性
                // function1是中文，function2是英文
<span class="nc" id="L979">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix1, dataMap, usedAttributes, func::setFunction1);</span>
<span class="nc" id="L980">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix2, dataMap, usedAttributes, func::setFunction2);</span>

                // level1是中文，level2是英文
<span class="nc" id="L983">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix1, dataMap, usedAttributes, func::setLevel1);</span>
<span class="nc" id="L984">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix2, dataMap, usedAttributes, func::setLevel2);</span>

                // limitationsApplying1是中文，limitationsApplying2是英文
<span class="nc" id="L987">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix1, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying1);
<span class="nc" id="L989">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix2, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying2);

<span class="nc" id="L992">                functionList.add(func);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L996">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L997">            result.put(&quot;capacityList&quot;, capacityList);</span>
<span class="nc" id="L998">            result.put(&quot;functionList&quot;, functionList);</span>

<span class="nc" id="L1000">        } catch (Exception e) {</span>
<span class="nc" id="L1001">            log.error(&quot;解析海船高级船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1002">            throw new RuntimeException(</span>
<span class="nc" id="L1003">                    &quot;解析海船高级船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1004">        }</span>

<span class="nc" id="L1006">        return result;</span>
    }

    /**
     * 处理海船普通船员适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertHcptcysrzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1017">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1018">        String hcptcysrzId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L1022">            DwdbCtfCertificateDetailHcptcysrz mainData = new DwdbCtfCertificateDetailHcptcysrz();</span>
<span class="nc" id="L1023">            mainData.setHcptcysrzId(hcptcysrzId);</span>
<span class="nc" id="L1024">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L1025">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L1026">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L1029">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L1030">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L1031">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L1032">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L1033">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L1034">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L1035">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L1036">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L1037">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L1038">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L1040">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L1042">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L1043">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L1044">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L1045">            setFieldAndMarkUsed(&quot;capacity1&quot;, dataMap, usedAttributes, mainData::setCapacity1);</span>
<span class="nc" id="L1046">            setFieldAndMarkUsed(&quot;capacity2&quot;, dataMap, usedAttributes, mainData::setCapacity2);</span>
<span class="nc" id="L1047">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L1048">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L1050">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);
<span class="nc" id="L1052">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L1053">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L1054">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L1055">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>

            // 2. 处理职务等级信息表数据
<span class="nc" id="L1058">            List&lt;DwdbCtfCertificateDetailHcptcysrzCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L1061" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L1062">                String capacityPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L1063">                String capacityPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1066">                String gradwAndCapacity1 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix1);</span>
<span class="nc" id="L1067">                String gradwAndCapacity2 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix2);</span>
<span class="nc" id="L1068">                String alimitationsApplying1 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix1);</span>
<span class="nc" id="L1069">                String alimitationsApplying2 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1072" title="All 4 branches missed.">                if (StringUtils.isBlank(gradwAndCapacity1) &amp;&amp; StringUtils.isBlank(gradwAndCapacity2) &amp;&amp;</span>
<span class="nc bnc" id="L1073" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L1074">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L1077">                DwdbCtfCertificateDetailHcptcysrzCapacity capacity = new DwdbCtfCertificateDetailHcptcysrzCapacity();</span>
<span class="nc" id="L1078">                capacity.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L1079">                capacity.setHcptcysrzCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1080">                capacity.setHcptcysrzId(hcptcysrzId);</span>
<span class="nc" id="L1081">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L1082">                capacity.setUpdateTime(new Date());</span>

                // 设置职务等级信息，同时记录已使用的属性
<span class="nc" id="L1085">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity1);
<span class="nc" id="L1087">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity2);

                // alimitationsApplying1是职务限制中文，alimitationsApplying2是职务限制英文
                // alimitationsApplying3是职务限制补充1，alimitationsApplying4是职务限制补充2
<span class="nc" id="L1092">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L1094">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L1097">                capacityList.add(capacity);</span>
            }

            // 3. 处理职能信息表数据
<span class="nc" id="L1101">            List&lt;DwdbCtfCertificateDetailHcptcysrzFunction&gt; functionList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L1104" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L1105">                String functionPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L1106">                String functionPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1109">                String function1 = dataMap.get(&quot;function&quot; + functionPrefix1);</span>
<span class="nc" id="L1110">                String function2 = dataMap.get(&quot;function&quot; + functionPrefix2);</span>
<span class="nc" id="L1111">                String level1 = dataMap.get(&quot;level&quot; + functionPrefix1);</span>
<span class="nc" id="L1112">                String level2 = dataMap.get(&quot;level&quot; + functionPrefix2);</span>
<span class="nc" id="L1113">                String limitationsApplying1 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix1);</span>
<span class="nc" id="L1114">                String limitationsApplying2 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1117" title="All 4 branches missed.">                if (StringUtils.isBlank(function1) &amp;&amp; StringUtils.isBlank(function2) &amp;&amp;</span>
<span class="nc bnc" id="L1118" title="All 4 branches missed.">                        StringUtils.isBlank(limitationsApplying1) &amp;&amp; StringUtils.isBlank(limitationsApplying2) &amp;&amp;</span>
<span class="nc bnc" id="L1119" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2)) {</span>
<span class="nc" id="L1120">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L1123">                DwdbCtfCertificateDetailHcptcysrzFunction func = new DwdbCtfCertificateDetailHcptcysrzFunction();</span>
<span class="nc" id="L1124">                func.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L1125">                func.setHcptcysrzFunctionId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1126">                func.setHcptcysrzId(hcptcysrzId);</span>
<span class="nc" id="L1127">                func.setCreateTime(new Date());</span>
<span class="nc" id="L1128">                func.setUpdateTime(new Date());</span>

                // 设置职能信息，同时记录已使用的属性
                // function1是中文，function2是英文
<span class="nc" id="L1132">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix1, dataMap, usedAttributes, func::setFunction1);</span>
<span class="nc" id="L1133">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix2, dataMap, usedAttributes, func::setFunction2);</span>

                // level1是中文，level2是英文
<span class="nc" id="L1136">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix1, dataMap, usedAttributes, func::setLevel1);</span>
<span class="nc" id="L1137">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix2, dataMap, usedAttributes, func::setLevel2);</span>

                // limitationsApplying1是中文，limitationsApplying2是英文
<span class="nc" id="L1140">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix1, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying1);
<span class="nc" id="L1142">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix2, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying2);

<span class="nc" id="L1145">                functionList.add(func);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L1149">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L1150">            result.put(&quot;capacityList&quot;, capacityList);</span>
<span class="nc" id="L1151">            result.put(&quot;functionList&quot;, functionList);</span>
            // 打印functionList的汇总信息（个数）
<span class="nc" id="L1153">            log.debug(&quot;functionList的汇总信息（个数）: &quot; + functionList.size());</span>

<span class="nc" id="L1155">        } catch (Exception e) {</span>
<span class="nc" id="L1156">            log.error(&quot;解析海船普通船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1157">            throw new RuntimeException(</span>
<span class="nc" id="L1158">                    &quot;解析海船普通船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1159">        }</span>

<span class="nc" id="L1161">        return result;</span>
    }

    /**
     * 处理海上非自航船舶船员适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertHsfhcysrzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1172">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1173">        String hsfhcysrzId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L1177">            DwdbCtfCertificateDetailHsfhcysrz mainData = new DwdbCtfCertificateDetailHsfhcysrz();</span>
<span class="nc" id="L1178">            mainData.setHsfhcysrzId(hsfhcysrzId);</span>
<span class="nc" id="L1179">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L1180">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L1181">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L1184">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L1185">            setFieldAndMarkUsed(&quot;fullNameoftheHolder&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder);</span>
<span class="nc" id="L1186">            setFieldAndMarkUsed(&quot;dateOfBirth&quot;, dataMap, usedAttributes, mainData::setDateOfBirth);</span>
<span class="nc" id="L1187">            setFieldAndMarkUsed(&quot;placeOfBirth&quot;, dataMap, usedAttributes, mainData::setPlaceOfBirth);</span>
<span class="nc" id="L1188">            setFieldAndMarkUsed(&quot;dateOfExpirty&quot;, dataMap, usedAttributes, mainData::setDateOfExpirty);</span>
<span class="nc" id="L1189">            setFieldAndMarkUsed(&quot;dateOfIssue&quot;, dataMap, usedAttributes, mainData::setDateOfIssue);</span>
<span class="nc" id="L1190">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L1191">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L1192">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial);
<span class="nc" id="L1194">            setFieldAndMarkUsed(&quot;remark&quot;, dataMap, usedAttributes, mainData::setRemark);</span>

            // 2. 处理船舶信息表数据
<span class="nc" id="L1197">            List&lt;DwdbCtfCertificateDetailHsfhcysrzShip&gt; shipList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录（假设最多31条记录）
<span class="nc bnc" id="L1200" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) {</span>
<span class="nc" id="L1201">                String prefix = String.valueOf(i);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1204">                String shipType = dataMap.get(&quot;shipType&quot; + prefix);</span>
<span class="nc" id="L1205">                String level = dataMap.get(&quot;level&quot; + prefix);</span>
<span class="nc" id="L1206">                String capacity = dataMap.get(&quot;capacity&quot; + prefix);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1209" title="All 4 branches missed.">                if (StringUtils.isBlank(shipType) &amp;&amp; StringUtils.isBlank(level) &amp;&amp;</span>
<span class="nc bnc" id="L1210" title="All 2 branches missed.">                        StringUtils.isBlank(capacity)) {</span>
<span class="nc" id="L1211">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L1214">                DwdbCtfCertificateDetailHsfhcysrzShip ship = new DwdbCtfCertificateDetailHsfhcysrzShip();</span>
<span class="nc" id="L1215">                ship.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L1216">                ship.setHsfhcysrzShipId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1217">                ship.setHsfhcysrzId(hsfhcysrzId);</span>
<span class="nc" id="L1218">                ship.setCreateTime(new Date());</span>
<span class="nc" id="L1219">                ship.setUpdateTime(new Date());</span>

                // 设置船舶信息，同时记录已使用的属性
<span class="nc" id="L1222">                setFieldAndMarkUsed(&quot;shipType&quot; + prefix, dataMap, usedAttributes, ship::setShipType);</span>
<span class="nc" id="L1223">                setFieldAndMarkUsed(&quot;level&quot; + prefix, dataMap, usedAttributes, ship::setLevel);</span>
<span class="nc" id="L1224">                setFieldAndMarkUsed(&quot;capacity&quot; + prefix, dataMap, usedAttributes, ship::setCapacity);</span>

<span class="nc" id="L1226">                shipList.add(ship);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L1230">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L1231">            result.put(&quot;shipList&quot;, shipList);</span>

<span class="nc" id="L1233">        } catch (Exception e) {</span>
<span class="nc" id="L1234">            log.error(&quot;解析海上非自航船舶船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1235">            throw new RuntimeException(</span>
<span class="nc" id="L1236">                    &quot;解析海上非自航船舶船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1237">        }</span>

<span class="nc" id="L1239">        return result;</span>
    }

    /**
     * 处理海员外派机构资质证书的数据转换
     */
    private DwdbCtfCertificateDetailHywpjg convertHywpjgCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1250">        DwdbCtfCertificateDetailHywpjg target = new DwdbCtfCertificateDetailHywpjg();</span>

        try {
<span class="nc" id="L1253">            target.setHywpjgId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1254">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1255">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1256">            target.setUpdateTime(new Date());</span>
<span class="nc" id="L1257">            String yesOrNo9 = dataMap.get(&quot;yesOrNo9&quot;);</span>
            // 使用setFieldAndMarkUsed方法设置字段
<span class="nc" id="L1259">            setFieldAndMarkUsed(&quot;permitNumber1&quot;, dataMap, usedAttributes, target::setPermitNumber1);</span>
<span class="nc" id="L1260">            setFieldAndMarkUsed(&quot;permitNumber2&quot;, dataMap, usedAttributes, target::setPermitNumber2);</span>
<span class="nc" id="L1261">            setFieldAndMarkUsed(&quot;anThorityName1&quot;, dataMap, usedAttributes, target::setAnThorityName1);</span>
<span class="nc" id="L1262">            setFieldAndMarkUsed(&quot;anThorityName2&quot;, dataMap, usedAttributes, target::setAnThorityName2);</span>
<span class="nc" id="L1263">            setFieldAndMarkUsed(&quot;anThorityName3&quot;, dataMap, usedAttributes, target::setAnThorityName3);</span>
<span class="nc" id="L1264">            setFieldAndMarkUsed(&quot;anThorityName4&quot;, dataMap, usedAttributes, target::setAnThorityName4);</span>
<span class="nc" id="L1265">            setFieldAndMarkUsed(&quot;address1&quot;, dataMap, usedAttributes, target::setAddress1);</span>
<span class="nc" id="L1266">            setFieldAndMarkUsed(&quot;address2&quot;, dataMap, usedAttributes, target::setAddress2);</span>
<span class="nc" id="L1267">            setFieldAndMarkUsed(&quot;address3&quot;, dataMap, usedAttributes, target::setAddress3);</span>
<span class="nc" id="L1268">            setFieldAndMarkUsed(&quot;address4&quot;, dataMap, usedAttributes, target::setAddress4);</span>
<span class="nc" id="L1269">            setFieldAndMarkUsed(&quot;representative1&quot;, dataMap, usedAttributes, target::setRepresentative1);</span>
<span class="nc" id="L1270">            setFieldAndMarkUsed(&quot;representative2&quot;, dataMap, usedAttributes, target::setRepresentative2);</span>
<span class="nc" id="L1271">            setFieldAndMarkUsed(&quot;representative3&quot;, dataMap, usedAttributes, target::setRepresentative3);</span>
<span class="nc" id="L1272">            setFieldAndMarkUsed(&quot;representative4&quot;, dataMap, usedAttributes, target::setRepresentative4);</span>
<span class="nc" id="L1273">            setFieldAndMarkUsed(&quot;expiryDate1&quot;, dataMap, usedAttributes, target::setExpiryDate1);</span>
<span class="nc" id="L1274">            setFieldAndMarkUsed(&quot;expiryDate2&quot;, dataMap, usedAttributes, target::setExpiryDate2);</span>
<span class="nc" id="L1275">            setFieldAndMarkUsed(&quot;dateofIssue1&quot;, dataMap, usedAttributes, target::setDateOfIssue1);</span>
<span class="nc" id="L1276">            setFieldAndMarkUsed(&quot;dateofIssue3&quot;, dataMap, usedAttributes, target::setDateOfIssue3);</span>
<span class="nc" id="L1277">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, target::setIssuingAuthority1);</span>
<span class="nc" id="L1278">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, target::setIssuingAuthority2);</span>
<span class="nc" id="L1279">            setFieldAndMarkUsed(&quot;issuingAuthority3&quot;, dataMap, usedAttributes, target::setIssuingAuthority3);</span>
<span class="nc" id="L1280">            setFieldAndMarkUsed(&quot;issuingAuthority4&quot;, dataMap, usedAttributes, target::setIssuingAuthority4);</span>
<span class="nc" id="L1281">            setFieldAndMarkUsed(&quot;remark1&quot;, dataMap, usedAttributes, target::setRemark1);</span>
<span class="nc" id="L1282">            setFieldAndMarkUsed(&quot;remark2&quot;, dataMap, usedAttributes, target::setRemark2);</span>
<span class="nc" id="L1283">            setFieldAndMarkUsed(&quot;annualExamination&quot;, dataMap, usedAttributes, target::setAnnualExamination);</span>
<span class="nc" id="L1284">        } catch (Exception e) {</span>
<span class="nc" id="L1285">            log.error(&quot;解析海员外派机构资质证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1286">            throw new RuntimeException(&quot;解析海员外派机构资质证书数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L1287">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1288">        }</span>
<span class="nc" id="L1289">        return target;</span>
    }

    /**
     * 处理海船船员健康证明的数据转换
     */
    private DwdbCtfCertificateDetailJkzm convertJkzmCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1300">        DwdbCtfCertificateDetailJkzm target = new DwdbCtfCertificateDetailJkzm();</span>

        try {
<span class="nc" id="L1303">            target.setJkzmId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1304">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1305">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1306">            target.setUpdateTime(new Date());</span>

            // 设置JSON中的字段，同时记录已使用的属性
<span class="nc" id="L1309">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, target::setFullNameOfTheHolder1);</span>
<span class="nc" id="L1310">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, target::setFullNameOfTheHolder2);</span>
<span class="nc" id="L1311">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, target::setNationality1);</span>
<span class="nc" id="L1312">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, target::setNationality2);</span>
<span class="nc" id="L1313">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, target::setDateOfBirth1);</span>
<span class="nc" id="L1314">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, target::setDateOfBirth2);</span>
<span class="nc" id="L1315">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, target::setGender1);</span>
<span class="nc" id="L1316">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, target::setGender2);</span>
<span class="nc" id="L1317">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, target::setCertificateNo);</span>
<span class="nc" id="L1318">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    target::setCertificateExpiringDate1);
<span class="nc" id="L1320">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    target::setCertificateExpiringDate2);
<span class="nc" id="L1322">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, target::setDateOfIssue1);</span>
<span class="nc" id="L1323">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, target::setDateOfIssue2);</span>
<span class="nc" id="L1324">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, target::setCertificateHolderName);</span>
<span class="nc" id="L1325">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, target::setInformationOfPhoto);</span>
<span class="nc" id="L1326">            setFieldAndMarkUsed(&quot;yesOrNo1&quot;, dataMap, usedAttributes, target::setYesOrNo1);</span>
<span class="nc" id="L1327">            setFieldAndMarkUsed(&quot;yesOrNo2&quot;, dataMap, usedAttributes, target::setYesOrNo2);</span>
<span class="nc" id="L1328">            setFieldAndMarkUsed(&quot;yesOrNo3&quot;, dataMap, usedAttributes, target::setYesOrNo3);</span>
<span class="nc" id="L1329">            setFieldAndMarkUsed(&quot;yesOrNo4&quot;, dataMap, usedAttributes, target::setYesOrNo4);</span>
<span class="nc" id="L1330">            setFieldAndMarkUsed(&quot;yesOrNo5&quot;, dataMap, usedAttributes, target::setYesOrNo5);</span>
<span class="nc" id="L1331">            setFieldAndMarkUsed(&quot;yesOrNo6&quot;, dataMap, usedAttributes, target::setYesOrNo6);</span>
<span class="nc" id="L1332">            setFieldAndMarkUsed(&quot;yesOrNo7&quot;, dataMap, usedAttributes, target::setYesOrNo7);</span>
<span class="nc" id="L1333">            setFieldAndMarkUsed(&quot;yesOrNo8&quot;, dataMap, usedAttributes, target::setYesOrNo8);</span>

<span class="nc" id="L1335">            Object rawValue = dataMap.get(&quot;yesOrNo9&quot;);</span>
<span class="nc bnc" id="L1336" title="All 2 branches missed.">            if (rawValue instanceof String) {</span>
<span class="nc" id="L1337">                String value = ((String) rawValue).trim();</span>

                // 只处理英文逗号、空格，保留中文内容不被切割
<span class="nc" id="L1340">                value = value.replaceAll(&quot;,+&quot;, &quot;,&quot;); // 合并逗号，跳过空格</span>
<span class="nc" id="L1341">                value = value.replaceAll(&quot;^,+|,+$&quot;, &quot;&quot;); // 去掉首尾逗号</span>

<span class="nc" id="L1343">                dataMap.put(&quot;yesOrNo9&quot;, value);</span>
            }
<span class="nc" id="L1345">            setFieldAndMarkUsed(&quot;yesOrNo9&quot;, dataMap, usedAttributes, target::setYesOrNo9);</span>
<span class="nc" id="L1346">            setFieldAndMarkUsed(&quot;authorizingAuthority1&quot;, dataMap, usedAttributes, target::setAuthorizingAuthority1);</span>
<span class="nc" id="L1347">            setFieldAndMarkUsed(&quot;authorizingAuthority2&quot;, dataMap, usedAttributes, target::setAuthorizingAuthority2);</span>
<span class="nc" id="L1348">            setFieldAndMarkUsed(&quot;doctorName1&quot;, dataMap, usedAttributes, target::setDoctorName1);</span>
<span class="nc" id="L1349">            setFieldAndMarkUsed(&quot;doctorName2&quot;, dataMap, usedAttributes, target::setDoctorName2);</span>
<span class="nc" id="L1350">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, target::setIssuingAuthority1);</span>
<span class="nc" id="L1351">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, target::setIssuingAuthority2);</span>
<span class="nc" id="L1352">            setFieldAndMarkUsed(&quot;department1&quot;, dataMap, usedAttributes, target::setDepartment1);</span>
<span class="nc" id="L1353">            setFieldAndMarkUsed(&quot;department2&quot;, dataMap, usedAttributes, target::setDepartment2);</span>

<span class="nc" id="L1355">        } catch (Exception e) {</span>
<span class="nc" id="L1356">            log.error(&quot;解析海船船员健康证明数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1357">            throw new RuntimeException(</span>
<span class="nc" id="L1358">                    &quot;解析海船船员健康证明数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1359">        }</span>

<span class="nc" id="L1361">        return target;</span>
    }

    /**
     * 处理内河船舶船员适任证书的数据转换
     */
    private DwdbCtfCertificateDetailNhcbcy convertNhcbcyCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1372">        DwdbCtfCertificateDetailNhcbcy target = new DwdbCtfCertificateDetailNhcbcy();</span>

        try {
<span class="nc" id="L1375">            target.setNhcbcyId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1376">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1377">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1378">            target.setUpdateTime(new Date());</span>

            // 使用setFieldAndMarkUsed方法设置字段
<span class="nc" id="L1381">            setFieldAndMarkUsed(&quot;name&quot;, dataMap, usedAttributes, target::setName);</span>
<span class="nc" id="L1382">            setFieldAndMarkUsed(&quot;sex&quot;, dataMap, usedAttributes, target::setSex);</span>
<span class="nc" id="L1383">            setFieldAndMarkUsed(&quot;number&quot;, dataMap, usedAttributes, target::setNumber);</span>
<span class="nc" id="L1384">            setFieldAndMarkUsed(&quot;type&quot;, dataMap, usedAttributes, target::setType);</span>
<span class="nc" id="L1385">            setFieldAndMarkUsed(&quot;endDate&quot;, dataMap, usedAttributes, target::setEndDate);</span>
<span class="nc" id="L1386">            setFieldAndMarkUsed(&quot;signDept&quot;, dataMap, usedAttributes, target::setSignDept);</span>
<span class="nc" id="L1387">            setFieldAndMarkUsed(&quot;printNo&quot;, dataMap, usedAttributes, target::setPrintNo);</span>
<span class="nc" id="L1388">            setFieldAndMarkUsed(&quot;scope&quot;, dataMap, usedAttributes, target::setScope);</span>
<span class="nc" id="L1389">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, target::setPhoto);</span>
<span class="nc" id="L1390">            setFieldAndMarkUsed(&quot;year&quot;, dataMap, usedAttributes, target::setYear);</span>
<span class="nc" id="L1391">            setFieldAndMarkUsed(&quot;month&quot;, dataMap, usedAttributes, target::setMonth);</span>
<span class="nc" id="L1392">            setFieldAndMarkUsed(&quot;day&quot;, dataMap, usedAttributes, target::setDay);</span>
<span class="nc" id="L1393">            setFieldAndMarkUsed(&quot;issueDept&quot;, dataMap, usedAttributes, target::setIssueDept);</span>
<span class="nc" id="L1394">            setFieldAndMarkUsed(&quot;signDate&quot;, dataMap, usedAttributes, target::setSignDate);</span>

<span class="nc" id="L1396">        } catch (Exception e) {</span>
<span class="nc" id="L1397">            log.error(&quot;解析内河船舶船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1398">            throw new RuntimeException(</span>
<span class="nc" id="L1399">                    &quot;解析内河船舶船员适任证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1400">        }</span>
<span class="nc" id="L1401">        return target;</span>
    }

    /**
     * 处理海船船员内河航线行驶资格证明的数据转换
     */
    private DwdbCtfCertificateDetailNhhxxs convertNhhxxsCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1412">        DwdbCtfCertificateDetailNhhxxs target = new DwdbCtfCertificateDetailNhhxxs();</span>

        try {
<span class="nc" id="L1415">            target.setNhhxxsId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1416">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1417">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1418">            target.setUpdateTime(new Date());</span>

            // 使用setFieldAndMarkUsed方法设置字段
<span class="nc" id="L1421">            setFieldAndMarkUsed(&quot;name&quot;, dataMap, usedAttributes, target::setName);</span>
<span class="nc" id="L1422">            setFieldAndMarkUsed(&quot;gender&quot;, dataMap, usedAttributes, target::setGender);</span>
<span class="nc" id="L1423">            setFieldAndMarkUsed(&quot;creditCode&quot;, dataMap, usedAttributes, target::setCreditCode);</span>
<span class="nc" id="L1424">            setFieldAndMarkUsed(&quot;dateOfIssue&quot;, dataMap, usedAttributes, target::setDateOfIssue);</span>
<span class="nc" id="L1425">            setFieldAndMarkUsed(&quot;expiryDate&quot;, dataMap, usedAttributes, target::setExpiryDate);</span>
<span class="nc" id="L1426">            setFieldAndMarkUsed(&quot;issuingDate&quot;, dataMap, usedAttributes, target::setIssuingDate);</span>
<span class="nc" id="L1427">            setFieldAndMarkUsed(&quot;applivations&quot;, dataMap, usedAttributes, target::setApplivations);</span>
<span class="nc" id="L1428">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, target::setPhoto);</span>
<span class="nc" id="L1429">            setFieldAndMarkUsed(&quot;issuingAuthority&quot;, dataMap, usedAttributes, target::setIssuingAuthority);</span>
<span class="nc" id="L1430">            setFieldAndMarkUsed(&quot;numberOfCertificate&quot;, dataMap, usedAttributes, target::setNumberOfCertificate);</span>

<span class="nc" id="L1432">        } catch (Exception e) {</span>
<span class="nc" id="L1433">            log.error(&quot;解析海船船员内河航线行驶资格证明数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1434">            throw new RuntimeException(&quot;解析海船船员内河航线行驶资格证明数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage()</span>
<span class="nc" id="L1435">                    + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1436">        }</span>
<span class="nc" id="L1437">        return target;</span>
    }

    /**
     * 转换内河船舶船员培训合格证数据
     */
    private DwdbCtfCertificateDetailNhpxhg convertNhpxhg(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1448">        DwdbCtfCertificateDetailNhpxhg target = new DwdbCtfCertificateDetailNhpxhg();</span>

        try {
<span class="nc" id="L1451">            target.setNhpxhgId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1452">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1453">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1454">            target.setUpdateTime(new Date());</span>

            // 设置JSON中的字段，同时记录已使用的属性
<span class="nc" id="L1457">            setFieldAndMarkUsed(&quot;name&quot;, dataMap, usedAttributes, target::setName);</span>
<span class="nc" id="L1458">            setFieldAndMarkUsed(&quot;sex&quot;, dataMap, usedAttributes, target::setSex);</span>
<span class="nc" id="L1459">            setFieldAndMarkUsed(&quot;number&quot;, dataMap, usedAttributes, target::setNumber);</span>
<span class="nc" id="L1460">            setFieldAndMarkUsed(&quot;printNo&quot;, dataMap, usedAttributes, target::setPrintNo);</span>
<span class="nc" id="L1461">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, target::setPhoto);</span>
<span class="nc" id="L1462">            setFieldAndMarkUsed(&quot;year&quot;, dataMap, usedAttributes, target::setYear);</span>
<span class="nc" id="L1463">            setFieldAndMarkUsed(&quot;month&quot;, dataMap, usedAttributes, target::setMonth);</span>
<span class="nc" id="L1464">            setFieldAndMarkUsed(&quot;day&quot;, dataMap, usedAttributes, target::setDay);</span>

            // 处理培训项目子表数据
<span class="nc" id="L1467">            List&lt;DwdbCtfCertificateDetailNhpxhgItem&gt; items = new ArrayList&lt;&gt;();</span>

            // 遍历1-9,提取多条记录
<span class="nc bnc" id="L1470" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) {</span>
<span class="nc" id="L1471">                String prefix = String.valueOf(i);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1474">                String project = dataMap.get(&quot;project&quot; + prefix);</span>
<span class="nc" id="L1475">                String signDept = dataMap.get(&quot;signDept&quot; + prefix);</span>
<span class="nc" id="L1476">                String signDate = dataMap.get(&quot;signDate&quot; + prefix);</span>
<span class="nc" id="L1477">                String endDate = dataMap.get(&quot;endDate&quot; + prefix);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1480" title="All 4 branches missed.">                if (StringUtils.isBlank(project) &amp;&amp; StringUtils.isBlank(signDept) &amp;&amp;</span>
<span class="nc bnc" id="L1481" title="All 4 branches missed.">                        StringUtils.isBlank(signDate) &amp;&amp; StringUtils.isBlank(endDate)) {</span>
<span class="nc" id="L1482">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L1485">                DwdbCtfCertificateDetailNhpxhgItem item = new DwdbCtfCertificateDetailNhpxhgItem();</span>
<span class="nc" id="L1486">                item.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L1487">                item.setNhpxhgItemId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1488">                item.setNhpxhgId(target.getNhpxhgId());</span>
<span class="nc" id="L1489">                item.setCreateTime(new Date());</span>
<span class="nc" id="L1490">                item.setUpdateTime(new Date());</span>

                // 设置培训项目信息，同时记录已使用的属性
<span class="nc" id="L1493">                setFieldAndMarkUsed(&quot;project&quot; + prefix, dataMap, usedAttributes, item::setProject);</span>
<span class="nc" id="L1494">                setFieldAndMarkUsed(&quot;signDept&quot; + prefix, dataMap, usedAttributes, item::setSignDept);</span>
<span class="nc" id="L1495">                setFieldAndMarkUsed(&quot;signDate&quot; + prefix, dataMap, usedAttributes, item::setSignDate);</span>
<span class="nc" id="L1496">                setFieldAndMarkUsed(&quot;endDate&quot; + prefix, dataMap, usedAttributes, item::setEndDate);</span>

<span class="nc" id="L1498">                items.add(item);</span>
            }

<span class="nc" id="L1501">            target.setItems(items);</span>

<span class="nc" id="L1503">        } catch (Exception e) {</span>
<span class="nc" id="L1504">            log.error(&quot;转换内河船舶船员培训合格证数据失败&quot;, e);</span>
<span class="nc" id="L1505">            throw new RuntimeException(</span>
<span class="nc" id="L1506">                    &quot;转换内河船舶船员培训合格证数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1507">        }</span>

<span class="nc" id="L1509">        return target;</span>
    }

    /**
     * 从JSON中获取字符串值,不存在返回空字符串
     */
    private String getStringValue(JsonNode json, String field) {
<span class="nc" id="L1516">        JsonNode node = json.get(field);</span>
<span class="nc bnc" id="L1517" title="All 2 branches missed.">        return node != null ? node.asText() : &quot;&quot;;</span>
    }

    /**
     * 处理船员培训质量管理体系证书的数据转换
     */
    private DwdbCtfCertificateDetailQms convertQmsCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1528">        DwdbCtfCertificateDetailQms target = new DwdbCtfCertificateDetailQms();</span>

        try {
<span class="nc" id="L1531">            target.setQmsId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1532">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1533">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1534">            target.setUpdateTime(new Date());</span>

            // 使用setFieldAndMarkUsed方法设置字段
<span class="nc" id="L1537">            setFieldAndMarkUsed(&quot;number1&quot;, dataMap, usedAttributes, target::setNumber1);</span>
<span class="nc" id="L1538">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, target::setFullNameOfTheHolder1);</span>
<span class="nc" id="L1539">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, target::setFullNameOfTheHolder2);</span>
<span class="nc" id="L1540">            setFieldAndMarkUsed(&quot;year1&quot;, dataMap, usedAttributes, target::setYear1);</span>
<span class="nc" id="L1541">            setFieldAndMarkUsed(&quot;month1&quot;, dataMap, usedAttributes, target::setMonth1);</span>
<span class="nc" id="L1542">            setFieldAndMarkUsed(&quot;day1&quot;, dataMap, usedAttributes, target::setDay1);</span>
<span class="nc" id="L1543">            setFieldAndMarkUsed(&quot;certificateExpiringDate&quot;, dataMap, usedAttributes, target::setCertificateExpiringDate);</span>
<span class="nc" id="L1544">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    target::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L1546">            setFieldAndMarkUsed(&quot;evaluationOrganization1&quot;, dataMap, usedAttributes, target::setEvaluationOrganization1);</span>
<span class="nc" id="L1547">            setFieldAndMarkUsed(&quot;evaluationOrganization2&quot;, dataMap, usedAttributes, target::setEvaluationOrganization2);</span>
<span class="nc" id="L1548">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, target::setDateOfIssue1);</span>
<span class="nc" id="L1549">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, target::setDateOfIssue2);</span>
<span class="nc" id="L1550">            setFieldAndMarkUsed(&quot;number2&quot;, dataMap, usedAttributes, target::setNumber2);</span>
<span class="nc" id="L1551">            setFieldAndMarkUsed(&quot;remarks&quot;, dataMap, usedAttributes, target::setRemarks);</span>

<span class="nc" id="L1553">        } catch (Exception e) {</span>
<span class="nc" id="L1554">            log.error(&quot;解析船员培训质量管理体系证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1555">            throw new RuntimeException(</span>
<span class="nc" id="L1556">                    &quot;解析船员培训质量管理体系证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1557">        }</span>
<span class="nc" id="L1558">        return target;</span>
    }

    /**
     * 处理海船船员培训许可证的数据转换
     */
    private Map&lt;String, Object&gt; convertSeamanPermitCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1569">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1570">        String seamanPermitId = UUID.randomUUID().toString();</span>
<span class="nc" id="L1571">        List&lt;DwdbCtfCertificateDetailSeamanPermit&gt; permitList = new ArrayList&lt;&gt;(); // 新增列表</span>

        try {
            // 1. 处理个人信息表
<span class="nc" id="L1575">            DwdbCtfCertificateDetailSeamanInfo seamanInfo = new DwdbCtfCertificateDetailSeamanInfo();</span>
<span class="nc" id="L1576">            seamanInfo.setSeamanInfoId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1577">            seamanInfo.setDataId(source.getDataid());</span>
<span class="nc" id="L1578">            seamanInfo.setCreateTime(new Date());</span>
<span class="nc" id="L1579">            seamanInfo.setUpdateTime(new Date());</span>

            // 使用setFieldAndMarkUsed方法设置字段
<span class="nc" id="L1582">            setFieldAndMarkUsed(&quot;number&quot;, dataMap, usedAttributes, seamanInfo::setNumber);</span>
<span class="nc" id="L1583">            setFieldAndMarkUsed(&quot;nameCn&quot;, dataMap, usedAttributes, seamanInfo::setNameCn);</span>
<span class="nc" id="L1584">            setFieldAndMarkUsed(&quot;nameEn&quot;, dataMap, usedAttributes, seamanInfo::setNameEn);</span>
<span class="nc" id="L1585">            setFieldAndMarkUsed(&quot;sexCn&quot;, dataMap, usedAttributes, seamanInfo::setSexCn);</span>
<span class="nc" id="L1586">            setFieldAndMarkUsed(&quot;sexEn&quot;, dataMap, usedAttributes, seamanInfo::setSexEn);</span>
<span class="nc" id="L1587">            setFieldAndMarkUsed(&quot;countryCn&quot;, dataMap, usedAttributes, seamanInfo::setCountryCn);</span>
<span class="nc" id="L1588">            setFieldAndMarkUsed(&quot;countryEn&quot;, dataMap, usedAttributes, seamanInfo::setCountryEn);</span>
<span class="nc" id="L1589">            setFieldAndMarkUsed(&quot;birthCn&quot;, dataMap, usedAttributes, seamanInfo::setBirthCn);</span>
<span class="nc" id="L1590">            setFieldAndMarkUsed(&quot;birthEn&quot;, dataMap, usedAttributes, seamanInfo::setBirthEn);</span>
<span class="nc" id="L1591">            setFieldAndMarkUsed(&quot;fileNoCn&quot;, dataMap, usedAttributes, seamanInfo::setFileNoCn);</span>
<span class="nc" id="L1592">            setFieldAndMarkUsed(&quot;fileNoEn&quot;, dataMap, usedAttributes, seamanInfo::setFileNoEn);</span>
<span class="nc" id="L1593">            setFieldAndMarkUsed(&quot;qualificationCn&quot;, dataMap, usedAttributes, seamanInfo::setQualificationCn);</span>
<span class="nc" id="L1594">            setFieldAndMarkUsed(&quot;qualificationEn&quot;, dataMap, usedAttributes, seamanInfo::setQualificationEn);</span>
<span class="nc" id="L1595">            setFieldAndMarkUsed(&quot;initialDateCn&quot;, dataMap, usedAttributes, seamanInfo::setInitialDateCn);</span>
<span class="nc" id="L1596">            setFieldAndMarkUsed(&quot;initialDateEn&quot;, dataMap, usedAttributes, seamanInfo::setInitialDateEn);</span>
<span class="nc" id="L1597">            setFieldAndMarkUsed(&quot;expiryDateCn&quot;, dataMap, usedAttributes, seamanInfo::setExpiryDateCn);</span>
<span class="nc" id="L1598">            setFieldAndMarkUsed(&quot;expiryDateEn&quot;, dataMap, usedAttributes, seamanInfo::setExpiryDateEn);</span>
<span class="nc" id="L1599">            setFieldAndMarkUsed(&quot;signDeptCn&quot;, dataMap, usedAttributes, seamanInfo::setSignDeptCn);</span>
<span class="nc" id="L1600">            setFieldAndMarkUsed(&quot;signDeptEn&quot;, dataMap, usedAttributes, seamanInfo::setSignDeptEn);</span>
<span class="nc" id="L1601">            setFieldAndMarkUsed(&quot;officeOfIssueCn&quot;, dataMap, usedAttributes, seamanInfo::setOfficeOfIssueCn);</span>
<span class="nc" id="L1602">            setFieldAndMarkUsed(&quot;officeOfIssueEn&quot;, dataMap, usedAttributes, seamanInfo::setOfficeOfIssueEn);</span>
<span class="nc" id="L1603">            setFieldAndMarkUsed(&quot;date&quot;, dataMap, usedAttributes, seamanInfo::setDate);</span>
<span class="nc" id="L1604">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, seamanInfo::setPhoto);</span>
<span class="nc" id="L1605">            setFieldAndMarkUsed(&quot;year&quot;, dataMap, usedAttributes, seamanInfo::setYear);</span>
<span class="nc" id="L1606">            setFieldAndMarkUsed(&quot;month&quot;, dataMap, usedAttributes, seamanInfo::setMonth);</span>
<span class="nc" id="L1607">            setFieldAndMarkUsed(&quot;day&quot;, dataMap, usedAttributes, seamanInfo::setDay);</span>

            // 2. 处理主表信息
<span class="nc" id="L1610">            DwdbCtfCertificateDetailSeamanPermit seamanPermit = new DwdbCtfCertificateDetailSeamanPermit();</span>
<span class="nc" id="L1611">            seamanPermit.setSeamanPermitId(seamanPermitId);</span>
<span class="nc" id="L1612">            seamanPermit.setDataId(source.getDataid());</span>
<span class="nc" id="L1613">            seamanPermit.setCreateTime(new Date());</span>
<span class="nc" id="L1614">            seamanPermit.setUpdateTime(new Date());</span>

            // 设置主表字段，同时记录已使用的属性
<span class="nc" id="L1617">            setFieldAndMarkUsed(&quot;permitNumber1&quot;, dataMap, usedAttributes, seamanPermit::setPermitNumber1);</span>
<span class="nc" id="L1618">            setFieldAndMarkUsed(&quot;permitNumber2&quot;, dataMap, usedAttributes, seamanPermit::setPermitNumber2);</span>
<span class="nc" id="L1619">            setFieldAndMarkUsed(&quot;anThorityName1&quot;, dataMap, usedAttributes, seamanPermit::setAnThorityName1);</span>
<span class="nc" id="L1620">            setFieldAndMarkUsed(&quot;anThorityName2&quot;, dataMap, usedAttributes, seamanPermit::setAnThorityName2);</span>
<span class="nc" id="L1621">            setFieldAndMarkUsed(&quot;trainingInstitutionCode1&quot;, dataMap, usedAttributes,</span>
                    seamanPermit::setTrainingInstitutionCode1);
<span class="nc" id="L1623">            setFieldAndMarkUsed(&quot;representative1&quot;, dataMap, usedAttributes, seamanPermit::setRepresentative1);</span>
<span class="nc" id="L1624">            setFieldAndMarkUsed(&quot;representative2&quot;, dataMap, usedAttributes, seamanPermit::setRepresentative2);</span>
<span class="nc" id="L1625">            setFieldAndMarkUsed(&quot;trainingProgram1&quot;, dataMap, usedAttributes, seamanPermit::setTrainingProgram1);</span>
<span class="nc" id="L1626">            setFieldAndMarkUsed(&quot;trainingProgram2&quot;, dataMap, usedAttributes, seamanPermit::setTrainingProgram2);</span>
<span class="nc" id="L1627">            setFieldAndMarkUsed(&quot;registeredAddress1&quot;, dataMap, usedAttributes, seamanPermit::setRegisteredAddress1);</span>
<span class="nc" id="L1628">            setFieldAndMarkUsed(&quot;registeredAddress2&quot;, dataMap, usedAttributes, seamanPermit::setRegisteredAddress2);</span>
<span class="nc" id="L1629">            setFieldAndMarkUsed(&quot;trainingLocation1&quot;, dataMap, usedAttributes, seamanPermit::setTrainingLocation1);</span>
<span class="nc" id="L1630">            setFieldAndMarkUsed(&quot;trainingLocation2&quot;, dataMap, usedAttributes, seamanPermit::setTrainingLocation2);</span>
<span class="nc" id="L1631">            setFieldAndMarkUsed(&quot;periodOfValidity1&quot;, dataMap, usedAttributes, seamanPermit::setPeriodOfValidity1);</span>
<span class="nc" id="L1632">            setFieldAndMarkUsed(&quot;periodOfValidity2&quot;, dataMap, usedAttributes, seamanPermit::setPeriodOfValidity2);</span>
<span class="nc" id="L1633">            setFieldAndMarkUsed(&quot;periodOfValidity3&quot;, dataMap, usedAttributes, seamanPermit::setPeriodOfValidity3);</span>
<span class="nc" id="L1634">            setFieldAndMarkUsed(&quot;periodOfValidity4&quot;, dataMap, usedAttributes, seamanPermit::setPeriodOfValidity4);</span>
<span class="nc" id="L1635">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, seamanPermit::setIssuingAuthority1);</span>
<span class="nc" id="L1636">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, seamanPermit::setIssuingAuthority2);</span>
<span class="nc" id="L1637">            setFieldAndMarkUsed(&quot;dateofIssue1&quot;, dataMap, usedAttributes, seamanPermit::setDateofIssue1);</span>
<span class="nc" id="L1638">            setFieldAndMarkUsed(&quot;dateofIssue2&quot;, dataMap, usedAttributes, seamanPermit::setDateofIssue2);</span>
<span class="nc" id="L1639">            setFieldAndMarkUsed(&quot;remarks&quot;, dataMap, usedAttributes, seamanPermit::setRemarks);</span>

<span class="nc" id="L1641">            permitList.add(seamanPermit); // 添加到列表</span>

            // 3. 处理培训项目子表信息
<span class="nc" id="L1644">            List&lt;DwdbCtfCertificateDetailSeamanPermitItem&gt; itemList = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L1645" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) {</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1648">                String number = dataMap.get(&quot;number&quot; + i);</span>
<span class="nc" id="L1649">                String atrainingProgram = dataMap.get(&quot;atrainingProgram&quot; + i);</span>
<span class="nc" id="L1650">                String trainingScale = dataMap.get(&quot;trainingScale&quot; + i);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1653" title="All 4 branches missed.">                if (StringUtils.isBlank(number) &amp;&amp; StringUtils.isBlank(atrainingProgram) &amp;&amp;</span>
<span class="nc bnc" id="L1654" title="All 2 branches missed.">                        StringUtils.isBlank(trainingScale)) {</span>
<span class="nc" id="L1655">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L1658">                DwdbCtfCertificateDetailSeamanPermitItem item = new DwdbCtfCertificateDetailSeamanPermitItem();</span>
<span class="nc" id="L1659">                item.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L1660">                item.setSeamanPermitItemId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1661">                item.setSeamanPermitId(seamanPermitId);</span>
<span class="nc" id="L1662">                item.setCreateTime(new Date());</span>
<span class="nc" id="L1663">                item.setUpdateTime(new Date());</span>

                // 设置培训项目信息，同时记录已使用的属性
<span class="nc" id="L1666">                setFieldAndMarkUsed(&quot;number&quot; + i, dataMap, usedAttributes, item::setNumber);</span>
<span class="nc" id="L1667">                setFieldAndMarkUsed(&quot;atrainingProgram&quot; + i, dataMap, usedAttributes, item::setAtrainingProgram);</span>
<span class="nc" id="L1668">                setFieldAndMarkUsed(&quot;trainingScale&quot; + i, dataMap, usedAttributes, item::setTrainingScale);</span>

<span class="nc" id="L1670">                itemList.add(item);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L1674">            result.put(&quot;seamanInfo&quot;, seamanInfo);</span>
<span class="nc" id="L1675">            result.put(&quot;seamanPermit&quot;, permitList);</span>
<span class="nc" id="L1676">            result.put(&quot;itemList&quot;, itemList);</span>

<span class="nc" id="L1678">        } catch (Exception e) {</span>
<span class="nc" id="L1679">            log.error(&quot;解析海船船员培训许可证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1680">            throw new RuntimeException(&quot;解析海船船员培训许可证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L1681">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1682">        }</span>

<span class="nc" id="L1684">        return result;</span>
    }

    /**
     * 处理特定航线江海直达船舶船员行驶资格证明培训合格证的数据转换
     */
    private DwdbCtfCertificateDetailTdhxjh convertTdhxjhCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1695">        DwdbCtfCertificateDetailTdhxjh target = new DwdbCtfCertificateDetailTdhxjh();</span>

        try {
<span class="nc" id="L1698">            target.setTdhxjhId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1699">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L1700">            target.setCreateTime(new Date());</span>
<span class="nc" id="L1701">            target.setUpdateTime(new Date());</span>

            // 设置JSON中的字段，同时记录已使用的属性
<span class="nc" id="L1704">            setFieldAndMarkUsed(&quot;numberOfCertificate&quot;, dataMap, usedAttributes, target::setNumberOfCertificate);</span>
<span class="nc" id="L1705">            setFieldAndMarkUsed(&quot;name&quot;, dataMap, usedAttributes, target::setName);</span>
<span class="nc" id="L1706">            setFieldAndMarkUsed(&quot;dateOfBirth&quot;, dataMap, usedAttributes, target::setDateOfBirth);</span>
<span class="nc" id="L1707">            setFieldAndMarkUsed(&quot;creditCode&quot;, dataMap, usedAttributes, target::setCreditCode);</span>
<span class="nc" id="L1708">            setFieldAndMarkUsed(&quot;gender&quot;, dataMap, usedAttributes, target::setGender);</span>
<span class="nc" id="L1709">            setFieldAndMarkUsed(&quot;dateOfIssue&quot;, dataMap, usedAttributes, target::setDateOfIssue);</span>
<span class="nc" id="L1710">            setFieldAndMarkUsed(&quot;expiryDate&quot;, dataMap, usedAttributes, target::setExpiryDate);</span>
<span class="nc" id="L1711">            setFieldAndMarkUsed(&quot;applivations&quot;, dataMap, usedAttributes, target::setApplivations);</span>
<span class="nc" id="L1712">            setFieldAndMarkUsed(&quot;limitationsApplying&quot;, dataMap, usedAttributes, target::setLimitationsApplying);</span>
<span class="nc" id="L1713">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, target::setPhoto);</span>
<span class="nc" id="L1714">            setFieldAndMarkUsed(&quot;issuingAuthority&quot;, dataMap, usedAttributes, target::setIssuingAuthority);</span>
<span class="nc" id="L1715">            setFieldAndMarkUsed(&quot;issuingDate&quot;, dataMap, usedAttributes, target::setIssuingDate);</span>

<span class="nc" id="L1717">        } catch (Exception e) {</span>
<span class="nc" id="L1718">            log.error(&quot;解析特定航线江海直达船舶船员行驶资格证明培训合格证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1719">            throw new RuntimeException(&quot;解析特定航线江海直达船舶船员行驶资格证明培训合格证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot;</span>
<span class="nc" id="L1720">                    + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1721">        }</span>

<span class="nc" id="L1723">        return target;</span>
    }

    /**
     * 处理小型海船适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertXhcsrzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1734">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1735">        String xhcsrzId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L1739">            DwdbCtfCertificateDetailXhcsrz mainData = new DwdbCtfCertificateDetailXhcsrz();</span>
<span class="nc" id="L1740">            mainData.setXhcsrzId(xhcsrzId);</span>
<span class="nc" id="L1741">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L1742">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L1743">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L1746">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L1747">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L1748">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L1749">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L1750">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L1751">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L1752">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L1753">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L1754">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L1755">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L1757">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L1759">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L1760">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L1761">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L1762">            setFieldAndMarkUsed(&quot;articleNumber1&quot;, dataMap, usedAttributes, mainData::setArticleNumber1);</span>
<span class="nc" id="L1763">            setFieldAndMarkUsed(&quot;articleNumber2&quot;, dataMap, usedAttributes, mainData::setArticleNumber2);</span>
<span class="nc" id="L1764">            setFieldAndMarkUsed(&quot;articleNumber3&quot;, dataMap, usedAttributes, mainData::setArticleNumber3);</span>
<span class="nc" id="L1765">            setFieldAndMarkUsed(&quot;articleNumber4&quot;, dataMap, usedAttributes, mainData::setArticleNumber4);</span>
<span class="nc" id="L1766">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L1767">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L1768">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L1769">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L1770">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>
<span class="nc" id="L1771">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L1773">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息表数据
<span class="nc" id="L1777">            List&lt;DwdbCtfCertificateDetailXhcsrzCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L1780" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L1781">                String capacityPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L1782">                String capacityPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1785">                String gradwAndCapacity1 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix1);</span>
<span class="nc" id="L1786">                String gradwAndCapacity2 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix2);</span>
<span class="nc" id="L1787">                String alimitationsApplying1 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix1);</span>
<span class="nc" id="L1788">                String alimitationsApplying2 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1791" title="All 4 branches missed.">                if (StringUtils.isBlank(gradwAndCapacity1) &amp;&amp; StringUtils.isBlank(gradwAndCapacity2) &amp;&amp;</span>
<span class="nc bnc" id="L1792" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L1793">                    continue;</span>
                }

<span class="nc" id="L1796">                DwdbCtfCertificateDetailXhcsrzCapacity capacity = new DwdbCtfCertificateDetailXhcsrzCapacity();</span>
<span class="nc" id="L1797">                capacity.setXhcsrzCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1798">                capacity.setXhcsrzId(xhcsrzId);</span>
<span class="nc" id="L1799">                capacity.setDataId(source.getDataid());</span>
<span class="nc" id="L1800">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L1801">                capacity.setUpdateTime(new Date());</span>

                // 设置职务等级信息
<span class="nc" id="L1804">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity1);
<span class="nc" id="L1806">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity2);
<span class="nc" id="L1808">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L1810">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L1813">                capacityList.add(capacity);</span>
            }

            // 3. 处理职能信息表数据
<span class="nc" id="L1817">            List&lt;DwdbCtfCertificateDetailXhcsrzFunction&gt; functionList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L1820" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L1821">                String functionPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L1822">                String functionPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1825">                String function1 = dataMap.get(&quot;function&quot; + functionPrefix1);</span>
<span class="nc" id="L1826">                String function2 = dataMap.get(&quot;function&quot; + functionPrefix2);</span>
<span class="nc" id="L1827">                String level1 = dataMap.get(&quot;level&quot; + functionPrefix1);</span>
<span class="nc" id="L1828">                String level2 = dataMap.get(&quot;level&quot; + functionPrefix2);</span>
<span class="nc" id="L1829">                String limitationsApplying1 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix1);</span>
<span class="nc" id="L1830">                String limitationsApplying2 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1833" title="All 4 branches missed.">                if (StringUtils.isBlank(function1) &amp;&amp; StringUtils.isBlank(function2) &amp;&amp;</span>
<span class="nc bnc" id="L1834" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2) &amp;&amp;</span>
<span class="nc bnc" id="L1835" title="All 4 branches missed.">                        StringUtils.isBlank(limitationsApplying1) &amp;&amp; StringUtils.isBlank(limitationsApplying2)) {</span>
<span class="nc" id="L1836">                    continue;</span>
                }

<span class="nc" id="L1839">                DwdbCtfCertificateDetailXhcsrzFunction func = new DwdbCtfCertificateDetailXhcsrzFunction();</span>
<span class="nc" id="L1840">                func.setXhcsrzFunctionId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1841">                func.setXhcsrzId(xhcsrzId);</span>
<span class="nc" id="L1842">                func.setDataId(source.getDataid());</span>
<span class="nc" id="L1843">                func.setCreateTime(new Date());</span>
<span class="nc" id="L1844">                func.setUpdateTime(new Date());</span>

                // 设置职能信息
<span class="nc" id="L1847">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix1, dataMap, usedAttributes, func::setFunction1);</span>
<span class="nc" id="L1848">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix2, dataMap, usedAttributes, func::setFunction2);</span>
<span class="nc" id="L1849">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix1, dataMap, usedAttributes, func::setLevel1);</span>
<span class="nc" id="L1850">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix2, dataMap, usedAttributes, func::setLevel2);</span>
<span class="nc" id="L1851">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix1, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying1);
<span class="nc" id="L1853">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix2, dataMap, usedAttributes,</span>
                        func::setLimitationsApplying2);

<span class="nc" id="L1856">                functionList.add(func);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L1860">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L1861">            result.put(&quot;capacityList&quot;, capacityList);</span>
<span class="nc" id="L1862">            result.put(&quot;functionList&quot;, functionList);</span>

<span class="nc" id="L1864">        } catch (Exception e) {</span>
<span class="nc" id="L1865">            log.error(&quot;解析小型海船适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1866">            throw new RuntimeException(&quot;解析小型海船适任证书数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L1867">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1868">        }</span>

<span class="nc" id="L1870">        return result;</span>
    }

    /**
     * 处理引航员船员适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertYhysrzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1881">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1882">        String yhysrzId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L1886">            DwdbCtfCertificateDetailYhysrz mainData = new DwdbCtfCertificateDetailYhysrz();</span>
<span class="nc" id="L1887">            mainData.setYhysrzId(yhysrzId);</span>
<span class="nc" id="L1888">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L1889">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L1890">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L1893">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L1894">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L1895">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L1896">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L1897">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L1898">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L1899">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L1900">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L1901">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L1902">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L1904">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L1906">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L1907">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L1908">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L1909">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L1910">            setFieldAndMarkUsed(&quot;remarks&quot;, dataMap, usedAttributes, mainData::setRemarks);</span>
<span class="nc" id="L1911">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L1912">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L1913">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L1915">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理引航范围表数据
<span class="nc" id="L1919">            List&lt;DwdbCtfCertificateDetailYhysrzRange&gt; rangeList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L1922" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L1923">                String prefix1 = String.valueOf(i);</span>
<span class="nc" id="L1924">                String prefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L1927">                String type1 = dataMap.get(&quot;type&quot; + prefix1);</span>
<span class="nc" id="L1928">                String type2 = dataMap.get(&quot;type&quot; + prefix2);</span>
<span class="nc" id="L1929">                String level1 = dataMap.get(&quot;level&quot; + prefix1);</span>
<span class="nc" id="L1930">                String level2 = dataMap.get(&quot;level&quot; + prefix2);</span>
<span class="nc" id="L1931">                String pilotageArea1 = dataMap.get(&quot;pilotageArea&quot; + prefix1);</span>
<span class="nc" id="L1932">                String pilotageArea2 = dataMap.get(&quot;pilotageArea&quot; + prefix2);</span>
<span class="nc" id="L1933">                String limitationOfPolotage1 = dataMap.get(&quot;limitationOfPolotage&quot; + prefix1);</span>
<span class="nc" id="L1934">                String limitationOfPolotage2 = dataMap.get(&quot;limitationOfPolotage&quot; + prefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L1937" title="All 4 branches missed.">                if (StringUtils.isBlank(type1) &amp;&amp; StringUtils.isBlank(type2) &amp;&amp;</span>
<span class="nc bnc" id="L1938" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2) &amp;&amp;</span>
<span class="nc bnc" id="L1939" title="All 4 branches missed.">                        StringUtils.isBlank(pilotageArea1) &amp;&amp; StringUtils.isBlank(pilotageArea2) &amp;&amp;</span>
<span class="nc bnc" id="L1940" title="All 4 branches missed.">                        StringUtils.isBlank(limitationOfPolotage1) &amp;&amp; StringUtils.isBlank(limitationOfPolotage2)) {</span>
<span class="nc" id="L1941">                    continue;</span>
                }

<span class="nc" id="L1944">                DwdbCtfCertificateDetailYhysrzRange range = new DwdbCtfCertificateDetailYhysrzRange();</span>
<span class="nc" id="L1945">                range.setYhysrzRangeId(UUID.randomUUID().toString());</span>
<span class="nc" id="L1946">                range.setYhysrzId(yhysrzId);</span>
<span class="nc" id="L1947">                range.setDataId(source.getDataid());</span>
<span class="nc" id="L1948">                range.setCreateTime(new Date());</span>
<span class="nc" id="L1949">                range.setUpdateTime(new Date());</span>

                // 设置引航范围信息
<span class="nc" id="L1952">                setFieldAndMarkUsed(&quot;type&quot; + prefix1, dataMap, usedAttributes, range::setType1);</span>
<span class="nc" id="L1953">                setFieldAndMarkUsed(&quot;type&quot; + prefix2, dataMap, usedAttributes, range::setType2);</span>
<span class="nc" id="L1954">                setFieldAndMarkUsed(&quot;level&quot; + prefix1, dataMap, usedAttributes, range::setLevel1);</span>
<span class="nc" id="L1955">                setFieldAndMarkUsed(&quot;level&quot; + prefix2, dataMap, usedAttributes, range::setLevel2);</span>
<span class="nc" id="L1956">                setFieldAndMarkUsed(&quot;pilotageArea&quot; + prefix1, dataMap, usedAttributes, range::setPilotageArea1);</span>
<span class="nc" id="L1957">                setFieldAndMarkUsed(&quot;pilotageArea&quot; + prefix2, dataMap, usedAttributes, range::setPilotageArea2);</span>
<span class="nc" id="L1958">                setFieldAndMarkUsed(&quot;limitationOfPolotage&quot; + prefix1, dataMap, usedAttributes,</span>
                        range::setLimitationOfPolotage1);
<span class="nc" id="L1960">                setFieldAndMarkUsed(&quot;limitationOfPolotage&quot; + prefix2, dataMap, usedAttributes,</span>
                        range::setLimitationOfPolotage2);

<span class="nc" id="L1963">                rangeList.add(range);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L1967">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L1968">            result.put(&quot;rangeList&quot;, rangeList);</span>

<span class="nc" id="L1970">        } catch (Exception e) {</span>
<span class="nc" id="L1971">            log.error(&quot;解析引航员船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L1972">            throw new RuntimeException(&quot;解析引航员船员适任证书数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L1973">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L1974">        }</span>

<span class="nc" id="L1976">        return result;</span>
    }

    /**
     * 处理海船船员培训合格证书的数据转换
     */
    private Map&lt;String, Object&gt; convertHcpxhgCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L1987">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L1988">        String hcpxhgId = UUID.randomUUID().toString();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L1992">            DwdbCtfCertificateDetailHcpxhg mainData = new DwdbCtfCertificateDetailHcpxhg();</span>
<span class="nc" id="L1993">            mainData.setHcpxhgId(hcpxhgId);</span>
<span class="nc" id="L1994">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L1995">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L1996">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段，同时记录已使用的属性
<span class="nc" id="L1999">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2000">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2001">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2002">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2003">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2004">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2005">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2006">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2007">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2008">            setFieldAndMarkUsed(&quot;issuedOn1&quot;, dataMap, usedAttributes, mainData::setIssuedOn1);</span>
<span class="nc" id="L2009">            setFieldAndMarkUsed(&quot;issuedOn2&quot;, dataMap, usedAttributes, mainData::setIssuedOn2);</span>
<span class="nc" id="L2010">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L2011">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2012">            setFieldAndMarkUsed(&quot;signatureOfDulyAutOffi1&quot;, dataMap, usedAttributes,</span>
                    mainData::setSignatureOfDulyAutOffi1);
<span class="nc" id="L2014">            setFieldAndMarkUsed(&quot;signatureOfDulyAutOffi2&quot;, dataMap, usedAttributes,</span>
                    mainData::setSignatureOfDulyAutOffi2);
<span class="nc" id="L2016">            setFieldAndMarkUsed(&quot;nameOfDulyAutOffi1&quot;, dataMap, usedAttributes, mainData::setNameOfDulyAutOffi1);</span>
<span class="nc" id="L2017">            setFieldAndMarkUsed(&quot;nameOfDulyAutOffi2&quot;, dataMap, usedAttributes, mainData::setNameOfDulyAutOffi2);</span>
<span class="nc" id="L2018">            setFieldAndMarkUsed(&quot;officalSeal1&quot;, dataMap, usedAttributes, mainData::setOfficalSeal1);</span>
<span class="nc" id="L2019">            setFieldAndMarkUsed(&quot;officalSeal2&quot;, dataMap, usedAttributes, mainData::setOfficalSeal2);</span>
<span class="nc" id="L2020">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L2021">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>

            // 2. 处理培训项目信息表数据
<span class="nc" id="L2024">            List&lt;DwdbCtfCertificateDetailHcpxhgTraining&gt; trainingList = new ArrayList&lt;&gt;();</span>

            // 遍历可能的记录，按照2个一组处理
<span class="nc bnc" id="L2027" title="All 2 branches missed.">            for (int i = 1; i &lt;= 31; i++) {</span>
<span class="nc" id="L2028">                String recordIndex = String.valueOf(i);</span>
<span class="nc" id="L2029">                int titleIndex = (i * 2) - 1; // 计算title索引: 1,3,5,7...</span>
<span class="nc" id="L2030">                int dateIndex = (i * 2) - 1; // 计算date索引: 1,3,5,7...</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L2033">                String prefix = dataMap.get(&quot;prefix&quot; + recordIndex);</span>
<span class="nc" id="L2034">                String titleOfTheCertificate1 = dataMap.get(&quot;titleOfTheCertificate&quot; + titleIndex);</span>
<span class="nc" id="L2035">                String titleOfTheCertificate2 = dataMap.get(&quot;titleOfTheCertificate&quot; + (titleIndex + 1));</span>
<span class="nc" id="L2036">                String level = dataMap.get(&quot;level&quot; + recordIndex);</span>
<span class="nc" id="L2037">                String dateOfIssue1 = dataMap.get(&quot;dateOfIssue&quot; + dateIndex);</span>
<span class="nc" id="L2038">                String dateOfIssue2 = dataMap.get(&quot;dateOfIssue&quot; + (dateIndex + 1));</span>
<span class="nc" id="L2039">                String dateOfExpiry1 = dataMap.get(&quot;dateOfExpiry&quot; + dateIndex);</span>
<span class="nc" id="L2040">                String dateOfExpiry2 = dataMap.get(&quot;dateOfExpiry&quot; + (dateIndex + 1));</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2043" title="All 4 branches missed.">                if (StringUtils.isBlank(prefix) &amp;&amp; StringUtils.isBlank(titleOfTheCertificate1) &amp;&amp;</span>
<span class="nc bnc" id="L2044" title="All 4 branches missed.">                        StringUtils.isBlank(titleOfTheCertificate2) &amp;&amp; StringUtils.isBlank(level) &amp;&amp;</span>
<span class="nc bnc" id="L2045" title="All 4 branches missed.">                        StringUtils.isBlank(dateOfIssue1) &amp;&amp; StringUtils.isBlank(dateOfIssue2) &amp;&amp;</span>
<span class="nc bnc" id="L2046" title="All 4 branches missed.">                        StringUtils.isBlank(dateOfExpiry1) &amp;&amp; StringUtils.isBlank(dateOfExpiry2)) {</span>
<span class="nc" id="L2047">                    continue; // 跳过空记录</span>
                }

<span class="nc" id="L2050">                DwdbCtfCertificateDetailHcpxhgTraining training = new DwdbCtfCertificateDetailHcpxhgTraining();</span>
<span class="nc" id="L2051">                training.setDataId(source.getDataid()); // 设置源数据ID</span>
<span class="nc" id="L2052">                training.setHcpxhgTrainingId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2053">                training.setHcpxhgId(hcpxhgId);</span>
<span class="nc" id="L2054">                training.setCreateTime(new Date());</span>
<span class="nc" id="L2055">                training.setUpdateTime(new Date());</span>

                // 设置培训项目信息，同时记录已使用的属性
                // prefix1是第一条记录、prefix2是第二条记录...
<span class="nc" id="L2059">                setFieldAndMarkUsed(&quot;prefix&quot; + recordIndex, dataMap, usedAttributes, training::setPrefix);</span>

                // titleOfTheCertificate1是培训项目名称(中文)、titleOfTheCertificate2是培训项目名称(英文)
<span class="nc" id="L2062">                setFieldAndMarkUsed(&quot;titleOftheCertificate&quot; + titleIndex, dataMap, usedAttributes,</span>
                        training::setTitleOfTheCertificate1);
<span class="nc" id="L2064">                setFieldAndMarkUsed(&quot;titleOftheCertificate&quot; + (titleIndex + 1), dataMap, usedAttributes,</span>
                        training::setTitleOfTheCertificate2);

                // level1是第一条记录、level2是第二条记录...
<span class="nc" id="L2068">                setFieldAndMarkUsed(&quot;level&quot; + recordIndex, dataMap, usedAttributes, training::setLevel);</span>

                // dateOfIssue1、dateOfIssue2是第一条记录的数据
<span class="nc" id="L2071">                setFieldAndMarkUsed(&quot;dateOfIssue&quot; + dateIndex, dataMap, usedAttributes, training::setDateOfIssue1);</span>
<span class="nc" id="L2072">                setFieldAndMarkUsed(&quot;dateOfIssue&quot; + (dateIndex + 1), dataMap, usedAttributes,</span>
                        training::setDateOfIssue2);

                // dateOfExpiry1、dateOfExpiry2是第一条记录的数据
<span class="nc" id="L2076">                setFieldAndMarkUsed(&quot;dateOfExpiry&quot; + dateIndex, dataMap, usedAttributes, training::setDateOfExpiry1);</span>
<span class="nc" id="L2077">                setFieldAndMarkUsed(&quot;dateOfExpiry&quot; + (dateIndex + 1), dataMap, usedAttributes,</span>
                        training::setDateOfExpiry2);

<span class="nc" id="L2080">                trainingList.add(training);</span>
            }

            // 将所有数据放入结果Map
<span class="nc" id="L2084">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L2085">            result.put(&quot;trainingList&quot;, trainingList);</span>

<span class="nc" id="L2087">        } catch (Exception e) {</span>
<span class="nc" id="L2088">            log.error(&quot;解析海船船员培训合格证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2089">            throw new RuntimeException(</span>
<span class="nc" id="L2090">                    &quot;解析海船船员培训合格证书数据失败&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2091">        }</span>

<span class="nc" id="L2093">        return result;</span>
    }

    /**
     * 根据证照类型处理surface_data字段
     * 
     * @return 返回统一的转换结果对象，包含主表数据和子表数据
     */
    public CertificateConvertResult convertSurfaceData(OdsCertificateData source) throws Exception {
<span class="nc" id="L2102">        CertificateConvertResult result = new CertificateConvertResult();</span>
<span class="nc" id="L2103">        result.setDataId(source.getDataid());</span>
<span class="nc" id="L2104">        result.setCertificateType(source.getCatalogname());</span>

<span class="nc bnc" id="L2106" title="All 4 branches missed.">        if (source == null || StringUtils.isBlank(source.getSurfacedata())) {</span>
<span class="nc" id="L2107">            result.setHasError(true);</span>
<span class="nc" id="L2108">            result.setErrorMessage(&quot;源数据为空或surface_data为空&quot;);</span>
<span class="nc" id="L2109">            throw new RuntimeException(&quot;源数据为空或surface_data为空&quot;);</span>
        }

        try {
            // 解析JSON数组
<span class="nc" id="L2114">            JSONArray jsonArray = JSON.parseArray(source.getSurfacedata());</span>
<span class="nc" id="L2115">            Map&lt;String, String&gt; dataMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2116">            Set&lt;String&gt; usedAttributes = new HashSet&lt;&gt;(); // 记录已使用的属性</span>

            // 将JSON数据转换为Map
<span class="nc bnc" id="L2119" title="All 2 branches missed.">            for (int i = 0; i &lt; jsonArray.size(); i++) {</span>
<span class="nc" id="L2120">                JSONObject item = jsonArray.getJSONObject(i);</span>
<span class="nc" id="L2121">                String name = item.getString(&quot;name&quot;);</span>
<span class="nc" id="L2122">                String value = item.getString(&quot;value&quot;);</span>
<span class="nc" id="L2123">                dataMap.put(name, value);</span>
            }

            // 根据证书类型进行转换
<span class="nc bnc" id="L2127" title="All 27 branches missed.">            switch (source.getCatalogname()) {</span>
                case &quot;不参加航行和轮机值班海船船员适任证书&quot;:
<span class="nc" id="L2129">                    Map&lt;String, Object&gt; bcjhljzbData = convertBcjhljzbCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2130">                    result.setMainTableData(bcjhljzbData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2131">                    result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) bcjhljzbData.get(&quot;capacityList&quot;));</span>
<span class="nc" id="L2132">                    break;</span>
                case &quot;船员适任证书申请表&quot;:
<span class="nc" id="L2134">                    Map&lt;String, Object&gt; cysrzsqbData = convertCysrzsqbCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2135">                    result.setMainTableData(cysrzsqbData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2136">                    result.setSubTableDataList(&quot;experienceList&quot;, (List&lt;?&gt;) cysrzsqbData.get(&quot;experienceList&quot;));</span>
<span class="nc" id="L2137">                    result.setSubTableDataList(&quot;optionsList&quot;, (List&lt;?&gt;) cysrzsqbData.get(&quot;optionsList&quot;));</span>
<span class="nc" id="L2138">                    break;</span>
                case &quot;公务船船员适任证书&quot;:
<span class="nc" id="L2140">                    Map&lt;String, Object&gt; gwccyData = convertGwccyCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2141">                    result.setMainTableData(gwccyData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2142">                    result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) gwccyData.get(&quot;capacityList&quot;));</span>
<span class="nc bnc" id="L2143" title="All 2 branches missed.">                    if (gwccyData.get(&quot;functionList&quot;) != null) {</span>
<span class="nc" id="L2144">                        result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) gwccyData.get(&quot;functionList&quot;));</span>
                    }
                    break;
                case &quot;海船高级船员适任证书&quot;:
<span class="nc" id="L2148">                    Map&lt;String, Object&gt; hcgjcyData = convertHcgjcyCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2149">                    result.setMainTableData(hcgjcyData.get(&quot;mainData&quot;));</span>
<span class="nc bnc" id="L2150" title="All 2 branches missed.">                    if (hcgjcyData.get(&quot;capacityList&quot;) != null) {</span>
<span class="nc" id="L2151">                        result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) hcgjcyData.get(&quot;capacityList&quot;));</span>
                    }
<span class="nc bnc" id="L2153" title="All 2 branches missed.">                    if (hcgjcyData.get(&quot;functionList&quot;) != null) {</span>
<span class="nc" id="L2154">                        result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) hcgjcyData.get(&quot;functionList&quot;));</span>
                    }
                    break;
                case &quot;海船普通船员适任证书&quot;:
<span class="nc" id="L2158">                    Map&lt;String, Object&gt; hcptcysrzData = convertHcptcysrzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2159">                    result.setMainTableData(hcptcysrzData.get(&quot;mainData&quot;));</span>
<span class="nc bnc" id="L2160" title="All 2 branches missed.">                    if (hcptcysrzData.get(&quot;capacityList&quot;) != null) {</span>
<span class="nc" id="L2161">                        result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) hcptcysrzData.get(&quot;capacityList&quot;));</span>
                    }
<span class="nc bnc" id="L2163" title="All 2 branches missed.">                    if (hcptcysrzData.get(&quot;functionList&quot;) != null) {</span>
<span class="nc" id="L2164">                        result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) hcptcysrzData.get(&quot;functionList&quot;));</span>
                    }
                    break;
                case &quot;海上非自航船舶船员适任证书&quot;:
<span class="nc" id="L2168">                    Map&lt;String, Object&gt; hsfhcysrzData = convertHsfhcysrzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2169">                    result.setMainTableData(hsfhcysrzData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2170">                    result.setSubTableDataList(&quot;shipList&quot;, (List&lt;?&gt;) hsfhcysrzData.get(&quot;shipList&quot;));</span>
<span class="nc" id="L2171">                    break;</span>
                case &quot;海员外派机构资质证书&quot;:
<span class="nc" id="L2173">                    DwdbCtfCertificateDetailHywpjg hywpjg = convertHywpjgCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2174">                    result.setMainTableData(hywpjg);</span>
<span class="nc" id="L2175">                    break;</span>
                case &quot;海船船员健康证明&quot;:
<span class="nc" id="L2177">                    DwdbCtfCertificateDetailJkzm jkzm = convertJkzmCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2178">                    result.setMainTableData(jkzm);</span>
<span class="nc" id="L2179">                    break;</span>
                case &quot;内河船舶船员适任证书&quot;:
<span class="nc" id="L2181">                    DwdbCtfCertificateDetailNhcbcy nhcbcy = convertNhcbcyCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2182">                    result.setMainTableData(nhcbcy);</span>
<span class="nc" id="L2183">                    break;</span>
                case &quot;海船船员内河航线行驶资格证明&quot;:
<span class="nc" id="L2185">                    DwdbCtfCertificateDetailNhhxxs nhhxxs = convertNhhxxsCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2186">                    result.setMainTableData(nhhxxs);</span>
<span class="nc" id="L2187">                    break;</span>
                case &quot;内河船舶船员培训合格证&quot;:
                case &quot;内河船舶船员特殊培训合格证&quot;:
<span class="nc" id="L2190">                    DwdbCtfCertificateDetailNhpxhg nhpxhg = convertNhpxhg(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2191">                    result.setMainTableData(nhpxhg);</span>
<span class="nc" id="L2192">                    result.setSubTableDataList(&quot;nhpxhgItems&quot;, nhpxhg.getItems()); // 添加子表数据</span>
<span class="nc" id="L2193">                    break;</span>
                case &quot;船员培训质量管理体系证书&quot;:
<span class="nc" id="L2195">                    DwdbCtfCertificateDetailQms qms = convertQmsCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2196">                    result.setMainTableData(qms);</span>
<span class="nc" id="L2197">                    break;</span>
                case &quot;海船船员培训许可证&quot;:
<span class="nc" id="L2199">                    Map&lt;String, Object&gt; seamanPermitData = convertSeamanPermitCertificate(source, dataMap,</span>
                            usedAttributes);
<span class="nc" id="L2201">                    result.setMainTableData(seamanPermitData.get(&quot;seamanInfo&quot;));</span>
<span class="nc" id="L2202">                    result.setSubTableDataList(&quot;seamanPermit&quot;, seamanPermitData.get(&quot;seamanPermit&quot;));</span>
<span class="nc" id="L2203">                    result.setSubTableDataList(&quot;itemList&quot;, seamanPermitData.get(&quot;itemList&quot;));</span>
<span class="nc" id="L2204">                    break;</span>
                case &quot;特定航线江海直达船舶船员行驶资格证明培训合格证&quot;:
<span class="nc" id="L2206">                    DwdbCtfCertificateDetailTdhxjh tdhxjh = convertTdhxjhCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2207">                    result.setMainTableData(tdhxjh);</span>
<span class="nc" id="L2208">                    break;</span>
                case &quot;小型海船适任证书&quot;:
<span class="nc" id="L2210">                    Map&lt;String, Object&gt; xhcsrzData = convertXhcsrzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2211">                    result.setMainTableData(xhcsrzData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2212">                    result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) xhcsrzData.get(&quot;capacityList&quot;));</span>
<span class="nc" id="L2213">                    result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) xhcsrzData.get(&quot;functionList&quot;));</span>
<span class="nc" id="L2214">                    break;</span>
                case &quot;引航员船员适任证书&quot;:
<span class="nc" id="L2216">                    Map&lt;String, Object&gt; yhysrzData = convertYhysrzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2217">                    result.setMainTableData(yhysrzData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2218">                    result.setSubTableDataList(&quot;rangeList&quot;, (List&lt;?&gt;) yhysrzData.get(&quot;rangeList&quot;));</span>
<span class="nc" id="L2219">                    break;</span>
                case &quot;海船船员培训合格证书&quot;:
<span class="nc" id="L2221">                    Map&lt;String, Object&gt; hcpxhgData = convertHcpxhgCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2222">                    result.setMainTableData(hcpxhgData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2223">                    result.setSubTableDataList(&quot;trainingList&quot;, hcpxhgData.get(&quot;trainingList&quot;));</span>
<span class="nc" id="L2224">                    break;</span>
                case &quot;游艇驾驶证（海上）&quot;:
                case &quot;游艇驾驶证（内河）&quot;:
                case &quot;游艇驾驶证内河&quot;:
                case &quot;游艇驾驶证海上&quot;:
                case &quot;游艇驾驶证&quot;:
<span class="nc" id="L2230">                    Map&lt;String, Object&gt; ytjszData = convertYtjszCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2231">                    result.setMainTableData(ytjszData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2232">                    break;</span>
                case &quot;船上厨师培训合格证明&quot;:
<span class="nc" id="L2234">                    Map&lt;String, Object&gt; cscspxData = convertCscspxCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2235">                    result.setMainTableData(cscspxData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2236">                    break;</span>
                case &quot;船上膳食服务辅助人员培训证明&quot;:
<span class="nc" id="L2238">                    Map&lt;String, Object&gt; csssfzpxData = convertCsssfzpxCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2239">                    result.setMainTableData(csssfzpxData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2240">                    break;</span>
                case &quot;海船船员适任证书承认签证&quot;:
<span class="nc" id="L2242">                    Map&lt;String, Object&gt; hccycrData = convertHccycrCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2243">                    result.setMainTableData(hccycrData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2244">                    result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) hccycrData.get(&quot;capacityList&quot;));</span>
<span class="nc" id="L2245">                    result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) hccycrData.get(&quot;functionList&quot;));</span>
<span class="nc" id="L2246">                    break;</span>
                case &quot;海上设施工作人员海上交通安全技能培训合格证明&quot;:
<span class="nc" id="L2248">                    Map&lt;String, Object&gt; hsssjnData = convertHsssjnCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2249">                    result.setMainTableData(hsssjnData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2250">                    break;</span>
                case &quot;海船不参加船员适任证书&quot;:
<span class="nc" id="L2252">                    Map&lt;String, Object&gt; hcbcjcyData = convertHcbcjcyCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2253">                    result.setMainTableData(hcbcjcyData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2254">                    result.setSubTableDataList(&quot;capacityList&quot;, (List&lt;?&gt;) hcbcjcyData.get(&quot;capacityList&quot;));</span>
<span class="nc" id="L2255">                    break;</span>
                case &quot;内河船员培训许可证&quot;: // 添加内河船员培训许可证的处理分支
<span class="nc" id="L2257">                    Map&lt;String, Object&gt; nhcyxkzData = convertNhcyxkzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2258">                    result.setMainTableData(nhcyxkzData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2259">                    result.setSubTableDataList(&quot;itemList&quot;, (List&lt;?&gt;) nhcyxkzData.get(&quot;itemList&quot;));</span>
<span class="nc" id="L2260">                    break;</span>
                case &quot;海船船员特免证明&quot;:
<span class="nc" id="L2262">                    Map&lt;String, Object&gt; hccytmData = convertHccytmCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2263">                    result.setMainTableData(hccytmData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2264">                    result.setSubTableDataList(&quot;capacities&quot;, (List&lt;?&gt;) hccytmData.get(&quot;capacityList&quot;));</span>
<span class="nc" id="L2265">                    result.setSubTableDataList(&quot;functions&quot;, (List&lt;?&gt;) hccytmData.get(&quot;functionList&quot;));</span>
<span class="nc" id="L2266">                    break;</span>
                case &quot;海船船员培训合格证承认签证&quot;:
<span class="nc" id="L2268">                    Map&lt;String, Object&gt; hcpxhgqzData = convertHcpxhgqzCertificate(source, dataMap, usedAttributes);</span>
<span class="nc" id="L2269">                    result.setMainTableData(hcpxhgqzData.get(&quot;mainData&quot;));</span>
<span class="nc" id="L2270">                    result.setSubTableDataList(&quot;trainingList&quot;, (List&lt;?&gt;) hcpxhgqzData.get(&quot;trainingList&quot;));</span>
<span class="nc" id="L2271">                    break;</span>
                default:
                    // log.warn(&quot;未知的证照目录类型：{}&quot;, source.getCatalogname());
                    // result.setHasError(true);
                    // result.setErrorMessage(&quot;未知的证照目录类型：&quot; + source.getCatalogname());
                    // 移除异常抛出，让程序继续执行
                    // throw new Exception(&quot;未知的证照目录类型：&quot; + source.getCatalogname());
<span class="nc" id="L2278">                    return result;</span>
            }

            // 处理未使用的属性
<span class="nc" id="L2282">            List&lt;DwdbCertificateDataAttribute&gt; unusedAttributes = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L2283" title="All 2 branches missed.">            for (Map.Entry&lt;String, String&gt; entry : dataMap.entrySet()) {</span>
<span class="nc bnc" id="L2284" title="All 2 branches missed.">                if (!usedAttributes.contains(entry.getKey())) {</span>
<span class="nc" id="L2285">                    DwdbCertificateDataAttribute attribute = new DwdbCertificateDataAttribute();</span>
<span class="nc" id="L2286">                    attribute.setCertificateAttributeId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2287">                    attribute.setDataId(source.getDataid());</span>
<span class="nc" id="L2288">                    attribute.setAttributeColumnName(entry.getKey());</span>
<span class="nc" id="L2289">                    attribute.setAttributeValue(entry.getValue());</span>
<span class="nc" id="L2290">                    attribute.setRecCreateDate(new Date());</span>
<span class="nc" id="L2291">                    attribute.setRecModifyDate(new Date());</span>
<span class="nc" id="L2292">                    unusedAttributes.add(attribute);</span>
                }
<span class="nc" id="L2294">            }</span>

<span class="nc bnc" id="L2296" title="All 2 branches missed.">            if (!unusedAttributes.isEmpty()) {</span>
<span class="nc" id="L2297">                result.setUnusedAttributes(unusedAttributes);</span>
            }

<span class="nc" id="L2300">        } catch (Exception e) {</span>
<span class="nc" id="L2301">            result.setHasError(true);</span>
<span class="nc" id="L2302">            result.setErrorMessage(&quot;转换失败: &quot; + e.getMessage());</span>
<span class="nc" id="L2303">            log.error(&quot;转换失败&quot;, e);</span>
<span class="nc" id="L2304">            throw new RuntimeException(</span>
<span class="nc" id="L2305">                    &quot;证照转换过程中发生异常&quot; + &quot;\n&quot; + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2306">        }</span>

<span class="nc" id="L2308">        return result;</span>
    }

    /**
     * 处理游艇驾驶证书的数据转换
     */
    private Map&lt;String, Object&gt; convertYtjszCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2319">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2320">        DwdbCtfCertificateDetailYtjsz target = new DwdbCtfCertificateDetailYtjsz();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2324">            target.setYtjszId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2325">            target.setDataId(source.getDataid());</span>
<span class="nc" id="L2326">            target.setCreateTime(new Date());</span>
<span class="nc" id="L2327">            target.setUpdateTime(new Date());</span>

            // 设置基础字段 - 带有优先级的字段映射
<span class="nc" id="L2330">            String certificateNo = getFirstNonBlankValue(dataMap, usedAttributes, &quot;number&quot;, &quot;certificateNo&quot;);</span>
<span class="nc" id="L2331">            target.setCertificateNo(certificateNo);</span>

<span class="nc" id="L2333">            String fullNameOfTheHolder1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;name&quot;, &quot;nameCn&quot;,</span>
                    &quot;fullNameOfTheHolder1&quot;);
<span class="nc" id="L2335">            target.setFullNameOfTheHolder1(fullNameOfTheHolder1);</span>

<span class="nc" id="L2337">            String fullNameOfTheHolder2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;nameEn&quot;,</span>
                    &quot;fullNameOfTheHolder2&quot;);
<span class="nc" id="L2339">            target.setFullNameOfTheHolder2(fullNameOfTheHolder2);</span>

<span class="nc" id="L2341">            String nationality1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;nationality&quot;, &quot;countryCn&quot;,</span>
                    &quot;nationality1&quot;);
<span class="nc" id="L2343">            target.setNationality1(nationality1);</span>

<span class="nc" id="L2345">            String nationality2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;countryEn&quot;, &quot;nationality2&quot;);</span>
<span class="nc" id="L2346">            target.setNationality2(nationality2);</span>

<span class="nc" id="L2348">            String dateOfBirth1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;dateOfBirth&quot;, &quot;birthCn&quot;,</span>
                    &quot;dateOfBirth1&quot;);
<span class="nc" id="L2350">            target.setDateOfBirth1(dateOfBirth1);</span>

<span class="nc" id="L2352">            String dateOfBirth2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;birthEn&quot;, &quot;dateOfBirth2&quot;);</span>
<span class="nc" id="L2353">            target.setDateOfBirth2(dateOfBirth2);</span>

<span class="nc" id="L2355">            String gender1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;sex&quot;, &quot;sexCn&quot;, &quot;gender1&quot;);</span>
<span class="nc" id="L2356">            target.setGender1(gender1);</span>

<span class="nc" id="L2358">            String gender2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;sexEn&quot;, &quot;gender2&quot;);</span>
<span class="nc" id="L2359">            target.setGender2(gender2);</span>

<span class="nc" id="L2361">            String dateOfExpiry1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;dateOfExpiry&quot;, &quot;expiryDateCn&quot;,</span>
                    &quot;dateOfExpiry1&quot;);
<span class="nc" id="L2363">            target.setDateOfExpiry1(dateOfExpiry1);</span>

<span class="nc" id="L2365">            String dateOfExpiry2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;expiryDateEn&quot;, &quot;dateOfExpiry2&quot;);</span>
<span class="nc" id="L2366">            target.setDateOfExpiry2(dateOfExpiry2);</span>

<span class="nc" id="L2368">            String issuedOn1 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;initialDate&quot;, &quot;initialDateCn&quot;,</span>
                    &quot;issuedOn1&quot;);
<span class="nc" id="L2370">            target.setIssuedOn1(issuedOn1);</span>

<span class="nc" id="L2372">            String issuedOn2 = getFirstNonBlankValue(dataMap, usedAttributes, &quot;initialDateEn&quot;, &quot;issuedOn2&quot;);</span>
<span class="nc" id="L2373">            target.setIssuedOn2(issuedOn2);</span>

<span class="nc" id="L2375">            String initialDateCn = getFirstNonBlankValue(dataMap, usedAttributes, &quot;issuedOn1&quot;, &quot;initialDateCn&quot;,</span>
                    &quot;initialDate&quot;);
<span class="nc" id="L2377">            target.setInitialDateCn(initialDateCn);</span>

<span class="nc" id="L2379">            String officeOfIssueCn = getFirstNonBlankValue(dataMap, usedAttributes, &quot;issuingAdministration1&quot;,</span>
                    &quot;officeOfIssueCn&quot;, &quot;officeOfissue&quot;);
<span class="nc" id="L2381">            target.setOfficeOfIssueCn(officeOfIssueCn);</span>

<span class="nc" id="L2383">            String officeOfIssueEn = getFirstNonBlankValue(dataMap, usedAttributes, &quot;issuingAdministration2&quot;,</span>
                    &quot;officeOfIssueEn&quot;);
<span class="nc" id="L2385">            target.setOfficeOfIssueEn(officeOfIssueEn);</span>

            // 设置其他基础字段
<span class="nc" id="L2388">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, target::setInformationOfPhoto);</span>
<span class="nc" id="L2389">            setFieldAndMarkUsed(&quot;fileNoCn&quot;, dataMap, usedAttributes, target::setFileNoCn);</span>
<span class="nc" id="L2390">            setFieldAndMarkUsed(&quot;fileNoEn&quot;, dataMap, usedAttributes, target::setFileNoEn);</span>
<span class="nc" id="L2391">            setFieldAndMarkUsed(&quot;qualificationCn&quot;, dataMap, usedAttributes, target::setQualificationCn);</span>
<span class="nc" id="L2392">            setFieldAndMarkUsed(&quot;qualificationEn&quot;, dataMap, usedAttributes, target::setQualificationEn);</span>
<span class="nc" id="L2393">            setFieldAndMarkUsed(&quot;initialDateEn&quot;, dataMap, usedAttributes, target::setInitialDateEn);</span>
<span class="nc" id="L2394">            setFieldAndMarkUsed(&quot;signDeptCn&quot;, dataMap, usedAttributes, target::setSignDeptCn);</span>
<span class="nc" id="L2395">            setFieldAndMarkUsed(&quot;signDeptEn&quot;, dataMap, usedAttributes, target::setSignDeptEn);</span>
<span class="nc" id="L2396">            setFieldAndMarkUsed(&quot;date&quot;, dataMap, usedAttributes, target::setDate);</span>
<span class="nc" id="L2397">            setFieldAndMarkUsed(&quot;year&quot;, dataMap, usedAttributes, target::setYear);</span>
<span class="nc" id="L2398">            setFieldAndMarkUsed(&quot;month&quot;, dataMap, usedAttributes, target::setMonth);</span>
<span class="nc" id="L2399">            setFieldAndMarkUsed(&quot;day&quot;, dataMap, usedAttributes, target::setDay);</span>

<span class="nc" id="L2401">            result.put(&quot;mainData&quot;, target);</span>

<span class="nc" id="L2403">        } catch (Exception e) {</span>
<span class="nc" id="L2404">            log.error(&quot;解析游艇驾驶证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2405">            throw new RuntimeException(&quot;解析游艇驾驶证书数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L2406">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2407">        }</span>

<span class="nc" id="L2409">        return result;</span>
    }

    /**
     * 获取多个字段中第一个非空值
     */
    private String getFirstNonBlankValue(Map&lt;String, String&gt; dataMap, Set&lt;String&gt; usedAttributes,
            String... fieldNames) {
<span class="nc" id="L2417">        String value = null;</span>
<span class="nc bnc" id="L2418" title="All 2 branches missed.">        for (String fieldName : fieldNames) {</span>
<span class="nc" id="L2419">            value = dataMap.get(fieldName);</span>
<span class="nc bnc" id="L2420" title="All 2 branches missed.">            if (StringUtils.isNotBlank(value)) {</span>
<span class="nc" id="L2421">                usedAttributes.add(fieldName);</span>
<span class="nc" id="L2422">                return value;</span>
            }
        }
        // 如果所有字段都为空，标记最后一个字段为已使用
<span class="nc bnc" id="L2426" title="All 2 branches missed.">        if (fieldNames.length &gt; 0) {</span>
<span class="nc" id="L2427">            usedAttributes.add(fieldNames[fieldNames.length - 1]);</span>
        }
<span class="nc" id="L2429">        return &quot;&quot;;</span>
    }

    /**
     * 处理船上厨师培训合格证的数据转换
     */
    private Map&lt;String, Object&gt; convertCscspxCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2440">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2444">            DwdbCtfCertificateDetailCscspx mainData = new DwdbCtfCertificateDetailCscspx();</span>
<span class="nc" id="L2445">            mainData.setCscspxId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2446">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2447">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2448">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2451">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2452">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2453">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2454">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2455">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2456">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2457">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2458">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2459">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2460">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L2461">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L2462">            setFieldAndMarkUsed(&quot;nameOfTheTraingManager1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfTheTraingManager1);
<span class="nc" id="L2464">            setFieldAndMarkUsed(&quot;nameOfTheTraingManager2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfTheTraingManager2);
<span class="nc" id="L2466">            setFieldAndMarkUsed(&quot;issuingBody1&quot;, dataMap, usedAttributes, mainData::setIssuingBody1);</span>
<span class="nc" id="L2467">            setFieldAndMarkUsed(&quot;issuingBody2&quot;, dataMap, usedAttributes, mainData::setIssuingBody2);</span>
<span class="nc" id="L2468">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2469">            setFieldAndMarkUsed(&quot;issuingBody&quot;, dataMap, usedAttributes, mainData::setIssuingBody);</span>
<span class="nc" id="L2470">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>

            // 将数据放入结果Map
<span class="nc" id="L2473">            result.put(&quot;mainData&quot;, mainData);</span>

<span class="nc" id="L2475">        } catch (Exception e) {</span>
<span class="nc" id="L2476">            log.error(&quot;解析船上厨师培训合格证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2477">            throw new RuntimeException(&quot;解析船上厨师培训合格证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L2478">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2479">        }</span>

<span class="nc" id="L2481">        return result;</span>
    }

    /**
     * 处理船上膳食服务辅助人员培训证明的数据转换
     */
    private Map&lt;String, Object&gt; convertCsssfzpxCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2492">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2496">            DwdbCtfCertificateDetailCsssfzpx mainData = new DwdbCtfCertificateDetailCsssfzpx();</span>
<span class="nc" id="L2497">            mainData.setCsssfzpxId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2498">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2499">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2500">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2503">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2504">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2505">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2506">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2507">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2508">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2509">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2510">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2511">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2512">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L2513">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L2514">            setFieldAndMarkUsed(&quot;nameOfTheTraingManager1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfTheTraingManager1);
<span class="nc" id="L2516">            setFieldAndMarkUsed(&quot;nameOfTheTraingManager2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfTheTraingManager2);
<span class="nc" id="L2518">            setFieldAndMarkUsed(&quot;issuingBody1&quot;, dataMap, usedAttributes, mainData::setIssuingBody1);</span>
<span class="nc" id="L2519">            setFieldAndMarkUsed(&quot;issuingBody2&quot;, dataMap, usedAttributes, mainData::setIssuingBody2);</span>
<span class="nc" id="L2520">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2521">            setFieldAndMarkUsed(&quot;issuingBody&quot;, dataMap, usedAttributes, mainData::setIssuingBody);</span>
<span class="nc" id="L2522">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>

            // 将数据放入结果Map
<span class="nc" id="L2525">            result.put(&quot;mainData&quot;, mainData);</span>

<span class="nc" id="L2527">        } catch (Exception e) {</span>
<span class="nc" id="L2528">            log.error(&quot;解析船上膳食服务辅助人员培训证明数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2529">            throw new RuntimeException(&quot;解析船上膳食服务辅助人员培训证明数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage()</span>
<span class="nc" id="L2530">                    + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2531">        }</span>

<span class="nc" id="L2533">        return result;</span>
    }

    /**
     * 处理海船船员适任证书承认签证的数据转换
     */
    private Map&lt;String, Object&gt; convertHccycrCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2544">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2545">        List&lt;DwdbCtfCertificateDetailHccycrCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L2546">        List&lt;DwdbCtfCertificateDetailHccycrFunction&gt; functionList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2550">            DwdbCtfCertificateDetailHccycr mainData = new DwdbCtfCertificateDetailHccycr();</span>
<span class="nc" id="L2551">            String hccycrId = UUID.randomUUID().toString();</span>
<span class="nc" id="L2552">            mainData.setHccycrId(hccycrId);</span>
<span class="nc" id="L2553">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2554">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2555">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2558">            setFieldAndMarkUsed(&quot;fullNameOfTheHolder1&quot;, dataMap, usedAttributes, mainData::setHolderName1);</span>
<span class="nc" id="L2559">            setFieldAndMarkUsed(&quot;fullNameOfTheHolder2&quot;, dataMap, usedAttributes, mainData::setHolderName2);</span>
<span class="nc" id="L2560">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2561">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2562">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2563">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2564">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2565">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2566">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2567">            setFieldAndMarkUsed(&quot;dateOfExpiry1&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry1);</span>
<span class="nc" id="L2568">            setFieldAndMarkUsed(&quot;dateOfExpiry2&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry2);</span>
<span class="nc" id="L2569">            setFieldAndMarkUsed(&quot;issuedOn1&quot;, dataMap, usedAttributes, mainData::setIssuedOn1);</span>
<span class="nc" id="L2570">            setFieldAndMarkUsed(&quot;issuedOn2&quot;, dataMap, usedAttributes, mainData::setIssuedOn2);</span>
<span class="nc" id="L2571">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L2572">            setFieldAndMarkUsed(&quot;articleNumber1&quot;, dataMap, usedAttributes, mainData::setArticleNumber1);</span>
<span class="nc" id="L2573">            setFieldAndMarkUsed(&quot;articleNumber2&quot;, dataMap, usedAttributes, mainData::setArticleNumber2);</span>
<span class="nc" id="L2574">            setFieldAndMarkUsed(&quot;articleNumber3&quot;, dataMap, usedAttributes, mainData::setArticleNumber3);</span>
<span class="nc" id="L2575">            setFieldAndMarkUsed(&quot;articleNumber4&quot;, dataMap, usedAttributes, mainData::setArticleNumber4);</span>
<span class="nc" id="L2576">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2577">            setFieldAndMarkUsed(&quot;issuingAminstration1&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration1);</span>
<span class="nc" id="L2578">            setFieldAndMarkUsed(&quot;issuingAminstration2&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration2);</span>
<span class="nc" id="L2579">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L2580">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>
<span class="nc" id="L2581">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L2583">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息
<span class="nc bnc" id="L2587" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L2588">                String capacityPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L2589">                String capacityPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L2592">                String capacity1 = dataMap.get(&quot;capacity&quot; + capacityPrefix1);</span>
<span class="nc" id="L2593">                String capacity2 = dataMap.get(&quot;capacity&quot; + capacityPrefix2);</span>
<span class="nc" id="L2594">                String alimitationsApplying1 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix1);</span>
<span class="nc" id="L2595">                String alimitationsApplying2 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2598" title="All 4 branches missed.">                if (StringUtils.isBlank(capacity1) &amp;&amp; StringUtils.isBlank(capacity2) &amp;&amp;</span>
<span class="nc bnc" id="L2599" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L2600">                    continue;</span>
                }

<span class="nc" id="L2603">                DwdbCtfCertificateDetailHccycrCapacity capacity = new DwdbCtfCertificateDetailHccycrCapacity();</span>
<span class="nc" id="L2604">                capacity.setHccycrCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2605">                capacity.setHccycrId(hccycrId);</span>
<span class="nc" id="L2606">                capacity.setDataId(source.getDataid());</span>
<span class="nc" id="L2607">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L2608">                capacity.setUpdateTime(new Date());</span>

<span class="nc" id="L2610">                setFieldAndMarkUsed(&quot;capacity&quot; + capacityPrefix1, dataMap, usedAttributes, capacity::setCapacity1);</span>
<span class="nc" id="L2611">                setFieldAndMarkUsed(&quot;capacity&quot; + capacityPrefix2, dataMap, usedAttributes, capacity::setCapacity2);</span>
<span class="nc" id="L2612">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L2614">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L2617">                capacityList.add(capacity);</span>
            }

            // 3. 处理职能信息
<span class="nc bnc" id="L2621" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L2622">                String functionPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L2623">                String functionPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L2626">                String function1 = dataMap.get(&quot;function&quot; + functionPrefix1);</span>
<span class="nc" id="L2627">                String function2 = dataMap.get(&quot;function&quot; + functionPrefix2);</span>
<span class="nc" id="L2628">                String level1 = dataMap.get(&quot;level&quot; + functionPrefix1);</span>
<span class="nc" id="L2629">                String level2 = dataMap.get(&quot;level&quot; + functionPrefix2);</span>
<span class="nc" id="L2630">                String limitationsApplying1 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix1);</span>
<span class="nc" id="L2631">                String limitationsApplying2 = dataMap.get(&quot;limitationsApplying&quot; + functionPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2634" title="All 4 branches missed.">                if (StringUtils.isBlank(function1) &amp;&amp; StringUtils.isBlank(function2) &amp;&amp;</span>
<span class="nc bnc" id="L2635" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2) &amp;&amp;</span>
<span class="nc bnc" id="L2636" title="All 4 branches missed.">                        StringUtils.isBlank(limitationsApplying1) &amp;&amp; StringUtils.isBlank(limitationsApplying2)) {</span>
<span class="nc" id="L2637">                    continue;</span>
                }

<span class="nc" id="L2640">                DwdbCtfCertificateDetailHccycrFunction function = new DwdbCtfCertificateDetailHccycrFunction();</span>
<span class="nc" id="L2641">                function.setHccycrFunctionId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2642">                function.setHccycrId(hccycrId);</span>
<span class="nc" id="L2643">                function.setDataId(source.getDataid());</span>
<span class="nc" id="L2644">                function.setCreateTime(new Date());</span>
<span class="nc" id="L2645">                function.setUpdateTime(new Date());</span>

<span class="nc" id="L2647">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix1, dataMap, usedAttributes, function::setFunction1);</span>
<span class="nc" id="L2648">                setFieldAndMarkUsed(&quot;function&quot; + functionPrefix2, dataMap, usedAttributes, function::setFunction2);</span>
<span class="nc" id="L2649">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix1, dataMap, usedAttributes, function::setLevel1);</span>
<span class="nc" id="L2650">                setFieldAndMarkUsed(&quot;level&quot; + functionPrefix2, dataMap, usedAttributes, function::setLevel2);</span>
<span class="nc" id="L2651">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix1, dataMap, usedAttributes,</span>
                        function::setLimitationsApplying1);
<span class="nc" id="L2653">                setFieldAndMarkUsed(&quot;limitationsApplying&quot; + functionPrefix2, dataMap, usedAttributes,</span>
                        function::setLimitationsApplying2);

<span class="nc" id="L2656">                functionList.add(function);</span>
            }

            // 将数据放入结果Map
<span class="nc" id="L2660">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L2661">            result.put(&quot;capacityList&quot;, capacityList);</span>
<span class="nc" id="L2662">            result.put(&quot;functionList&quot;, functionList);</span>

<span class="nc" id="L2664">        } catch (Exception e) {</span>
<span class="nc" id="L2665">            log.error(&quot;解析海船船员适任证书承认签证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2666">            throw new RuntimeException(&quot;解析海船船员适任证书承认签证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L2667">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2668">        }</span>

<span class="nc" id="L2670">        return result;</span>
    }

    /**
     * 处理海上设施工作人员海上交通安全技能培训合格证明的数据转换
     */
    private Map&lt;String, Object&gt; convertHsssjnCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2681">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>

        try {
<span class="nc" id="L2684">            DwdbCtfCertificateDetailHsssjn mainData = new DwdbCtfCertificateDetailHsssjn();</span>
<span class="nc" id="L2685">            mainData.setHsssjnId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2686">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2687">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2688">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2691">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2692">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2693">            setFieldAndMarkUsed(&quot;EditBox1&quot;, dataMap, usedAttributes, mainData::setEditBox1);</span>
<span class="nc" id="L2694">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2695">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2696">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2697">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2698">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2699">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2700">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2701">            setFieldAndMarkUsed(&quot;anThorityName1&quot;, dataMap, usedAttributes, mainData::setAnThorityName1);</span>
<span class="nc" id="L2702">            setFieldAndMarkUsed(&quot;anThorityName2&quot;, dataMap, usedAttributes, mainData::setAnThorityName2);</span>
<span class="nc" id="L2703">            setFieldAndMarkUsed(&quot;evaluationOrganization&quot;, dataMap, usedAttributes,</span>
                    mainData::setEvaluationOrganization1);
<span class="nc" id="L2705">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2706">            setFieldAndMarkUsed(&quot;year1&quot;, dataMap, usedAttributes, mainData::setYear1);</span>
<span class="nc" id="L2707">            setFieldAndMarkUsed(&quot;month1&quot;, dataMap, usedAttributes, mainData::setMonth1);</span>
<span class="nc" id="L2708">            setFieldAndMarkUsed(&quot;day1&quot;, dataMap, usedAttributes, mainData::setDay1);</span>
<span class="nc" id="L2709">            setFieldAndMarkUsed(&quot;year2&quot;, dataMap, usedAttributes, mainData::setYear2);</span>
<span class="nc" id="L2710">            setFieldAndMarkUsed(&quot;month2&quot;, dataMap, usedAttributes, mainData::setMonth2);</span>
<span class="nc" id="L2711">            setFieldAndMarkUsed(&quot;day2&quot;, dataMap, usedAttributes, mainData::setDay2);</span>
<span class="nc" id="L2712">            setFieldAndMarkUsed(&quot;IDNumber&quot;, dataMap, usedAttributes, mainData::setIDNumber);</span>
<span class="nc" id="L2713">            setFieldAndMarkUsed(&quot;passportNumber&quot;, dataMap, usedAttributes, mainData::setPassportNumber);</span>

<span class="nc" id="L2715">            result.put(&quot;mainData&quot;, mainData);</span>

<span class="nc" id="L2717">        } catch (Exception e) {</span>
<span class="nc" id="L2718">            log.error(&quot;解析海上设施工作人员海上交通安全技能培训合格证明数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2719">            throw new RuntimeException(&quot;解析海上设施工作人员海上交通安全技能培训合格证明数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot;</span>
<span class="nc" id="L2720">                    + e.getMessage() + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2721">        }</span>

<span class="nc" id="L2723">        return result;</span>
    }

    /**
     * 处理海船不参加船员适任证书的数据转换
     */
    private Map&lt;String, Object&gt; convertHcbcjcyCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2734">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2735">        List&lt;DwdbCtfCertificateDetailHcbcjcyCapacity&gt; capacityList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2739">            DwdbCtfCertificateDetailHcbcjcy mainData = new DwdbCtfCertificateDetailHcbcjcy();</span>
<span class="nc" id="L2740">            String hcbcjcyId = UUID.randomUUID().toString();</span>
<span class="nc" id="L2741">            mainData.setHcbcjcyId(hcbcjcyId);</span>
<span class="nc" id="L2742">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2743">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2744">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2747">            setFieldAndMarkUsed(&quot;fullNameoftheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2748">            setFieldAndMarkUsed(&quot;fullNameoftheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2749">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2750">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2751">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2752">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2753">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2754">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2755">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2756">            setFieldAndMarkUsed(&quot;certificateExpiringDate1&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate1);
<span class="nc" id="L2758">            setFieldAndMarkUsed(&quot;certificateExpiringDate2&quot;, dataMap, usedAttributes,</span>
                    mainData::setCertificateExpiringDate2);
<span class="nc" id="L2760">            setFieldAndMarkUsed(&quot;dateOfIssue1&quot;, dataMap, usedAttributes, mainData::setDateOfIssue1);</span>
<span class="nc" id="L2761">            setFieldAndMarkUsed(&quot;dateOfIssue2&quot;, dataMap, usedAttributes, mainData::setDateOfIssue2);</span>
<span class="nc" id="L2762">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setInformationOfPhoto);</span>
<span class="nc" id="L2763">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L2764">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L2765">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L2766">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>
<span class="nc" id="L2767">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L2769">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息
<span class="nc bnc" id="L2773" title="All 2 branches missed.">            for (int i = 1; i &lt;= 62; i += 2) {</span>
<span class="nc" id="L2774">                String capacityPrefix1 = String.valueOf(i);</span>
<span class="nc" id="L2775">                String capacityPrefix2 = String.valueOf(i + 1);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L2778">                String gradwAndCapacity1 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix1);</span>
<span class="nc" id="L2779">                String gradwAndCapacity2 = dataMap.get(&quot;gradwAndCapacity&quot; + capacityPrefix2);</span>
<span class="nc" id="L2780">                String alimitationsApplying1 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix1);</span>
<span class="nc" id="L2781">                String alimitationsApplying2 = dataMap.get(&quot;alimitationsApplying&quot; + capacityPrefix2);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2784" title="All 4 branches missed.">                if (StringUtils.isBlank(gradwAndCapacity1) &amp;&amp; StringUtils.isBlank(gradwAndCapacity2) &amp;&amp;</span>
<span class="nc bnc" id="L2785" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L2786">                    continue;</span>
                }

<span class="nc" id="L2789">                DwdbCtfCertificateDetailHcbcjcyCapacity capacity = new DwdbCtfCertificateDetailHcbcjcyCapacity();</span>
<span class="nc" id="L2790">                capacity.setHcbcjcyCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2791">                capacity.setHcbcjcyId(hcbcjcyId);</span>
<span class="nc" id="L2792">                capacity.setDataId(source.getDataid());</span>
<span class="nc" id="L2793">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L2794">                capacity.setUpdateTime(new Date());</span>

<span class="nc" id="L2796">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity1);
<span class="nc" id="L2798">                setFieldAndMarkUsed(&quot;gradwAndCapacity&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setGradwAndCapacity2);
<span class="nc" id="L2800">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix1, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying1);
<span class="nc" id="L2802">                setFieldAndMarkUsed(&quot;alimitationsApplying&quot; + capacityPrefix2, dataMap, usedAttributes,</span>
                        capacity::setAlimitationsApplying2);

<span class="nc" id="L2805">                capacityList.add(capacity);</span>
            }

            // 将数据放入结果Map
<span class="nc" id="L2809">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L2810">            result.put(&quot;capacityList&quot;, capacityList);</span>

<span class="nc" id="L2812">        } catch (Exception e) {</span>
<span class="nc" id="L2813">            log.error(&quot;解析海船不参加船员适任证书数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2814">            throw new RuntimeException(&quot;解析海船不参加船员适任证书数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L2815">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2816">        }</span>

<span class="nc" id="L2818">        return result;</span>
    }

    /**
     * 处理内河船员培训许可证的数据转换
     */
    private Map&lt;String, Object&gt; convertNhcyxkzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2829">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2830">        List&lt;DwdbCtfCertificateDetailNhcyxkzItem&gt; itemList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2834">            DwdbCtfCertificateDetailNhcyxkz mainData = new DwdbCtfCertificateDetailNhcyxkz();</span>
<span class="nc" id="L2835">            String nhcyxkzId = UUID.randomUUID().toString();</span>
<span class="nc" id="L2836">            mainData.setNhcyxkzId(nhcyxkzId);</span>
<span class="nc" id="L2837">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2838">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2839">            mainData.setUpdateTime(new Date());</span>

            // 设置基础字段
<span class="nc" id="L2842">            setFieldAndMarkUsed(&quot;permitNumber1&quot;, dataMap, usedAttributes, mainData::setPermitNumber1);</span>
<span class="nc" id="L2843">            setFieldAndMarkUsed(&quot;anThorityName1&quot;, dataMap, usedAttributes, mainData::setAnThorityName1);</span>
<span class="nc" id="L2844">            setFieldAndMarkUsed(&quot;trainingInstitutionCode1&quot;, dataMap, usedAttributes,</span>
                    mainData::setTrainingInstitutionCode1);
<span class="nc" id="L2846">            setFieldAndMarkUsed(&quot;representative1&quot;, dataMap, usedAttributes, mainData::setRepresentative1);</span>
<span class="nc" id="L2847">            setFieldAndMarkUsed(&quot;trainingProgram1&quot;, dataMap, usedAttributes, mainData::setTrainingProgram1);</span>
<span class="nc" id="L2848">            setFieldAndMarkUsed(&quot;trainingProgram2&quot;, dataMap, usedAttributes, mainData::setTrainingProgram2);</span>
<span class="nc" id="L2849">            setFieldAndMarkUsed(&quot;registeredAddress1&quot;, dataMap, usedAttributes, mainData::setRegisteredAddress1);</span>
<span class="nc" id="L2850">            setFieldAndMarkUsed(&quot;trainingLocation1&quot;, dataMap, usedAttributes, mainData::setTrainingLocation1);</span>
<span class="nc" id="L2851">            setFieldAndMarkUsed(&quot;periodOfValidity1&quot;, dataMap, usedAttributes, mainData::setPeriodOfValidity1);</span>
<span class="nc" id="L2852">            setFieldAndMarkUsed(&quot;periodOfValidity2&quot;, dataMap, usedAttributes, mainData::setPeriodOfValidity2);</span>
<span class="nc" id="L2853">            setFieldAndMarkUsed(&quot;issuingAuthority1&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority1);</span>
<span class="nc" id="L2854">            setFieldAndMarkUsed(&quot;dateofIssue1&quot;, dataMap, usedAttributes, mainData::setDateofIssue1);</span>
<span class="nc" id="L2855">            setFieldAndMarkUsed(&quot;permitNumber2&quot;, dataMap, usedAttributes, mainData::setPermitNumber2);</span>
<span class="nc" id="L2856">            setFieldAndMarkUsed(&quot;anThorityName2&quot;, dataMap, usedAttributes, mainData::setAnThorityName2);</span>
<span class="nc" id="L2857">            setFieldAndMarkUsed(&quot;registeredAddress2&quot;, dataMap, usedAttributes, mainData::setRegisteredAddress2);</span>
<span class="nc" id="L2858">            setFieldAndMarkUsed(&quot;representative2&quot;, dataMap, usedAttributes, mainData::setRepresentative2);</span>
<span class="nc" id="L2859">            setFieldAndMarkUsed(&quot;trainingLocation2&quot;, dataMap, usedAttributes, mainData::setTrainingLocation2);</span>
<span class="nc" id="L2860">            setFieldAndMarkUsed(&quot;periodOfValidity3&quot;, dataMap, usedAttributes, mainData::setPeriodOfValidity3);</span>
<span class="nc" id="L2861">            setFieldAndMarkUsed(&quot;periodOfValidity4&quot;, dataMap, usedAttributes, mainData::setPeriodOfValidity4);</span>
<span class="nc" id="L2862">            setFieldAndMarkUsed(&quot;remarks&quot;, dataMap, usedAttributes, mainData::setRemarks);</span>
<span class="nc" id="L2863">            setFieldAndMarkUsed(&quot;issuingAuthority2&quot;, dataMap, usedAttributes, mainData::setIssuingAuthority2);</span>
<span class="nc" id="L2864">            setFieldAndMarkUsed(&quot;dateofIssue2&quot;, dataMap, usedAttributes, mainData::setDateofIssue2);</span>

            // 2. 处理培训项目子表数据
<span class="nc bnc" id="L2867" title="All 2 branches missed.">            for (int i = 1; i &lt;= 20; i++) {</span>
<span class="nc" id="L2868">                String numberPrefix = String.valueOf(i);</span>

                // 先获取所有字段值，用于检查是否全部为空
<span class="nc" id="L2871">                String number = dataMap.get(&quot;number&quot; + numberPrefix);</span>
<span class="nc" id="L2872">                String atrainingProgram = dataMap.get(&quot;atrainingProgram&quot; + numberPrefix);</span>
<span class="nc" id="L2873">                String trainingScale = dataMap.get(&quot;trainingScale&quot; + numberPrefix);</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2876" title="All 4 branches missed.">                if (StringUtils.isBlank(number) &amp;&amp; StringUtils.isBlank(atrainingProgram) &amp;&amp;</span>
<span class="nc bnc" id="L2877" title="All 2 branches missed.">                        StringUtils.isBlank(trainingScale)) {</span>
<span class="nc" id="L2878">                    continue;</span>
                }

<span class="nc" id="L2881">                DwdbCtfCertificateDetailNhcyxkzItem item = new DwdbCtfCertificateDetailNhcyxkzItem();</span>
<span class="nc" id="L2882">                item.setNhcyxkzItemId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2883">                item.setNhcyxkzId(nhcyxkzId);</span>
<span class="nc" id="L2884">                item.setCreateTime(new Date());</span>
<span class="nc" id="L2885">                item.setUpdateTime(new Date());</span>

<span class="nc" id="L2887">                setFieldAndMarkUsed(&quot;number&quot; + numberPrefix, dataMap, usedAttributes, item::setNumber);</span>
<span class="nc" id="L2888">                setFieldAndMarkUsed(&quot;atrainingProgram&quot; + numberPrefix, dataMap, usedAttributes,</span>
                        item::setAtrainingProgram);
<span class="nc" id="L2890">                setFieldAndMarkUsed(&quot;trainingScale&quot; + numberPrefix, dataMap, usedAttributes, item::setTrainingScale);</span>

<span class="nc" id="L2892">                itemList.add(item);</span>
            }

            // 将数据放入结果Map
<span class="nc" id="L2896">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L2897">            result.put(&quot;itemList&quot;, itemList);</span>

<span class="nc" id="L2899">        } catch (Exception e) {</span>
<span class="nc" id="L2900">            log.error(&quot;解析内河船员培训许可证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L2901">            throw new RuntimeException(&quot;解析内河船员培训许可证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L2902">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L2903">        }</span>

<span class="nc" id="L2905">        return result;</span>
    }

    /**
     * 处理海船船员特免证明的数据转换
     */
    private Map&lt;String, Object&gt; convertHccytmCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L2916">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L2917">        List&lt;DwdbCtfCertDetailHccytmCap&gt; capacityList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L2918">        List&lt;DwdbCtfCertDetailHccytmFunc&gt; functionList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L2922">            DwdbCtfCertDetailHccytm mainData = new DwdbCtfCertDetailHccytm();</span>
<span class="nc" id="L2923">            String hccytmId = UUID.randomUUID().toString();</span>
<span class="nc" id="L2924">            mainData.setHccytmId(hccytmId);</span>
<span class="nc" id="L2925">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L2926">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L2927">            mainData.setUpdateTime(new Date());</span>

            // 根据表字段名直接从JSON中获取值
<span class="nc" id="L2930">            setFieldAndMarkUsed(&quot;holderName1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L2931">            setFieldAndMarkUsed(&quot;holderName2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L2932">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L2933">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L2934">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L2935">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L2936">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L2937">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L2938">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L2939">            setFieldAndMarkUsed(&quot;dateOfExpiry1&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry1);</span>
<span class="nc" id="L2940">            setFieldAndMarkUsed(&quot;dateOfExpiry2&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry2);</span>
<span class="nc" id="L2941">            setFieldAndMarkUsed(&quot;issuedOn1&quot;, dataMap, usedAttributes, mainData::setIssuedOn1);</span>
<span class="nc" id="L2942">            setFieldAndMarkUsed(&quot;issuedOn2&quot;, dataMap, usedAttributes, mainData::setIssuedOn2);</span>
<span class="nc" id="L2943">            setFieldAndMarkUsed(&quot;certificateHolderName&quot;, dataMap, usedAttributes, mainData::setCertificateHolderName);</span>
<span class="nc" id="L2944">            setFieldAndMarkUsed(&quot;articleNumber1&quot;, dataMap, usedAttributes, mainData::setArticleNumber1);</span>
<span class="nc" id="L2945">            setFieldAndMarkUsed(&quot;articleNumber2&quot;, dataMap, usedAttributes, mainData::setArticleNumber2);</span>
<span class="nc" id="L2946">            setFieldAndMarkUsed(&quot;articleNumber3&quot;, dataMap, usedAttributes, mainData::setArticleNumber3);</span>
<span class="nc" id="L2947">            setFieldAndMarkUsed(&quot;articleNumber4&quot;, dataMap, usedAttributes, mainData::setArticleNumber4);</span>
<span class="nc" id="L2948">            setFieldAndMarkUsed(&quot;informationOfPhoto&quot;, dataMap, usedAttributes, mainData::setPhoto);</span>
<span class="nc" id="L2949">            setFieldAndMarkUsed(&quot;issuingAminstration1&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration1);</span>
<span class="nc" id="L2950">            setFieldAndMarkUsed(&quot;issuingAminstration2&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration2);</span>
<span class="nc" id="L2951">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L2952">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>
<span class="nc" id="L2953">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L2955">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);

            // 2. 处理职务等级信息表数据
<span class="nc bnc" id="L2959" title="All 2 branches missed.">            for (int i = 1; i &lt;= 50; i += 2) { // 假设最多50组数据</span>
<span class="nc" id="L2960">                String capacity1 = dataMap.get(&quot;capacity&quot; + i);</span>
<span class="nc" id="L2961">                String capacity2 = dataMap.get(&quot;capacity&quot; + (i + 1));</span>
<span class="nc" id="L2962">                String alimitationsApplying1 = dataMap.get(&quot;alimitations_applying&quot; + i);</span>
<span class="nc" id="L2963">                String alimitationsApplying2 = dataMap.get(&quot;alimitations_applying&quot; + (i + 1));</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L2966" title="All 4 branches missed.">                if (StringUtils.isBlank(capacity1) &amp;&amp; StringUtils.isBlank(capacity2) &amp;&amp;</span>
<span class="nc bnc" id="L2967" title="All 4 branches missed.">                        StringUtils.isBlank(alimitationsApplying1) &amp;&amp; StringUtils.isBlank(alimitationsApplying2)) {</span>
<span class="nc" id="L2968">                    continue;</span>
                }

<span class="nc" id="L2971">                DwdbCtfCertDetailHccytmCap capacity = new DwdbCtfCertDetailHccytmCap();</span>
<span class="nc" id="L2972">                capacity.setHccytmCapacityId(UUID.randomUUID().toString());</span>
<span class="nc" id="L2973">                capacity.setHccytmId(hccytmId);</span>
<span class="nc" id="L2974">                capacity.setCreateTime(new Date());</span>
<span class="nc" id="L2975">                capacity.setUpdateTime(new Date());</span>

                // 设置职务等级字段
<span class="nc bnc" id="L2978" title="All 2 branches missed.">                capacity.setCapacity1(StringUtils.isNotBlank(capacity1) ? capacity1 : &quot;&quot;);</span>
<span class="nc bnc" id="L2979" title="All 2 branches missed.">                capacity.setCapacity2(StringUtils.isNotBlank(capacity2) ? capacity2 : &quot;&quot;);</span>
<span class="nc" id="L2980">                capacity.setAlimitationsApplying1(</span>
<span class="nc bnc" id="L2981" title="All 2 branches missed.">                        StringUtils.isNotBlank(alimitationsApplying1) ? alimitationsApplying1 : &quot;&quot;);</span>
<span class="nc" id="L2982">                capacity.setAlimitationsApplying2(</span>
<span class="nc bnc" id="L2983" title="All 2 branches missed.">                        StringUtils.isNotBlank(alimitationsApplying2) ? alimitationsApplying2 : &quot;&quot;);</span>

                // 标记为已使用
<span class="nc" id="L2986">                usedAttributes.add(&quot;capacity&quot; + i);</span>
<span class="nc" id="L2987">                usedAttributes.add(&quot;capacity&quot; + (i + 1));</span>
<span class="nc" id="L2988">                usedAttributes.add(&quot;alimitations_applying&quot; + i);</span>
<span class="nc" id="L2989">                usedAttributes.add(&quot;alimitations_applying&quot; + (i + 1));</span>

<span class="nc" id="L2991">                capacityList.add(capacity);</span>
            }

            // 3. 处理职能信息表数据
<span class="nc bnc" id="L2995" title="All 2 branches missed.">            for (int i = 1; i &lt;= 50; i += 2) { // 假设最多50组数据</span>
<span class="nc" id="L2996">                String function1 = dataMap.get(&quot;function&quot; + i);</span>
<span class="nc" id="L2997">                String function2 = dataMap.get(&quot;function&quot; + (i + 1));</span>
<span class="nc" id="L2998">                String level1 = dataMap.get(&quot;level&quot; + i);</span>
<span class="nc" id="L2999">                String level2 = dataMap.get(&quot;level&quot; + (i + 1));</span>
<span class="nc" id="L3000">                String limitationsApplying1 = dataMap.get(&quot;limitations_applying&quot; + i);</span>
<span class="nc" id="L3001">                String limitationsApplying2 = dataMap.get(&quot;limitations_applying&quot; + (i + 1));</span>

                // 检查是否所有字段都为空，如果是则跳过此记录
<span class="nc bnc" id="L3004" title="All 4 branches missed.">                if (StringUtils.isBlank(function1) &amp;&amp; StringUtils.isBlank(function2) &amp;&amp;</span>
<span class="nc bnc" id="L3005" title="All 4 branches missed.">                        StringUtils.isBlank(level1) &amp;&amp; StringUtils.isBlank(level2) &amp;&amp;</span>
<span class="nc bnc" id="L3006" title="All 4 branches missed.">                        StringUtils.isBlank(limitationsApplying1) &amp;&amp; StringUtils.isBlank(limitationsApplying2)) {</span>
<span class="nc" id="L3007">                    continue;</span>
                }

<span class="nc" id="L3010">                DwdbCtfCertDetailHccytmFunc function = new DwdbCtfCertDetailHccytmFunc();</span>
<span class="nc" id="L3011">                function.setHccytmFunctionId(UUID.randomUUID().toString());</span>
<span class="nc" id="L3012">                function.setHccytmId(hccytmId);</span>
<span class="nc" id="L3013">                function.setCreateTime(new Date());</span>
<span class="nc" id="L3014">                function.setUpdateTime(new Date());</span>

                // 设置职能字段
<span class="nc bnc" id="L3017" title="All 2 branches missed.">                function.setFunction1(StringUtils.isNotBlank(function1) ? function1 : &quot;&quot;);</span>
<span class="nc bnc" id="L3018" title="All 2 branches missed.">                function.setFunction2(StringUtils.isNotBlank(function2) ? function2 : &quot;&quot;);</span>
<span class="nc bnc" id="L3019" title="All 2 branches missed.">                function.setLevel1(StringUtils.isNotBlank(level1) ? level1 : &quot;&quot;);</span>
<span class="nc bnc" id="L3020" title="All 2 branches missed.">                function.setLevel2(StringUtils.isNotBlank(level2) ? level2 : &quot;&quot;);</span>
<span class="nc" id="L3021">                function.setLimitationsApplying1(</span>
<span class="nc bnc" id="L3022" title="All 2 branches missed.">                        StringUtils.isNotBlank(limitationsApplying1) ? limitationsApplying1 : &quot;&quot;);</span>
<span class="nc" id="L3023">                function.setLimitationsApplying2(</span>
<span class="nc bnc" id="L3024" title="All 2 branches missed.">                        StringUtils.isNotBlank(limitationsApplying2) ? limitationsApplying2 : &quot;&quot;);</span>

                // 标记为已使用
<span class="nc" id="L3027">                usedAttributes.add(&quot;function&quot; + i);</span>
<span class="nc" id="L3028">                usedAttributes.add(&quot;function&quot; + (i + 1));</span>
<span class="nc" id="L3029">                usedAttributes.add(&quot;level&quot; + i);</span>
<span class="nc" id="L3030">                usedAttributes.add(&quot;level&quot; + (i + 1));</span>
<span class="nc" id="L3031">                usedAttributes.add(&quot;limitations_applying&quot; + i);</span>
<span class="nc" id="L3032">                usedAttributes.add(&quot;limitations_applying&quot; + (i + 1));</span>

<span class="nc" id="L3034">                functionList.add(function);</span>
            }

            // 将数据放入结果Map
<span class="nc" id="L3038">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L3039">            result.put(&quot;capacityList&quot;, capacityList);</span>
<span class="nc" id="L3040">            result.put(&quot;functionList&quot;, functionList);</span>

<span class="nc" id="L3042">        } catch (Exception e) {</span>
<span class="nc" id="L3043">            log.error(&quot;解析海船船员特免证明数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L3044">            throw new RuntimeException(&quot;解析海船船员特免证明数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage() + &quot;\n&quot;</span>
<span class="nc" id="L3045">                    + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L3046">        }</span>

<span class="nc" id="L3048">        return result;</span>
    }

    /**
     * 处理海船船员培训合格证承认签证的数据转换
     */
    private Map&lt;String, Object&gt; convertHcpxhgqzCertificate(
            OdsCertificateData source,
            Map&lt;String, String&gt; dataMap,
            Set&lt;String&gt; usedAttributes) {

<span class="nc" id="L3059">        Map&lt;String, Object&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc" id="L3060">        List&lt;DwdbCtfCertDetailHcpxqzTrain&gt; trainingList = new ArrayList&lt;&gt;();</span>

        try {
            // 1. 处理主表数据
<span class="nc" id="L3064">            DwdbCtfCertDetailHcpxhgqz mainData = new DwdbCtfCertDetailHcpxhgqz();</span>
<span class="nc" id="L3065">            String hcpxhgqzId = UUID.randomUUID().toString();</span>
<span class="nc" id="L3066">            mainData.setHcpxhgqzId(hcpxhgqzId);</span>
<span class="nc" id="L3067">            mainData.setDataId(source.getDataid());</span>
<span class="nc" id="L3068">            mainData.setCreateTime(new Date());</span>
<span class="nc" id="L3069">            mainData.setUpdateTime(new Date());</span>

            // 根据表字段名直接从JSON中获取值
<span class="nc" id="L3072">            setFieldAndMarkUsed(&quot;fullNameOfTheHolder1&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder1);</span>
<span class="nc" id="L3073">            setFieldAndMarkUsed(&quot;fullNameOfTheHolder2&quot;, dataMap, usedAttributes, mainData::setFullNameOfTheHolder2);</span>
<span class="nc" id="L3074">            setFieldAndMarkUsed(&quot;nationality1&quot;, dataMap, usedAttributes, mainData::setNationality1);</span>
<span class="nc" id="L3075">            setFieldAndMarkUsed(&quot;nationality2&quot;, dataMap, usedAttributes, mainData::setNationality2);</span>
<span class="nc" id="L3076">            setFieldAndMarkUsed(&quot;dateOfBirth1&quot;, dataMap, usedAttributes, mainData::setDateOfBirth1);</span>
<span class="nc" id="L3077">            setFieldAndMarkUsed(&quot;dateOfBirth2&quot;, dataMap, usedAttributes, mainData::setDateOfBirth2);</span>
<span class="nc" id="L3078">            setFieldAndMarkUsed(&quot;gender1&quot;, dataMap, usedAttributes, mainData::setGender1);</span>
<span class="nc" id="L3079">            setFieldAndMarkUsed(&quot;gender2&quot;, dataMap, usedAttributes, mainData::setGender2);</span>
<span class="nc" id="L3080">            setFieldAndMarkUsed(&quot;certificateNo&quot;, dataMap, usedAttributes, mainData::setCertificateNo);</span>
<span class="nc" id="L3081">            setFieldAndMarkUsed(&quot;dateOfExpiry1&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry1);</span>
<span class="nc" id="L3082">            setFieldAndMarkUsed(&quot;dateOfExpiry2&quot;, dataMap, usedAttributes, mainData::setDateOfExpiry2);</span>
<span class="nc" id="L3083">            setFieldAndMarkUsed(&quot;issuedOn1&quot;, dataMap, usedAttributes, mainData::setIssuedOn1);</span>
<span class="nc" id="L3084">            setFieldAndMarkUsed(&quot;issuedOn2&quot;, dataMap, usedAttributes, mainData::setIssuedOn2);</span>
<span class="nc" id="L3085">            setFieldAndMarkUsed(&quot;articleNumber1&quot;, dataMap, usedAttributes, mainData::setArticleNumber1);</span>
<span class="nc" id="L3086">            setFieldAndMarkUsed(&quot;articleNumber2&quot;, dataMap, usedAttributes, mainData::setArticleNumber2);</span>
<span class="nc" id="L3087">            setFieldAndMarkUsed(&quot;articleNumber3&quot;, dataMap, usedAttributes, mainData::setArticleNumber3);</span>
<span class="nc" id="L3088">            setFieldAndMarkUsed(&quot;articleNumber4&quot;, dataMap, usedAttributes, mainData::setArticleNumber4);</span>
<span class="nc" id="L3089">            setFieldAndMarkUsed(&quot;photo&quot;, dataMap, usedAttributes, mainData::setPhoto);</span>
<span class="nc" id="L3090">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial1&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial1);
<span class="nc" id="L3092">            setFieldAndMarkUsed(&quot;nameOfDulyAuthorizedOfficial2&quot;, dataMap, usedAttributes,</span>
                    mainData::setNameOfDulyAuthorizedOfficial2);
<span class="nc" id="L3094">            setFieldAndMarkUsed(&quot;issuingAminstration1&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration1);</span>
<span class="nc" id="L3095">            setFieldAndMarkUsed(&quot;issuingAminstration2&quot;, dataMap, usedAttributes, mainData::setIssuingAminstration2);</span>
<span class="nc" id="L3096">            setFieldAndMarkUsed(&quot;officialUseOnly1&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly1);</span>
<span class="nc" id="L3097">            setFieldAndMarkUsed(&quot;officialUseOnly2&quot;, dataMap, usedAttributes, mainData::setOfficialUseOnly2);</span>

            // 2. 处理培训项目子表数据
            // title_of_the_certificate1、title_of_the_certificate2 是第一条记录
            // title_of_the_certificate3、title_of_the_certificate4 是第二条记录
            // certificate_no1、certificate_no2... 按照相同规则
            // date_of_expiry3、date_of_expiry4 是第一条记录（从3,4开始）
            // clause1 是第一条记录，clause2 是第二条记录

<span class="nc" id="L3106">            int recordIndex = 1;</span>
            while (true) {
                // 计算当前记录的索引偏移
<span class="nc" id="L3109">                int titleIndex1 = recordIndex * 2 - 1; // 1, 3, 5, 7...</span>
<span class="nc" id="L3110">                int titleIndex2 = recordIndex * 2; // 2, 4, 6, 8...</span>
<span class="nc" id="L3111">                int certIndex1 = recordIndex * 2 - 1; // 1, 3, 5, 7...</span>
<span class="nc" id="L3112">                int certIndex2 = recordIndex * 2; // 2, 4, 6, 8...</span>
<span class="nc" id="L3113">                int expiryIndex1 = recordIndex * 2 + 1; // 3, 5, 7, 9... (从3开始)</span>
<span class="nc" id="L3114">                int expiryIndex2 = recordIndex * 2 + 2; // 4, 6, 8, 10... (从4开始)</span>
<span class="nc" id="L3115">                int clauseIndex = recordIndex; // 1, 2, 3, 4...</span>

                // 获取当前记录的所有字段值
<span class="nc" id="L3118">                String titleOfTheCertificate1 = dataMap.get(&quot;titleOfTheCertificate&quot; + titleIndex1);</span>
<span class="nc" id="L3119">                String titleOfTheCertificate2 = dataMap.get(&quot;titleOfTheCertificate&quot; + titleIndex2);</span>
<span class="nc" id="L3120">                String certificateNo1 = dataMap.get(&quot;certificateNo&quot; + certIndex1);</span>
<span class="nc" id="L3121">                String certificateNo2 = dataMap.get(&quot;certificateNo&quot; + certIndex2);</span>
<span class="nc" id="L3122">                String dateOfExpiry3 = dataMap.get(&quot;dateOfExpiry&quot; + expiryIndex1);</span>
<span class="nc" id="L3123">                String dateOfExpiry4 = dataMap.get(&quot;dateOfExpiry&quot; + expiryIndex2);</span>
<span class="nc" id="L3124">                String clause = dataMap.get(&quot;clause&quot; + clauseIndex);</span>

                // 检查是否所有字段都为空，如果是则跳出循环
<span class="nc bnc" id="L3127" title="All 4 branches missed.">                if (StringUtils.isBlank(titleOfTheCertificate1) &amp;&amp; StringUtils.isBlank(titleOfTheCertificate2) &amp;&amp;</span>
<span class="nc bnc" id="L3128" title="All 4 branches missed.">                        StringUtils.isBlank(certificateNo1) &amp;&amp; StringUtils.isBlank(certificateNo2) &amp;&amp;</span>
<span class="nc bnc" id="L3129" title="All 4 branches missed.">                        StringUtils.isBlank(dateOfExpiry3) &amp;&amp; StringUtils.isBlank(dateOfExpiry4) &amp;&amp;</span>
<span class="nc bnc" id="L3130" title="All 2 branches missed.">                        StringUtils.isBlank(clause)) {</span>
<span class="nc" id="L3131">                    break;</span>
                }

<span class="nc" id="L3134">                DwdbCtfCertDetailHcpxqzTrain training = new DwdbCtfCertDetailHcpxqzTrain();</span>
<span class="nc" id="L3135">                training.setHcpxhgqzTrainingId(UUID.randomUUID().toString());</span>
<span class="nc" id="L3136">                training.setHcpxhgqzId(hcpxhgqzId);</span>
<span class="nc" id="L3137">                training.setCreateTime(new Date());</span>
<span class="nc" id="L3138">                training.setUpdateTime(new Date());</span>

                // 设置培训项目字段
<span class="nc" id="L3141">                training.setTitleOfTheCertificate1(</span>
<span class="nc bnc" id="L3142" title="All 2 branches missed.">                        StringUtils.isNotBlank(titleOfTheCertificate1) ? titleOfTheCertificate1 : &quot;&quot;);</span>
<span class="nc" id="L3143">                training.setTitleOfTheCertificate2(</span>
<span class="nc bnc" id="L3144" title="All 2 branches missed.">                        StringUtils.isNotBlank(titleOfTheCertificate2) ? titleOfTheCertificate2 : &quot;&quot;);</span>
<span class="nc bnc" id="L3145" title="All 2 branches missed.">                training.setCertificateNo1(StringUtils.isNotBlank(certificateNo1) ? certificateNo1 : &quot;&quot;);</span>
<span class="nc bnc" id="L3146" title="All 2 branches missed.">                training.setCertificateNo2(StringUtils.isNotBlank(certificateNo2) ? certificateNo2 : &quot;&quot;);</span>
<span class="nc bnc" id="L3147" title="All 2 branches missed.">                training.setDateOfExpiry3(StringUtils.isNotBlank(dateOfExpiry3) ? dateOfExpiry3 : &quot;&quot;);</span>
<span class="nc bnc" id="L3148" title="All 2 branches missed.">                training.setDateOfExpiry4(StringUtils.isNotBlank(dateOfExpiry4) ? dateOfExpiry4 : &quot;&quot;);</span>
<span class="nc bnc" id="L3149" title="All 2 branches missed.">                training.setClause(StringUtils.isNotBlank(clause) ? clause : &quot;&quot;);</span>

                // 标记为已使用
<span class="nc" id="L3152">                usedAttributes.add(&quot;titleOfTheCertificate&quot; + titleIndex1);</span>
<span class="nc" id="L3153">                usedAttributes.add(&quot;titleOfTheCertificate&quot; + titleIndex2);</span>
<span class="nc" id="L3154">                usedAttributes.add(&quot;certificateNo&quot; + certIndex1);</span>
<span class="nc" id="L3155">                usedAttributes.add(&quot;certificateNo&quot; + certIndex2);</span>
<span class="nc" id="L3156">                usedAttributes.add(&quot;dateOfExpiry&quot; + expiryIndex1);</span>
<span class="nc" id="L3157">                usedAttributes.add(&quot;dateOfExpiry&quot; + expiryIndex2);</span>
<span class="nc" id="L3158">                usedAttributes.add(&quot;clause&quot; + clauseIndex);</span>

<span class="nc" id="L3160">                trainingList.add(training);</span>
<span class="nc" id="L3161">                recordIndex++;</span>

                // 防止无限循环，最多处理50条记录
<span class="nc bnc" id="L3164" title="All 2 branches missed.">                if (recordIndex &gt; 50) {</span>
<span class="nc" id="L3165">                    break;</span>
                }
<span class="nc" id="L3167">            }</span>

            // 将数据放入结果Map
<span class="nc" id="L3170">            result.put(&quot;mainData&quot;, mainData);</span>
<span class="nc" id="L3171">            result.put(&quot;trainingList&quot;, trainingList);</span>

<span class="nc" id="L3173">        } catch (Exception e) {</span>
<span class="nc" id="L3174">            log.error(&quot;解析海船船员培训合格证承认签证数据失败，dataId: &quot; + source.getDataid(), e);</span>
<span class="nc" id="L3175">            throw new RuntimeException(&quot;解析海船船员培训合格证承认签证数据失败，dataId: &quot; + source.getDataid() + &quot;\n&quot; + e.getMessage()</span>
<span class="nc" id="L3176">                    + &quot;\n&quot; + Arrays.toString(e.getStackTrace()), e);</span>
<span class="nc" id="L3177">        }</span>

<span class="nc" id="L3179">        return result;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>