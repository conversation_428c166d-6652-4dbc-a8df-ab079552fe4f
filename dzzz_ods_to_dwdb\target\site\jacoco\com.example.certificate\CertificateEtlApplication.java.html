<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CertificateEtlApplication.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">certificate-etl</a> &gt; <a href="index.source.html" class="el_package">com.example.certificate</a> &gt; <span class="el_source">CertificateEtlApplication.java</span></div><h1>CertificateEtlApplication.java</h1><pre class="source lang-java linenums">package com.example.certificate;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import java.io.File;

@SpringBootApplication
@EnableScheduling
<span class="fc" id="L10">public class CertificateEtlApplication {</span>

    public static void main(String[] args) {
        // 确保日志目录存在
<span class="nc" id="L14">        File logDir = new File(&quot;logs&quot;);</span>
<span class="nc bnc" id="L15" title="All 2 branches missed.">        if (!logDir.exists()) {</span>
<span class="nc" id="L16">            logDir.mkdirs();</span>
        }
        
<span class="nc" id="L19">        SpringApplication.run(CertificateEtlApplication.class, args);</span>
<span class="nc" id="L20">    }</span>
} 
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.7.202105040129</span></div></body></html>