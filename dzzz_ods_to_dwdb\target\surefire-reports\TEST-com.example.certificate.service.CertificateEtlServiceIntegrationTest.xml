<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.certificate.service.CertificateEtlServiceIntegrationTest" time="39.379" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\test-classes;E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\classes;D:\environment\repository\org\springframework\boot\spring-boot-starter\2.7.5\spring-boot-starter-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot\2.7.5\spring-boot-2.7.5.jar;D:\environment\repository\org\springframework\spring-context\5.3.23\spring-context-5.3.23.jar;D:\environment\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.5\spring-boot-autoconfigure-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-logging\2.7.5\spring-boot-starter-logging-2.7.5.jar;D:\environment\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;D:\environment\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;D:\environment\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\environment\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\environment\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\environment\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\environment\repository\org\springframework\spring-core\5.3.23\spring-core-5.3.23.jar;D:\environment\repository\org\springframework\spring-jcl\5.3.23\spring-jcl-5.3.23.jar;D:\environment\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-web\2.7.5\spring-boot-starter-web-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-json\2.7.5\spring-boot-starter-json-2.7.5.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.5\spring-boot-starter-tomcat-2.7.5.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.68\tomcat-embed-core-9.0.68.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.68\tomcat-embed-el-9.0.68.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.68\tomcat-embed-websocket-9.0.68.jar;D:\environment\repository\org\springframework\spring-web\5.3.23\spring-web-5.3.23.jar;D:\environment\repository\org\springframework\spring-beans\5.3.23\spring-beans-5.3.23.jar;D:\environment\repository\org\springframework\spring-webmvc\5.3.23\spring-webmvc-5.3.23.jar;D:\environment\repository\org\springframework\spring-aop\5.3.23\spring-aop-5.3.23.jar;D:\environment\repository\org\springframework\spring-expression\5.3.23\spring-expression-5.3.23.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.5\spring-boot-starter-jdbc-2.7.5.jar;D:\environment\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\environment\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\environment\repository\org\springframework\spring-jdbc\5.3.23\spring-jdbc-5.3.23.jar;D:\environment\repository\org\springframework\spring-tx\5.3.23\spring-tx-5.3.23.jar;D:\environment\repository\com\baomidou\mybatis-plus-boot-starter\3.5.2\mybatis-plus-boot-starter-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus\3.5.2\mybatis-plus-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-extension\3.5.2\mybatis-plus-extension-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-core\3.5.2\mybatis-plus-core-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-annotation\3.5.2\mybatis-plus-annotation-3.5.2.jar;D:\environment\repository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;D:\environment\repository\org\mybatis\mybatis\3.5.10\mybatis-3.5.10.jar;D:\environment\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.6.21\kotlin-stdlib-jdk8-1.6.21.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib\1.6.21\kotlin-stdlib-1.6.21.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.6.21\kotlin-stdlib-common-1.6.21.jar;D:\environment\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.6.21\kotlin-stdlib-jdk7-1.6.21.jar;D:\environment\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;D:\environment\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;D:\environment\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;D:\environment\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;D:\environment\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-test\2.7.5\spring-boot-starter-test-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-test\2.7.5\spring-boot-test-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.5\spring-boot-test-autoconfigure-2.7.5.jar;D:\environment\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\environment\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\environment\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\environment\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\environment\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\environment\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\environment\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\environment\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\environment\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\environment\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\environment\repository\org\springframework\spring-test\5.3.23\spring-test-5.3.23.jar;D:\environment\repository\org\xmlunit\xmlunit-core\2.9.0\xmlunit-core-2.9.0.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\environment\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\environment\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\environment\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\environment\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\environment\repository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\environment\repository\net\bytebuddy\byte-buddy\1.12.18\byte-buddy-1.12.18.jar;D:\environment\repository\net\bytebuddy\byte-buddy-agent\1.12.18\byte-buddy-agent-1.12.18.jar;D:\environment\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\environment\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\environment\repository\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar;D:\environment\repository\org\springframework\spring-aspects\5.3.23\spring-aspects-5.3.23.jar;D:\environment\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\environment\Java\jdk-1.8\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire3561844282243827029\surefirebooter6633647534962066208.jar C:\Users\<USER>\AppData\Local\Temp\surefire3561844282243827029 2025-07-31T12-11-51_320-jvmRun1 surefire8313451935596434118tmp surefire_01127464918686016857tmp"/>
    <property name="surefire.test.class.path" value="E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\test-classes;E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb\target\classes;D:\environment\repository\org\springframework\boot\spring-boot-starter\2.7.5\spring-boot-starter-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot\2.7.5\spring-boot-2.7.5.jar;D:\environment\repository\org\springframework\spring-context\5.3.23\spring-context-5.3.23.jar;D:\environment\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.5\spring-boot-autoconfigure-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-logging\2.7.5\spring-boot-starter-logging-2.7.5.jar;D:\environment\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;D:\environment\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;D:\environment\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;D:\environment\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;D:\environment\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;D:\environment\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\environment\repository\org\springframework\spring-core\5.3.23\spring-core-5.3.23.jar;D:\environment\repository\org\springframework\spring-jcl\5.3.23\spring-jcl-5.3.23.jar;D:\environment\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-web\2.7.5\spring-boot-starter-web-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-json\2.7.5\spring-boot-starter-json-2.7.5.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.4\jackson-annotations-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\core\jackson-core\2.13.4\jackson-core-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.4\jackson-datatype-jdk8-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.4\jackson-datatype-jsr310-2.13.4.jar;D:\environment\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.4\jackson-module-parameter-names-2.13.4.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.5\spring-boot-starter-tomcat-2.7.5.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.68\tomcat-embed-core-9.0.68.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.68\tomcat-embed-el-9.0.68.jar;D:\environment\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.68\tomcat-embed-websocket-9.0.68.jar;D:\environment\repository\org\springframework\spring-web\5.3.23\spring-web-5.3.23.jar;D:\environment\repository\org\springframework\spring-beans\5.3.23\spring-beans-5.3.23.jar;D:\environment\repository\org\springframework\spring-webmvc\5.3.23\spring-webmvc-5.3.23.jar;D:\environment\repository\org\springframework\spring-aop\5.3.23\spring-aop-5.3.23.jar;D:\environment\repository\org\springframework\spring-expression\5.3.23\spring-expression-5.3.23.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.5\spring-boot-starter-jdbc-2.7.5.jar;D:\environment\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\environment\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\environment\repository\org\springframework\spring-jdbc\5.3.23\spring-jdbc-5.3.23.jar;D:\environment\repository\org\springframework\spring-tx\5.3.23\spring-tx-5.3.23.jar;D:\environment\repository\com\baomidou\mybatis-plus-boot-starter\3.5.2\mybatis-plus-boot-starter-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus\3.5.2\mybatis-plus-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-extension\3.5.2\mybatis-plus-extension-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-core\3.5.2\mybatis-plus-core-3.5.2.jar;D:\environment\repository\com\baomidou\mybatis-plus-annotation\3.5.2\mybatis-plus-annotation-3.5.2.jar;D:\environment\repository\com\github\jsqlparser\jsqlparser\4.4\jsqlparser-4.4.jar;D:\environment\repository\org\mybatis\mybatis\3.5.10\mybatis-3.5.10.jar;D:\environment\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.6.21\kotlin-stdlib-jdk8-1.6.21.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib\1.6.21\kotlin-stdlib-1.6.21.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.6.21\kotlin-stdlib-common-1.6.21.jar;D:\environment\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\environment\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.6.21\kotlin-stdlib-jdk7-1.6.21.jar;D:\environment\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;D:\environment\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;D:\environment\repository\org\projectlombok\lombok\1.18.24\lombok-1.18.24.jar;D:\environment\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;D:\environment\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;D:\environment\repository\org\springframework\boot\spring-boot-starter-test\2.7.5\spring-boot-starter-test-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-test\2.7.5\spring-boot-test-2.7.5.jar;D:\environment\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.5\spring-boot-test-autoconfigure-2.7.5.jar;D:\environment\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;D:\environment\repository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;D:\environment\repository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;D:\environment\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\environment\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\environment\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\environment\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;D:\environment\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\environment\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\environment\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\environment\repository\org\springframework\spring-test\5.3.23\spring-test-5.3.23.jar;D:\environment\repository\org\xmlunit\xmlunit-core\2.9.0\xmlunit-core-2.9.0.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\environment\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\environment\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\environment\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\environment\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\environment\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\environment\repository\org\mockito\mockito-core\4.5.1\mockito-core-4.5.1.jar;D:\environment\repository\net\bytebuddy\byte-buddy\1.12.18\byte-buddy-1.12.18.jar;D:\environment\repository\net\bytebuddy\byte-buddy-agent\1.12.18\byte-buddy-agent-1.12.18.jar;D:\environment\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\environment\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;D:\environment\repository\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar;D:\environment\repository\org\springframework\spring-aspects\5.3.23\spring-aspects-5.3.23.jar;D:\environment\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\environment\Java\jdk-1.8\jre"/>
    <property name="basedir" value="E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire3561844282243827029\surefirebooter6633647534962066208.jar;D:\\environment\\repository\\org\\jacoco\\org.jacoco.agent\\0.8.7\\org.jacoco.agent-0.8.7-runtime.jar"/>
    <property name="sun.boot.class.path" value="D:\environment\Java\jdk-1.8\jre\lib\resources.jar;D:\environment\Java\jdk-1.8\jre\lib\rt.jar;D:\environment\Java\jdk-1.8\jre\lib\jsse.jar;D:\environment\Java\jdk-1.8\jre\lib\jce.jar;D:\environment\Java\jdk-1.8\jre\lib\charsets.jar;D:\environment\Java\jdk-1.8\jre\lib\jfr.jar;D:\environment\Java\jdk-1.8\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_431-b10"/>
    <property name="user.name" value="deLl"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\environment\Java\jdk-1.8\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\environment\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2024.3"/>
    <property name="java.version" value="1.8.0_431"/>
    <property name="user.dir" value="E:\project\IdeaProjects\dzzz_etl\dzzz_ods_to_dwdb"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\environment\Java\jdk-1.8\jre\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;d:\soft\cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;d:\soft\Cursor\resources\app\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\WINDOWS.X64_193000_db_home\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\dotnet\;D:\environment\Java\jdk-1.8\bin;D:\environment\Java\jdk-1.8\jre_1.8\bin;D:\environment\apache-maven-3.5.3\bin;D:\soft\TortoiseGit\bin;D:\soft\mysql-5.7.24-winx64\bin;D:\soft\Tencent\微信web开发者工具\dll;D:\env;ronment\ffmpeg-master-latest-win64-gpl-shared\bin;C:\Program Files\Docker\Docker\resources\bin;D:\soft\Git\cmd;D:\environment\nodejs;D:\environment\nodejs\node_gobal;D:\environment\nvm;D:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\soft\JetBrains\IntelliJ IDEA 2024.3\bin;D:\environment\nodejs\node_gobal\node_modules;D:\environment\nvm;D:\nvm4w\nodejs;D:\soft\cursor\resources\app\bi;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\soft\JetBrains\IntelliJ IDEA 2024.3\bin;;node_gobal\node_modules;D:\soft\Microsoft VS Code\bin;D:\environment\nvm;D:\nvm4w\nodejs;D:\soft\cursor\resources\app\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.431-b10"/>
    <property name="java.specification.maintenance.version" value="6"/>
    <property name="java.ext.dirs" value="D:\environment\Java\jdk-1.8\jre\lib\ext;C:\windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="maven.repo.local" value="D:\environment\repository"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testEtlProcess" classname="com.example.certificate.service.CertificateEtlServiceIntegrationTest" time="2.408"/>
</testsuite>